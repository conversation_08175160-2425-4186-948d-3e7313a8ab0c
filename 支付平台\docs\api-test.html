<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蓝兔支付系统API测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .api-section {
            margin-bottom: 40px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .section-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e0e0e0;
            font-weight: bold;
            color: #333;
        }
        
        .api-item {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .api-item:last-child {
            border-bottom: none;
        }
        
        .api-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .api-url {
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin-bottom: 15px;
            border-left: 4px solid #007bff;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .response {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #28a745;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .response.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success {
            background: #28a745;
        }
        
        .status-error {
            background: #dc3545;
        }
        
        .status-pending {
            background: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 蓝兔支付系统</h1>
            <p>API接口测试工具</p>
        </div>
        
        <div class="content">
            <!-- 系统接口 -->
            <div class="api-section">
                <div class="section-header">🔧 系统接口</div>
                
                <div class="api-item">
                    <div class="api-title">健康检查</div>
                    <div class="api-url">GET /health</div>
                    <button class="btn" onclick="testHealthCheck()">测试</button>
                    <div id="health-response" class="response" style="display: none;"></div>
                </div>
                
                <div class="api-item">
                    <div class="api-title">API信息</div>
                    <div class="api-url">GET /api</div>
                    <button class="btn" onclick="testAPIInfo()">测试</button>
                    <div id="api-info-response" class="response" style="display: none;"></div>
                </div>
            </div>
            
            <!-- 支付接口 -->
            <div class="api-section">
                <div class="section-header">💰 支付接口</div>
                
                <div class="api-item">
                    <div class="api-title">创建支付订单</div>
                    <div class="api-url">POST /api/payment/create</div>
                    
                    <div class="form-group">
                        <label>支付金额（元）:</label>
                        <input type="number" id="amount" value="0.01" step="0.01" min="0.01">
                    </div>
                    
                    <div class="form-group">
                        <label>商品描述:</label>
                        <input type="text" id="body" value="测试商品">
                    </div>
                    
                    <div class="form-group">
                        <label>附加数据:</label>
                        <input type="text" id="attach" value="测试数据">
                    </div>
                    
                    <div class="form-group">
                        <label>过期时间:</label>
                        <input type="text" id="time_expire" value="30m" placeholder="如: 30m, 2h">
                    </div>
                    
                    <button class="btn" onclick="createPayment()">创建订单</button>
                    <div id="create-response" class="response" style="display: none;"></div>
                </div>
                
                <div class="api-item">
                    <div class="api-title">查询支付订单</div>
                    <div class="api-url">GET /api/payment/query/:out_trade_no</div>
                    
                    <div class="form-group">
                        <label>商户订单号:</label>
                        <input type="text" id="query-order-no" placeholder="请输入订单号">
                    </div>
                    
                    <button class="btn" onclick="queryPayment()">查询订单</button>
                    <div id="query-response" class="response" style="display: none;"></div>
                </div>
                
                <div class="api-item">
                    <div class="api-title">获取订单列表</div>
                    <div class="api-url">GET /api/payment/orders</div>
                    
                    <div style="display: flex; gap: 15px; margin-bottom: 15px;">
                        <div class="form-group" style="flex: 1;">
                            <label>页码:</label>
                            <input type="number" id="page" value="1" min="1">
                        </div>
                        <div class="form-group" style="flex: 1;">
                            <label>每页数量:</label>
                            <input type="number" id="pageSize" value="10" min="1" max="100">
                        </div>
                    </div>
                    
                    <button class="btn" onclick="getOrderList()">获取列表</button>
                    <div id="orders-response" class="response" style="display: none;"></div>
                </div>
                
                <div class="api-item">
                    <div class="api-title">获取支付统计</div>
                    <div class="api-url">GET /api/payment/stats</div>
                    <button class="btn" onclick="getPaymentStats()">获取统计</button>
                    <div id="stats-response" class="response" style="display: none;"></div>
                </div>
                
                <div class="api-item">
                    <div class="api-title">测试接口</div>
                    <div class="api-url">GET /api/payment/test</div>
                    <button class="btn" onclick="testPaymentAPI()">测试</button>
                    <div id="test-response" class="response" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:3000';
        
        // 通用请求函数
        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(BASE_URL + url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.text();
                let jsonData;
                
                try {
                    jsonData = JSON.parse(data);
                } catch {
                    jsonData = data;
                }
                
                return {
                    success: response.ok,
                    status: response.status,
                    data: jsonData
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }
        
        // 显示响应结果
        function showResponse(elementId, result) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            
            if (result.success) {
                element.className = 'response';
                element.textContent = JSON.stringify(result.data, null, 2);
            } else {
                element.className = 'response error';
                element.textContent = result.error || `HTTP ${result.status}: ${JSON.stringify(result.data, null, 2)}`;
            }
        }
        
        // 设置按钮加载状态
        function setButtonLoading(button, loading) {
            if (loading) {
                button.disabled = true;
                button.innerHTML = '<span class="loading"></span>请求中...';
            } else {
                button.disabled = false;
                button.innerHTML = button.getAttribute('data-original-text') || '测试';
            }
        }
        
        // 测试健康检查
        async function testHealthCheck() {
            const btn = event.target;
            btn.setAttribute('data-original-text', btn.innerHTML);
            setButtonLoading(btn, true);
            
            const result = await makeRequest('/health');
            showResponse('health-response', result);
            
            setButtonLoading(btn, false);
        }
        
        // 测试API信息
        async function testAPIInfo() {
            const btn = event.target;
            btn.setAttribute('data-original-text', btn.innerHTML);
            setButtonLoading(btn, true);
            
            const result = await makeRequest('/api');
            showResponse('api-info-response', result);
            
            setButtonLoading(btn, false);
        }
        
        // 创建支付订单
        async function createPayment() {
            const btn = event.target;
            btn.setAttribute('data-original-text', btn.innerHTML);
            setButtonLoading(btn, true);
            
            const data = {
                amount: parseFloat(document.getElementById('amount').value),
                body: document.getElementById('body').value,
                attach: document.getElementById('attach').value,
                time_expire: document.getElementById('time_expire').value
            };
            
            const result = await makeRequest('/api/payment/create', {
                method: 'POST',
                body: JSON.stringify(data)
            });
            
            showResponse('create-response', result);
            
            // 如果创建成功，自动填充查询订单号
            if (result.success && result.data.data && result.data.data.out_trade_no) {
                document.getElementById('query-order-no').value = result.data.data.out_trade_no;
            }
            
            setButtonLoading(btn, false);
        }
        
        // 查询支付订单
        async function queryPayment() {
            const btn = event.target;
            btn.setAttribute('data-original-text', btn.innerHTML);
            setButtonLoading(btn, true);
            
            const orderNo = document.getElementById('query-order-no').value;
            if (!orderNo) {
                alert('请输入订单号');
                setButtonLoading(btn, false);
                return;
            }
            
            const result = await makeRequest(`/api/payment/query/${orderNo}`);
            showResponse('query-response', result);
            
            setButtonLoading(btn, false);
        }
        
        // 获取订单列表
        async function getOrderList() {
            const btn = event.target;
            btn.setAttribute('data-original-text', btn.innerHTML);
            setButtonLoading(btn, true);
            
            const page = document.getElementById('page').value;
            const pageSize = document.getElementById('pageSize').value;
            
            const result = await makeRequest(`/api/payment/orders?page=${page}&pageSize=${pageSize}`);
            showResponse('orders-response', result);
            
            setButtonLoading(btn, false);
        }
        
        // 获取支付统计
        async function getPaymentStats() {
            const btn = event.target;
            btn.setAttribute('data-original-text', btn.innerHTML);
            setButtonLoading(btn, true);
            
            const result = await makeRequest('/api/payment/stats');
            showResponse('stats-response', result);
            
            setButtonLoading(btn, false);
        }
        
        // 测试支付接口
        async function testPaymentAPI() {
            const btn = event.target;
            btn.setAttribute('data-original-text', btn.innerHTML);
            setButtonLoading(btn, true);
            
            const result = await makeRequest('/api/payment/test');
            showResponse('test-response', result);
            
            setButtonLoading(btn, false);
        }
    </script>
</body>
</html>
