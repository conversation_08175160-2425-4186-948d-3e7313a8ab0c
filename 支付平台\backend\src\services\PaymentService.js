const { Order, PaymentNotify, Merchant } = require('../models');
const LtzfService = require('./LtzfService');
const SignatureUtil = require('../utils/signature');
const logger = require('../utils/logger');

/**
 * 支付业务服务类
 * 处理支付相关的业务逻辑
 */
class PaymentService {
  constructor() {
    this.ltzfService = new LtzfService();
  }
  
  /**
   * 创建支付订单
   * @param {Object} params - 订单参数
   * @returns {Promise<Object>} 创建结果
   */
  async createPayment(params) {
    try {
      const {
        amount,           // 支付金额（元）
        body,            // 商品描述
        attach = '',     // 附加数据
        time_expire = '30m', // 过期时间
        notify_url,      // 通知地址
        client_ip,       // 客户端IP
        user_agent       // 用户代理
      } = params;
      
      // 生成商户订单号
      const out_trade_no = SignatureUtil.generateOutTradeNo();
      
      // 转换金额为分
      const total_fee = LtzfService.formatAmount(amount);
      
      // 计算过期时间
      const expire_time = this.ltzfService.calculateExpireTime(time_expire);
      
      // 调用蓝兔支付API
      const paymentResult = await this.ltzfService.nativePay({
        out_trade_no,
        total_fee,
        body,
        notify_url: notify_url || process.env.NOTIFY_URL,
        attach,
        time_expire
      });
      
      if (paymentResult.code !== 0) {
        throw new Error(paymentResult.msg || '支付创建失败');
      }
      
      // 保存订单到数据库
      const order = await Order.create({
        out_trade_no,
        total_fee,
        body,
        pay_status: 'pending',
        pay_channel: 'wxpay',
        trade_type: 'NATIVE',
        code_url: paymentResult.data.code_url,
        qrcode_url: paymentResult.data.QRcode_url,
        expire_time,
        attach,
        notify_url: notify_url || process.env.NOTIFY_URL,
        request_id: paymentResult.request_id,
        client_ip,
        user_agent
      });
      
      logger.payment('支付订单创建成功', {
        out_trade_no,
        total_fee,
        body
      });
      
      return {
        out_trade_no,
        amount: LtzfService.parseAmount(total_fee),
        code_url: paymentResult.data.code_url,
        qrcode_url: paymentResult.data.QRcode_url,
        expire_time,
        order_id: order.id
      };
      
    } catch (error) {
      logger.error('创建支付订单失败', error);
      throw error;
    }
  }
  
  /**
   * 查询订单状态
   * @param {string} out_trade_no - 商户订单号
   * @returns {Promise<Object>} 订单信息
   */
  async queryPayment(out_trade_no) {
    try {
      // 先从数据库查询
      const order = await Order.findOne({
        where: { out_trade_no }
      });
      
      if (!order) {
        throw new Error('订单不存在');
      }
      
      // 如果订单已支付，直接返回
      if (order.pay_status === 'paid') {
        return this.formatOrderResult(order);
      }
      
      // 如果订单已过期，更新状态
      if (order.expire_time && new Date() > order.expire_time) {
        await order.update({ pay_status: 'expired' });
        return this.formatOrderResult(order);
      }
      
      // 调用蓝兔支付API查询最新状态
      const queryResult = await this.ltzfService.queryOrder(out_trade_no);
      
      if (queryResult.code === 0 && queryResult.data) {
        const { data } = queryResult;
        
        // 更新订单信息
        const updateData = {
          order_no: data.order_no,
          pay_status: data.pay_status === 1 ? 'paid' : 'pending'
        };
        
        if (data.pay_status === 1) {
          updateData.pay_no = data.pay_no;
          updateData.success_time = new Date(data.success_time);
          updateData.openid = data.openid;
        }
        
        await order.update(updateData);
        
        // 重新获取更新后的订单
        await order.reload();
      }
      
      return this.formatOrderResult(order);
      
    } catch (error) {
      logger.error('查询支付订单失败', error);
      throw error;
    }
  }

  /**
   * 处理支付通知
   * @param {Object} notifyData - 通知数据
   * @param {string} clientIp - 客户端IP
   * @param {string} userAgent - 用户代理
   * @returns {Promise<string>} 处理结果
   */
  async handlePaymentNotify(notifyData, clientIp, userAgent) {
    let notify = null;

    try {
      // 记录通知数据
      notify = await PaymentNotify.create({
        out_trade_no: notifyData.out_trade_no,
        order_no: notifyData.order_no,
        pay_no: notifyData.pay_no,
        code: notifyData.code,
        total_fee: notifyData.total_fee,
        mch_id: notifyData.mch_id,
        timestamp: notifyData.timestamp,
        sign: notifyData.sign,
        pay_channel: notifyData.pay_channel,
        trade_type: notifyData.trade_type,
        success_time: notifyData.success_time,
        attach: notifyData.attach,
        openid: notifyData.openid,
        raw_data: JSON.stringify(notifyData),
        client_ip: clientIp,
        user_agent: userAgent,
        process_status: 'pending'
      });

      logger.payment('收到支付通知', {
        out_trade_no: notifyData.out_trade_no,
        code: notifyData.code,
        pay_no: notifyData.pay_no
      });

      // 验证签名
      const signValid = this.ltzfService.verifyNotifySign(notifyData);
      await notify.update({ sign_valid: signValid });

      if (!signValid) {
        await notify.update({
          process_status: 'failed',
          process_result: '签名验证失败',
          process_time: new Date()
        });

        logger.error('支付通知签名验证失败', { out_trade_no: notifyData.out_trade_no });
        return 'FAIL';
      }

      // 查找订单
      const order = await Order.findOne({
        where: { out_trade_no: notifyData.out_trade_no }
      });

      if (!order) {
        await notify.update({
          process_status: 'failed',
          process_result: '订单不存在',
          process_time: new Date()
        });

        logger.error('支付通知对应的订单不存在', { out_trade_no: notifyData.out_trade_no });
        return 'FAIL';
      }

      // 检查订单状态
      if (order.pay_status === 'paid') {
        await notify.update({
          process_status: 'success',
          process_result: '订单已处理',
          process_time: new Date()
        });

        logger.info('订单已支付，重复通知', { out_trade_no: notifyData.out_trade_no });
        return 'SUCCESS';
      }

      // 处理支付成功通知
      if (notifyData.code === '0') {
        await order.update({
          order_no: notifyData.order_no,
          pay_no: notifyData.pay_no,
          pay_status: 'paid',
          success_time: new Date(notifyData.success_time),
          openid: notifyData.openid
        });

        await notify.update({
          process_status: 'success',
          process_result: '支付成功处理完成',
          process_time: new Date()
        });

        logger.payment('订单支付成功', {
          out_trade_no: notifyData.out_trade_no,
          pay_no: notifyData.pay_no,
          total_fee: notifyData.total_fee
        });

        return 'SUCCESS';
      } else {
        // 支付失败
        await order.update({
          pay_status: 'failed'
        });

        await notify.update({
          process_status: 'success',
          process_result: '支付失败处理完成',
          process_time: new Date()
        });

        logger.payment('订单支付失败', {
          out_trade_no: notifyData.out_trade_no,
          code: notifyData.code
        });

        return 'SUCCESS';
      }

    } catch (error) {
      logger.error('处理支付通知失败', error);

      if (notify) {
        await notify.update({
          process_status: 'failed',
          process_result: `处理异常: ${error.message}`,
          process_time: new Date()
        });
      }

      return 'FAIL';
    }
  }

  /**
   * 格式化订单结果
   * @param {Object} order - 订单对象
   * @returns {Object} 格式化后的订单信息
   */
  formatOrderResult(order) {
    return {
      out_trade_no: order.out_trade_no,
      order_no: order.order_no,
      pay_no: order.pay_no,
      amount: LtzfService.parseAmount(order.total_fee),
      body: order.body,
      pay_status: order.pay_status,
      pay_channel: order.pay_channel,
      trade_type: order.trade_type,
      code_url: order.code_url,
      qrcode_url: order.qrcode_url,
      success_time: order.success_time,
      expire_time: order.expire_time,
      attach: order.attach,
      openid: order.openid,
      created_at: order.created_at,
      updated_at: order.updated_at
    };
  }

  /**
   * 获取订单列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 订单列表
   */
  async getOrderList(params) {
    try {
      const {
        page = 1,
        pageSize = 10,
        pay_status,
        start_date,
        end_date,
        out_trade_no
      } = params;

      const where = {};

      if (pay_status) {
        where.pay_status = pay_status;
      }

      if (out_trade_no) {
        where.out_trade_no = out_trade_no;
      }

      if (start_date && end_date) {
        where.created_at = {
          [require('sequelize').Op.between]: [new Date(start_date), new Date(end_date)]
        };
      }

      const { count, rows } = await Order.findAndCountAll({
        where,
        order: [['created_at', 'DESC']],
        limit: parseInt(pageSize),
        offset: (parseInt(page) - 1) * parseInt(pageSize)
      });

      return {
        list: rows.map(order => this.formatOrderResult(order)),
        total: count,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      };

    } catch (error) {
      logger.error('获取订单列表失败', error);
      throw error;
    }
  }
}
