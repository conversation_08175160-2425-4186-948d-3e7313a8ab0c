hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@antfu/utils@0.7.10':
    '@antfu/utils': private
  '@asamuzakjp/css-color@3.2.0':
    '@asamuzakjp/css-color': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.0':
    '@babel/core': private
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.27.3':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-member-expression-to-functions@7.27.1':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.27.1':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/plugin-proposal-decorators@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-proposal-decorators': private
  '@babel/plugin-syntax-decorators@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-decorators': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-typescript': private
  '@babel/plugin-transform-typescript@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-typescript': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
  '@babel/types@7.28.0':
    '@babel/types': private
  '@csstools/color-helpers@5.0.2':
    '@csstools/color-helpers': private
  '@csstools/css-calc@2.1.4(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-calc': private
  '@csstools/css-color-parser@3.0.10(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-color-parser': private
  '@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-parser-algorithms': private
  '@csstools/css-tokenizer@3.0.4':
    '@csstools/css-tokenizer': private
  '@cypress/request@3.0.8':
    '@cypress/request': private
  '@cypress/xvfb@1.2.4(supports-color@8.1.1)':
    '@cypress/xvfb': private
  '@esbuild/aix-ppc64@0.25.6':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.6':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.6':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.6':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.6':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.6':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.6':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.6':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.6':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.6':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.6':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.6':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.6':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.6':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.6':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.6':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.6':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.6':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.6':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.6':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.6':
    '@esbuild/openbsd-x64': private
  '@esbuild/openharmony-arm64@0.25.6':
    '@esbuild/openharmony-arm64': private
  '@esbuild/sunos-x64@0.25.6':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.6':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.6':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.6':
    '@esbuild/win32-x64': private
  '@hapi/hoek@9.3.0':
    '@hapi/hoek': private
  '@hapi/topo@5.1.0':
    '@hapi/topo': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@one-ini/wasm@0.1.1':
    '@one-ini/wasm': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@polka/url@1.0.0-next.29':
    '@polka/url': private
  '@rolldown/pluginutils@1.0.0-beta.24':
    '@rolldown/pluginutils': private
  '@rollup/pluginutils@5.2.0(rollup@4.44.2)':
    '@rollup/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.44.2':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.44.2':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.44.2':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.44.2':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.44.2':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.44.2':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.44.2':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.44.2':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.44.2':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.44.2':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.44.2':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.44.2':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.44.2':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.44.2':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.44.2':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.44.2':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.44.2':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.44.2':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.44.2':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.44.2':
    '@rollup/rollup-win32-x64-msvc': private
  '@sec-ant/readable-stream@0.4.1':
    '@sec-ant/readable-stream': private
  '@sideway/address@4.1.5':
    '@sideway/address': private
  '@sideway/formula@3.0.1':
    '@sideway/formula': private
  '@sideway/pinpoint@2.0.0':
    '@sideway/pinpoint': private
  '@sindresorhus/merge-streams@4.0.0':
    '@sindresorhus/merge-streams': private
  '@types/chai@5.2.2':
    '@types/chai': private
  '@types/deep-eql@4.0.2':
    '@types/deep-eql': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/sinonjs__fake-timers@8.1.1':
    '@types/sinonjs__fake-timers': private
  '@types/sizzle@2.3.9':
    '@types/sizzle': private
  '@types/tough-cookie@4.0.5':
    '@types/tough-cookie': private
  '@types/yauzl@2.10.3':
    '@types/yauzl': private
  '@vitest/expect@3.2.4':
    '@vitest/expect': private
  '@vitest/mocker@3.2.4(vite@7.0.2(@types/node@22.16.0))':
    '@vitest/mocker': private
  '@vitest/pretty-format@3.2.4':
    '@vitest/pretty-format': private
  '@vitest/runner@3.2.4':
    '@vitest/runner': private
  '@vitest/snapshot@3.2.4':
    '@vitest/snapshot': private
  '@vitest/spy@3.2.4':
    '@vitest/spy': private
  '@vitest/utils@3.2.4':
    '@vitest/utils': private
  '@volar/language-core@2.4.15':
    '@volar/language-core': private
  '@volar/source-map@2.4.15':
    '@volar/source-map': private
  '@volar/typescript@2.4.15':
    '@volar/typescript': private
  '@vue/babel-helper-vue-transform-on@1.4.0':
    '@vue/babel-helper-vue-transform-on': private
  '@vue/babel-plugin-jsx@1.4.0(@babel/core@7.28.0)':
    '@vue/babel-plugin-jsx': private
  '@vue/babel-plugin-resolve-type@1.4.0(@babel/core@7.28.0)':
    '@vue/babel-plugin-resolve-type': private
  '@vue/compiler-core@3.5.17':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.17':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.17':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.17':
    '@vue/compiler-ssr': private
  '@vue/compiler-vue2@2.7.16':
    '@vue/compiler-vue2': private
  '@vue/devtools-api@7.7.7':
    '@vue/devtools-api': private
  '@vue/devtools-core@7.7.7(vite@7.0.2(@types/node@22.16.0))(vue@3.5.17(typescript@5.8.3))':
    '@vue/devtools-core': private
  '@vue/devtools-kit@7.7.7':
    '@vue/devtools-kit': private
  '@vue/devtools-shared@7.7.7':
    '@vue/devtools-shared': private
  '@vue/language-core@2.2.12(typescript@5.8.3)':
    '@vue/language-core': private
  '@vue/reactivity@3.5.17':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.17':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.17':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.17(vue@3.5.17(typescript@5.8.3))':
    '@vue/server-renderer': private
  '@vue/shared@3.5.17':
    '@vue/shared': private
  abbrev@2.0.0:
    abbrev: private
  agent-base@7.1.4:
    agent-base: private
  aggregate-error@3.1.0:
    aggregate-error: private
  alien-signals@1.0.13:
    alien-signals: private
  ansi-colors@4.1.3:
    ansi-colors: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@6.2.1:
    ansi-styles: private
  arch@2.2.0:
    arch: private
  arg@5.0.2:
    arg: private
  asn1@0.2.6:
    asn1: private
  assert-plus@1.0.0:
    assert-plus: private
  assertion-error@2.0.1:
    assertion-error: private
  astral-regex@2.0.0:
    astral-regex: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  at-least-node@1.0.0:
    at-least-node: private
  aws-sign2@0.7.0:
    aws-sign2: private
  aws4@1.13.2:
    aws4: private
  axios@1.10.0(debug@4.4.1):
    axios: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  bcrypt-pbkdf@1.0.2:
    bcrypt-pbkdf: private
  birpc@2.4.0:
    birpc: private
  blob-util@2.0.2:
    blob-util: private
  bluebird@3.7.2:
    bluebird: private
  brace-expansion@2.0.2:
    brace-expansion: private
  browserslist@4.25.1:
    browserslist: private
  buffer-crc32@0.2.13:
    buffer-crc32: private
  buffer@5.7.1:
    buffer: private
  bundle-name@4.1.0:
    bundle-name: private
  cac@6.7.14:
    cac: private
  cachedir@2.4.0:
    cachedir: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  caniuse-lite@1.0.30001727:
    caniuse-lite: private
  caseless@0.12.0:
    caseless: private
  chai@5.2.0:
    chai: private
  chalk@4.1.2:
    chalk: private
  check-error@2.1.1:
    check-error: private
  check-more-types@2.24.0:
    check-more-types: private
  ci-info@4.3.0:
    ci-info: private
  clean-stack@2.2.0:
    clean-stack: private
  cli-cursor@3.1.0:
    cli-cursor: private
  cli-table3@0.6.1:
    cli-table3: private
  cli-truncate@2.1.0:
    cli-truncate: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  colorette@2.0.20:
    colorette: private
  colors@1.4.0:
    colors: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@6.2.1:
    commander: private
  common-tags@1.8.2:
    common-tags: private
  config-chain@1.1.13:
    config-chain: private
  convert-source-map@2.0.0:
    convert-source-map: private
  copy-anything@3.0.5:
    copy-anything: private
  core-util-is@1.0.2:
    core-util-is: private
  cross-spawn@7.0.6:
    cross-spawn: private
  cssstyle@4.6.0:
    cssstyle: private
  csstype@3.1.3:
    csstype: private
  dashdash@1.14.1:
    dashdash: private
  data-urls@5.0.0:
    data-urls: private
  dayjs@1.11.13:
    dayjs: private
  de-indent@1.0.2:
    de-indent: private
  debug@4.4.1(supports-color@8.1.1):
    debug: private
  decimal.js@10.6.0:
    decimal.js: private
  deep-eql@5.0.2:
    deep-eql: private
  default-browser-id@5.0.0:
    default-browser-id: private
  default-browser@5.2.1:
    default-browser: private
  define-lazy-prop@3.0.0:
    define-lazy-prop: private
  delayed-stream@1.0.0:
    delayed-stream: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexer@0.1.2:
    duplexer: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ecc-jsbn@0.1.2:
    ecc-jsbn: private
  editorconfig@1.0.4:
    editorconfig: private
  electron-to-chromium@1.5.180:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  end-of-stream@1.4.5:
    end-of-stream: private
  enquirer@2.4.1:
    enquirer: private
  entities@6.0.1:
    entities: private
  error-stack-parser-es@0.1.5:
    error-stack-parser-es: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  esbuild@0.25.6:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@1.0.5:
    escape-string-regexp: private
  estree-walker@3.0.3:
    estree-walker: private
  event-stream@3.3.4:
    event-stream: private
  eventemitter2@6.4.7:
    eventemitter2: private
  execa@4.1.0:
    execa: private
  executable@4.1.1:
    executable: private
  expect-type@1.2.2:
    expect-type: private
  extend@3.0.2:
    extend: private
  extract-zip@2.0.1(supports-color@8.1.1):
    extract-zip: private
  extsprintf@1.3.0:
    extsprintf: private
  fd-slicer@1.1.0:
    fd-slicer: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  figures@3.2.0:
    figures: private
  follow-redirects@1.15.9(debug@4.4.1):
    follow-redirects: private
  foreground-child@3.3.1:
    foreground-child: private
  forever-agent@0.6.1:
    forever-agent: private
  form-data@4.0.3:
    form-data: private
  from@0.1.7:
    from: private
  fs-extra@9.1.0:
    fs-extra: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@5.2.0:
    get-stream: private
  getos@3.2.1:
    getos: private
  getpass@0.1.7:
    getpass: private
  glob@10.4.5:
    glob: private
  global-dirs@3.0.1:
    global-dirs: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasha@5.2.2:
    hasha: private
  hasown@2.0.2:
    hasown: private
  he@1.2.0:
    he: private
  hookable@5.5.3:
    hookable: private
  html-encoding-sniffer@4.0.0:
    html-encoding-sniffer: private
  http-proxy-agent@7.0.2:
    http-proxy-agent: private
  http-signature@1.4.0:
    http-signature: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  human-signals@1.1.1:
    human-signals: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ieee754@1.2.1:
    ieee754: private
  indent-string@4.0.0:
    indent-string: private
  ini@1.3.8:
    ini: private
  is-docker@3.0.0:
    is-docker: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-inside-container@1.0.0:
    is-inside-container: private
  is-installed-globally@0.4.0:
    is-installed-globally: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  is-potential-custom-element-name@1.0.1:
    is-potential-custom-element-name: private
  is-stream@2.0.1:
    is-stream: private
  is-typedarray@1.0.0:
    is-typedarray: private
  is-unicode-supported@0.1.0:
    is-unicode-supported: private
  is-what@4.1.16:
    is-what: private
  is-wsl@3.1.0:
    is-wsl: private
  isexe@3.1.1:
    isexe: private
  isstream@0.1.2:
    isstream: private
  jackspeak@3.4.3:
    jackspeak: private
  joi@17.13.3:
    joi: private
  js-beautify@1.15.4:
    js-beautify: private
  js-cookie@3.0.5:
    js-cookie: private
  js-tokens@4.0.0:
    js-tokens: private
  jsbn@0.1.1:
    jsbn: private
  jsesc@3.1.0:
    jsesc: private
  json-parse-even-better-errors@4.0.0:
    json-parse-even-better-errors: private
  json-schema@0.4.0:
    json-schema: private
  json-stringify-safe@5.0.1:
    json-stringify-safe: private
  json5@2.2.3:
    json5: private
  jsonfile@6.1.0:
    jsonfile: private
  jsprim@2.0.2:
    jsprim: private
  kolorist@1.8.0:
    kolorist: private
  lazy-ass@1.6.0:
    lazy-ass: private
  listr2@3.14.0(enquirer@2.4.1):
    listr2: private
  lodash.once@4.1.1:
    lodash.once: private
  lodash@4.17.21:
    lodash: private
  log-symbols@4.1.0:
    log-symbols: private
  log-update@4.0.0:
    log-update: private
  loupe@3.1.4:
    loupe: private
  lru-cache@10.4.3:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  map-stream@0.1.0:
    map-stream: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  memorystream@0.3.1:
    memorystream: private
  merge-stream@2.0.0:
    merge-stream: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mimic-fn@2.1.0:
    mimic-fn: private
  minimatch@9.0.5:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  mitt@3.0.1:
    mitt: private
  mrmime@2.0.1:
    mrmime: private
  ms@2.1.3:
    ms: private
  muggle-string@0.4.1:
    muggle-string: private
  nanoid@5.1.5:
    nanoid: private
  node-releases@2.0.19:
    node-releases: private
  nopt@7.2.1:
    nopt: private
  npm-normalize-package-bin@4.0.0:
    npm-normalize-package-bin: private
  npm-run-path@4.0.1:
    npm-run-path: private
  nwsapi@2.2.20:
    nwsapi: private
  object-inspect@1.13.4:
    object-inspect: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  open@10.1.2:
    open: private
  ospath@1.2.2:
    ospath: private
  p-map@4.0.0:
    p-map: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parse-ms@4.0.0:
    parse-ms: private
  parse5@7.3.0:
    parse5: private
  path-browserify@1.0.1:
    path-browserify: private
  path-key@3.1.1:
    path-key: private
  path-scurry@1.11.1:
    path-scurry: private
  pathe@2.0.3:
    pathe: private
  pathval@2.0.1:
    pathval: private
  pause-stream@0.0.11:
    pause-stream: private
  pend@1.2.0:
    pend: private
  perfect-debounce@1.0.0:
    perfect-debounce: private
  performance-now@2.1.0:
    performance-now: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pidtree@0.6.0:
    pidtree: private
  pify@2.3.0:
    pify: private
  playwright-core@1.53.2:
    playwright-core: private
  playwright@1.53.2:
    playwright: private
  postcss@8.5.6:
    postcss: private
  pretty-bytes@5.6.0:
    pretty-bytes: private
  pretty-ms@9.2.0:
    pretty-ms: private
  process@0.11.10:
    process: private
  proto-list@1.2.4:
    proto-list: private
  proxy-from-env@1.0.0:
    proxy-from-env: private
  ps-tree@1.2.0:
    ps-tree: private
  pump@3.0.3:
    pump: private
  punycode@2.3.1:
    punycode: private
  qs@6.14.0:
    qs: private
  read-package-json-fast@4.0.0:
    read-package-json-fast: private
  request-progress@3.0.0:
    request-progress: private
  restore-cursor@3.1.0:
    restore-cursor: private
  rfdc@1.4.1:
    rfdc: private
  rollup@4.44.2:
    rollup: private
  rrweb-cssom@0.8.0:
    rrweb-cssom: private
  run-applescript@7.0.0:
    run-applescript: private
  rxjs@7.8.2:
    rxjs: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  saxes@6.0.0:
    saxes: private
  semver@7.7.2:
    semver: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.3:
    shell-quote: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  siginfo@2.0.0:
    siginfo: private
  signal-exit@3.0.7:
    signal-exit: private
  sirv@3.0.1:
    sirv: private
  slice-ansi@3.0.0:
    slice-ansi: private
  source-map-js@1.2.1:
    source-map-js: private
  speakingurl@14.0.1:
    speakingurl: private
  split@0.3.3:
    split: private
  sshpk@1.18.0:
    sshpk: private
  stackback@0.0.2:
    stackback: private
  std-env@3.9.0:
    std-env: private
  stream-combiner@0.0.4:
    stream-combiner: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-literal@3.0.0:
    strip-literal: private
  superjson@2.2.2:
    superjson: private
  supports-color@8.1.1:
    supports-color: private
  symbol-tree@3.2.4:
    symbol-tree: private
  throttleit@1.0.1:
    throttleit: private
  through@2.3.8:
    through: private
  tinybench@2.9.0:
    tinybench: private
  tinyexec@0.3.2:
    tinyexec: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tinypool@1.1.1:
    tinypool: private
  tinyrainbow@2.0.0:
    tinyrainbow: private
  tinyspy@4.0.3:
    tinyspy: private
  tldts-core@6.1.86:
    tldts-core: private
  tldts@6.1.86:
    tldts: private
  tmp@0.2.3:
    tmp: private
  totalist@3.0.1:
    totalist: private
  tough-cookie@5.1.2:
    tough-cookie: private
  tr46@5.1.1:
    tr46: private
  tree-kill@1.2.2:
    tree-kill: private
  tslib@2.8.1:
    tslib: private
  tunnel-agent@0.6.0:
    tunnel-agent: private
  tweetnacl@0.14.5:
    tweetnacl: private
  type-fest@0.8.1:
    type-fest: private
  undici-types@6.21.0:
    undici-types: private
  unicorn-magic@0.3.0:
    unicorn-magic: private
  universalify@2.0.1:
    universalify: private
  untildify@4.0.0:
    untildify: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  uuid@8.3.2:
    uuid: private
  verror@1.10.0:
    verror: private
  vite-hot-client@2.1.0(vite@7.0.2(@types/node@22.16.0)):
    vite-hot-client: private
  vite-node@3.2.4(@types/node@22.16.0):
    vite-node: private
  vite-plugin-inspect@0.8.9(rollup@4.44.2)(vite@7.0.2(@types/node@22.16.0)):
    vite-plugin-inspect: private
  vite-plugin-vue-inspector@5.3.2(vite@7.0.2(@types/node@22.16.0)):
    vite-plugin-vue-inspector: private
  vscode-uri@3.1.0:
    vscode-uri: private
  vue-component-type-helpers@2.2.12:
    vue-component-type-helpers: private
  w3c-xmlserializer@5.0.0:
    w3c-xmlserializer: private
  wait-on@8.0.3(debug@4.4.1):
    wait-on: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  whatwg-encoding@3.1.1:
    whatwg-encoding: private
  whatwg-mimetype@4.0.0:
    whatwg-mimetype: private
  whatwg-url@14.2.0:
    whatwg-url: private
  which@5.0.0:
    which: private
  why-is-node-running@2.3.0:
    why-is-node-running: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  ws@8.18.3:
    ws: private
  xml-name-validator@5.0.0:
    xml-name-validator: private
  xmlchars@2.2.0:
    xmlchars: private
  yallist@3.1.1:
    yallist: private
  yauzl@2.10.0:
    yauzl: private
  yoctocolors@2.1.1:
    yoctocolors: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.12.3
pendingBuilds: []
prunedAt: Tue, 08 Jul 2025 05:22:48 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.6'
  - '@esbuild/android-arm64@0.25.6'
  - '@esbuild/android-arm@0.25.6'
  - '@esbuild/android-x64@0.25.6'
  - '@esbuild/darwin-arm64@0.25.6'
  - '@esbuild/darwin-x64@0.25.6'
  - '@esbuild/freebsd-arm64@0.25.6'
  - '@esbuild/freebsd-x64@0.25.6'
  - '@esbuild/linux-arm64@0.25.6'
  - '@esbuild/linux-arm@0.25.6'
  - '@esbuild/linux-ia32@0.25.6'
  - '@esbuild/linux-loong64@0.25.6'
  - '@esbuild/linux-mips64el@0.25.6'
  - '@esbuild/linux-ppc64@0.25.6'
  - '@esbuild/linux-riscv64@0.25.6'
  - '@esbuild/linux-s390x@0.25.6'
  - '@esbuild/linux-x64@0.25.6'
  - '@esbuild/netbsd-arm64@0.25.6'
  - '@esbuild/netbsd-x64@0.25.6'
  - '@esbuild/openbsd-arm64@0.25.6'
  - '@esbuild/openbsd-x64@0.25.6'
  - '@esbuild/openharmony-arm64@0.25.6'
  - '@esbuild/sunos-x64@0.25.6'
  - '@esbuild/win32-arm64@0.25.6'
  - '@esbuild/win32-ia32@0.25.6'
  - '@rollup/rollup-android-arm-eabi@4.44.2'
  - '@rollup/rollup-android-arm64@4.44.2'
  - '@rollup/rollup-darwin-arm64@4.44.2'
  - '@rollup/rollup-darwin-x64@4.44.2'
  - '@rollup/rollup-freebsd-arm64@4.44.2'
  - '@rollup/rollup-freebsd-x64@4.44.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.44.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.44.2'
  - '@rollup/rollup-linux-arm64-gnu@4.44.2'
  - '@rollup/rollup-linux-arm64-musl@4.44.2'
  - '@rollup/rollup-linux-loongarch64-gnu@4.44.2'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.44.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.44.2'
  - '@rollup/rollup-linux-riscv64-musl@4.44.2'
  - '@rollup/rollup-linux-s390x-gnu@4.44.2'
  - '@rollup/rollup-linux-x64-gnu@4.44.2'
  - '@rollup/rollup-linux-x64-musl@4.44.2'
  - '@rollup/rollup-win32-arm64-msvc@4.44.2'
  - '@rollup/rollup-win32-ia32-msvc@4.44.2'
  - fsevents@2.3.2
  - fsevents@2.3.3
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v3
virtualStoreDir: C:\Users\<USER>\Desktop\支付平台\node_modules\.pnpm
virtualStoreDirMaxLength: 120
