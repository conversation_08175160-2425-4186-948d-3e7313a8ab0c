const crypto = require('crypto');

/**
 * 蓝兔支付签名工具类
 * 实现与蓝兔支付API文档一致的签名算法
 */
class SignatureUtil {
  
  /**
   * 生成签名
   * @param {Object} params - 参数对象
   * @param {string} key - 商户密钥
   * @returns {string} 签名字符串
   */
  static generateSign(params, key) {
    // 过滤空值和sign字段
    const filteredParams = {};
    Object.keys(params).forEach(k => {
      if (params[k] !== '' && params[k] !== null && params[k] !== undefined && k !== 'sign') {
        filteredParams[k] = params[k];
      }
    });
    
    // 按键名ASCII码排序
    const sortedKeys = Object.keys(filteredParams).sort();
    
    // 构建签名字符串
    const stringArr = [];
    sortedKeys.forEach(k => {
      stringArr.push(`${k}=${filteredParams[k]}`);
    });
    
    // 添加密钥
    stringArr.push(`key=${key}`);
    
    // 拼接字符串
    const stringSignTemp = stringArr.join('&');
    
    // MD5加密并转大写
    return crypto.createHash('md5').update(stringSignTemp, 'utf8').digest('hex').toUpperCase();
  }
  
  /**
   * 验证签名
   * @param {Object} params - 参数对象
   * @param {string} key - 商户密钥
   * @returns {boolean} 验证结果
   */
  static verifySign(params, key) {
    const receivedSign = params.sign;
    if (!receivedSign) {
      return false;
    }
    
    const calculatedSign = this.generateSign(params, key);
    return receivedSign === calculatedSign;
  }
  
  /**
   * 为请求参数添加签名
   * @param {Object} params - 请求参数
   * @param {string} key - 商户密钥
   * @returns {Object} 包含签名的参数对象
   */
  static signParams(params, key) {
    const signedParams = { ...params };
    signedParams.sign = this.generateSign(params, key);
    return signedParams;
  }
  
  /**
   * 生成随机字符串
   * @param {number} length - 字符串长度
   * @returns {string} 随机字符串
   */
  static generateNonceStr(length = 32) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
  
  /**
   * 生成时间戳
   * @returns {string} 当前时间戳（秒）
   */
  static generateTimestamp() {
    return Math.floor(Date.now() / 1000).toString();
  }
  
  /**
   * 生成商户订单号
   * @param {string} prefix - 前缀
   * @returns {string} 商户订单号
   */
  static generateOutTradeNo(prefix = 'PAY') {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `${prefix}${timestamp}${random}`;
  }
}

module.exports = SignatureUtil;
