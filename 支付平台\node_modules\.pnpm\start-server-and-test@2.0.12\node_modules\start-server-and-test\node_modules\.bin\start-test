#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/start-server-and-test@2.0.12/node_modules/start-server-and-test/src/bin/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/start-server-and-test@2.0.12/node_modules/start-server-and-test/src/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/start-server-and-test@2.0.12/node_modules/start-server-and-test/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/start-server-and-test@2.0.12/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/start-server-and-test@2.0.12/node_modules/start-server-and-test/src/bin/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/start-server-and-test@2.0.12/node_modules/start-server-and-test/src/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/start-server-and-test@2.0.12/node_modules/start-server-and-test/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/start-server-and-test@2.0.12/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../src/bin/start.js" "$@"
else
  exec node  "$basedir/../../src/bin/start.js" "$@"
fi
