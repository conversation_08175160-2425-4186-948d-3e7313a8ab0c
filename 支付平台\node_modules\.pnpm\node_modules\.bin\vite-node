#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/vite-node@3.2.4_@types+node@22.16.0/node_modules/vite-node/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/vite-node@3.2.4_@types+node@22.16.0/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/vite-node@3.2.4_@types+node@22.16.0/node_modules/vite-node/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/vite-node@3.2.4_@types+node@22.16.0/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vite-node/vite-node.mjs" "$@"
else
  exec node  "$basedir/../vite-node/vite-node.mjs" "$@"
fi
