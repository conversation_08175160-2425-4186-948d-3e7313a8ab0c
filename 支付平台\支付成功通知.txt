支付通知API
蓝兔支付通过支付通知接口将用户支付成功消息通知给商户
注意：

同样的通知可能会多次发送给商户系统。商户系统必须能够正确处理重复的通知。 推荐的做法是，当商户系统收到通知进行处理时，先检查对应业务数据的状态，并判断该通知是否已经处理。如果未处理，则再进行处理；如果已处理，则直接返回结果成功。在对业务数据进行状态检查和处理之前，要采用数据锁进行并发控制，以避免函数重入造成的数据混乱。
如果在所有通知频率后没有收到蓝兔支付侧回调，商户应调用查询订单接口确认订单状态。
特别提醒：商户系统对于开启结果通知的内容一定要做签名验证，并校验通知的信息是否与商户侧的信息一致，防止数据泄露导致出现“假通知”，造成资金损失。

接口说明
适用对象：个人、个体户、企业

请求方式：POST

回调URL：该链接是通过支付接口中的请求参数“notify_url”来设置的，要求必须为http或https地址。请确保回调URL是外部可正常访问的，且不能携带后缀参数，否则可能导致商户无法接收到蓝兔支付的回调通知信息。回调URL示例：“https://pay.weixin.qq.com/wxpay/pay.action”

通知规则
用户支付完成后，蓝兔支付会把相关支付结果和用户信息发送给商户，商户需要接收处理该消息，并返回应答。

对后台通知交互时，如果蓝兔支付收到商户的应答不符合规范或超时，蓝兔支付认为通知失败，蓝兔支付会通过一定的策略定期重新发起通知，尽可能提高通知的成功率，但蓝兔支付不保证通知最终能成功。（通知频率为15s/15s/30s/3m/10m/20m/30m/30m/30m/60m/3h/3h/3h/6h/6h - 总计 24h4m）

通知参数
参数名	参数类型	是否参与签名	描述
code	String	是	支付结果，枚举值：
0：成功
1：失败
示例值：0
timestamp	String	是	时间戳
示例值：1669518774
mch_id	String	是	商户号
示例值：1230000109
order_no	String	是	系统订单号
示例值：WX202211221155084844072633
out_trade_no	String	是	商户订单号
示例值：LTZF2022112264463
pay_no	String	是	支付宝或微信支付订单号
示例值：4200001635202211222291508463
total_fee	String	是	支付金额
示例值：0.01
sign	String	否	签名，签名验证的算法请参考《签名算法》。
示例值：575225E549B2FBB82FB23505263633CD
pay_channel	String	否	支付渠道，枚举值：
alipay：支付宝
wxpay：微信支付
示例值：wxpay
trade_type	String	否	支付类型，枚举值：
NATIVE：扫码支付
H5：H5支付
APP：APP支付
JSAPI：公众号支付
MINIPROGRAM：小程序支付
示例值：NATIVE
success_time	String	否	支付完成时间
示例值：2022-11-22 11:55:42
attach	String	否	附加数据，在支付接口中填写的数据，可作为自定义参数使用。
示例值：自定义数据
openid	String	否	支付者信息
示例值：o5wq46GAKVxVKpsdcI4aU4cBpgT0
通知应答
接收成功：HTTP应答状态码需返回200，同时应答报文需返回：SUCCESS，必须为大写。

接收失败：应答报文返回：FAIL。