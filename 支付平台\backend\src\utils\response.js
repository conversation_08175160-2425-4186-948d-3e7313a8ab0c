/**
 * 统一响应工具类
 * 提供标准化的API响应格式
 */
class ResponseUtil {
  
  /**
   * 成功响应
   * @param {Object} ctx - Koa上下文
   * @param {*} data - 响应数据
   * @param {string} message - 响应消息
   * @param {number} code - 状态码
   */
  static success(ctx, data = null, message = '操作成功', code = 0) {
    ctx.status = 200;
    ctx.body = {
      code,
      message,
      data,
      timestamp: Date.now()
    };
  }
  
  /**
   * 失败响应
   * @param {Object} ctx - Koa上下文
   * @param {string} message - 错误消息
   * @param {number} code - 错误码
   * @param {*} data - 错误数据
   */
  static error(ctx, message = '操作失败', code = 1, data = null) {
    ctx.status = 200; // 业务错误仍返回200状态码
    ctx.body = {
      code,
      message,
      data,
      timestamp: Date.now()
    };
  }
  
  /**
   * 参数错误响应
   * @param {Object} ctx - Koa上下文
   * @param {string} message - 错误消息
   * @param {*} data - 错误数据
   */
  static badRequest(ctx, message = '参数错误', data = null) {
    ctx.status = 400;
    ctx.body = {
      code: 400,
      message,
      data,
      timestamp: Date.now()
    };
  }
  
  /**
   * 未授权响应
   * @param {Object} ctx - Koa上下文
   * @param {string} message - 错误消息
   */
  static unauthorized(ctx, message = '未授权访问') {
    ctx.status = 401;
    ctx.body = {
      code: 401,
      message,
      data: null,
      timestamp: Date.now()
    };
  }
  
  /**
   * 禁止访问响应
   * @param {Object} ctx - Koa上下文
   * @param {string} message - 错误消息
   */
  static forbidden(ctx, message = '禁止访问') {
    ctx.status = 403;
    ctx.body = {
      code: 403,
      message,
      data: null,
      timestamp: Date.now()
    };
  }
  
  /**
   * 资源不存在响应
   * @param {Object} ctx - Koa上下文
   * @param {string} message - 错误消息
   */
  static notFound(ctx, message = '资源不存在') {
    ctx.status = 404;
    ctx.body = {
      code: 404,
      message,
      data: null,
      timestamp: Date.now()
    };
  }
  
  /**
   * 服务器错误响应
   * @param {Object} ctx - Koa上下文
   * @param {string} message - 错误消息
   * @param {*} error - 错误对象
   */
  static serverError(ctx, message = '服务器内部错误', error = null) {
    ctx.status = 500;
    ctx.body = {
      code: 500,
      message,
      data: process.env.NODE_ENV === 'development' ? error : null,
      timestamp: Date.now()
    };
  }
  
  /**
   * 分页响应
   * @param {Object} ctx - Koa上下文
   * @param {Array} list - 数据列表
   * @param {number} total - 总数
   * @param {number} page - 当前页
   * @param {number} pageSize - 每页大小
   * @param {string} message - 响应消息
   */
  static page(ctx, list = [], total = 0, page = 1, pageSize = 10, message = '查询成功') {
    ctx.status = 200;
    ctx.body = {
      code: 0,
      message,
      data: {
        list,
        pagination: {
          total,
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          totalPages: Math.ceil(total / pageSize)
        }
      },
      timestamp: Date.now()
    };
  }
}

module.exports = ResponseUtil;
