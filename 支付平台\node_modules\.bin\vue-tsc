#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/vue-tsc@2.2.12_typescript@5.8.3/node_modules/vue-tsc/bin/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/vue-tsc@2.2.12_typescript@5.8.3/node_modules/vue-tsc/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/vue-tsc@2.2.12_typescript@5.8.3/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/vue-tsc@2.2.12_typescript@5.8.3/node_modules/vue-tsc/bin/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/vue-tsc@2.2.12_typescript@5.8.3/node_modules/vue-tsc/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/vue-tsc@2.2.12_typescript@5.8.3/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vue-tsc/bin/vue-tsc.js" "$@"
else
  exec node  "$basedir/../vue-tsc/bin/vue-tsc.js" "$@"
fi
