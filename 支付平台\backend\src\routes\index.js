const Router = require('koa-router');
const paymentRoutes = require('./payment');

const router = new Router();

// 健康检查接口
router.get('/health', async (ctx) => {
  ctx.body = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: require('../../package.json').version || '1.0.0'
  };
});

// API根路径
router.get('/api', async (ctx) => {
  ctx.body = {
    message: '蓝兔支付系统 API',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    endpoints: [
      'POST /api/payment/create - 创建支付订单',
      'GET /api/payment/query/:out_trade_no - 查询支付订单',
      'POST /api/payment/notify - 支付通知回调',
      'GET /api/payment/orders - 获取订单列表',
      'GET /api/payment/stats - 获取支付统计',
      'GET /api/payment/test - 测试接口'
    ]
  };
});

// 注册子路由
router.use(paymentRoutes.routes(), paymentRoutes.allowedMethods());

module.exports = router;
