const axios = require('axios');
const SignatureUtil = require('../utils/signature');
const logger = require('../utils/logger');

/**
 * 蓝兔支付服务类
 * 封装蓝兔支付API调用
 */
class LtzfService {
  constructor() {
    this.baseURL = process.env.LTZF_API_URL || 'https://api.ltzf.cn';
    this.mchId = process.env.LTZF_MCH_ID;
    this.apiKey = process.env.LTZF_API_KEY;
    
    // 创建axios实例
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
    
    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        logger.debug('蓝兔支付请求', {
          url: config.url,
          method: config.method,
          data: config.data
        });
        return config;
      },
      (error) => {
        logger.error('蓝兔支付请求错误', error);
        return Promise.reject(error);
      }
    );
    
    // 响应拦截器
    this.client.interceptors.response.use(
      (response) => {
        logger.debug('蓝兔支付响应', {
          status: response.status,
          data: response.data
        });
        return response;
      },
      (error) => {
        logger.error('蓝兔支付响应错误', error);
        return Promise.reject(error);
      }
    );
  }
  
  /**
   * 扫码支付
   * @param {Object} params - 支付参数
   * @returns {Promise<Object>} 支付结果
   */
  async nativePay(params) {
    try {
      const {
        out_trade_no,
        total_fee,
        body,
        notify_url,
        attach = '',
        time_expire = '30m',
        developer_appid = ''
      } = params;
      
      // 构建请求参数
      const requestParams = {
        mch_id: this.mchId,
        out_trade_no,
        total_fee: total_fee.toString(),
        body,
        timestamp: SignatureUtil.generateTimestamp(),
        notify_url,
        attach,
        time_expire,
        developer_appid
      };
      
      // 生成签名
      const signedParams = SignatureUtil.signParams(requestParams, this.apiKey);
      
      logger.payment('发起扫码支付请求', {
        out_trade_no,
        total_fee,
        body
      });
      
      // 发送请求
      const response = await this.client.post('/api/wxpay/native', 
        new URLSearchParams(signedParams)
      );
      
      const result = response.data;
      
      logger.payment('扫码支付响应', {
        out_trade_no,
        code: result.code,
        message: result.msg,
        request_id: result.request_id
      });
      
      return result;
      
    } catch (error) {
      logger.error('扫码支付请求失败', error);
      throw new Error(`支付请求失败: ${error.message}`);
    }
  }
  
  /**
   * 查询订单
   * @param {string} out_trade_no - 商户订单号
   * @returns {Promise<Object>} 查询结果
   */
  async queryOrder(out_trade_no) {
    try {
      // 构建请求参数
      const requestParams = {
        mch_id: this.mchId,
        out_trade_no,
        timestamp: SignatureUtil.generateTimestamp()
      };
      
      // 生成签名
      const signedParams = SignatureUtil.signParams(requestParams, this.apiKey);
      
      logger.payment('发起订单查询请求', { out_trade_no });
      
      // 发送请求
      const response = await this.client.post('/api/wxpay/get_pay_order',
        new URLSearchParams(signedParams)
      );
      
      const result = response.data;
      
      logger.payment('订单查询响应', {
        out_trade_no,
        code: result.code,
        message: result.msg,
        pay_status: result.data?.pay_status
      });
      
      return result;
      
    } catch (error) {
      logger.error('订单查询失败', error);
      throw new Error(`订单查询失败: ${error.message}`);
    }
  }
  
  /**
   * 验证支付通知签名
   * @param {Object} notifyData - 通知数据
   * @returns {boolean} 验证结果
   */
  verifyNotifySign(notifyData) {
    try {
      return SignatureUtil.verifySign(notifyData, this.apiKey);
    } catch (error) {
      logger.error('签名验证失败', error);
      return false;
    }
  }
  
  /**
   * 计算订单过期时间
   * @param {string} timeExpire - 过期时间字符串（如：30m, 2h）
   * @returns {Date} 过期时间
   */
  calculateExpireTime(timeExpire) {
    const now = new Date();
    const match = timeExpire.match(/^(\d+)([mh])$/);
    
    if (!match) {
      // 默认30分钟
      return new Date(now.getTime() + 30 * 60 * 1000);
    }
    
    const value = parseInt(match[1]);
    const unit = match[2];
    
    if (unit === 'm') {
      return new Date(now.getTime() + value * 60 * 1000);
    } else if (unit === 'h') {
      return new Date(now.getTime() + value * 60 * 60 * 1000);
    }
    
    return new Date(now.getTime() + 30 * 60 * 1000);
  }
  
  /**
   * 格式化金额（元转分）
   * @param {number} amount - 金额（元）
   * @returns {number} 金额（分）
   */
  static formatAmount(amount) {
    return Math.round(parseFloat(amount) * 100);
  }
  
  /**
   * 格式化金额（分转元）
   * @param {number} amount - 金额（分）
   * @returns {number} 金额（元）
   */
  static parseAmount(amount) {
    return parseFloat(amount) / 100;
  }
}

module.exports = LtzfService;
