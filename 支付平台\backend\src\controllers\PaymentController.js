const PaymentService = require('../services/PaymentService');
const ResponseUtil = require('../utils/response');
const logger = require('../utils/logger');

/**
 * 支付控制器
 * 处理支付相关的HTTP请求
 */
class PaymentController {
  constructor() {
    this.paymentService = new PaymentService();
  }
  
  /**
   * 创建支付订单
   */
  async createPayment(ctx) {
    try {
      const { amount, body, attach, time_expire, notify_url } = ctx.request.body;
      
      // 参数验证
      if (!amount || !body) {
        return ResponseUtil.badRequest(ctx, '缺少必要参数：amount, body');
      }
      
      if (isNaN(amount) || parseFloat(amount) <= 0) {
        return ResponseUtil.badRequest(ctx, '金额格式错误');
      }
      
      // 获取客户端信息
      const client_ip = ctx.request.ip || ctx.request.header['x-forwarded-for'] || ctx.request.socket.remoteAddress;
      const user_agent = ctx.request.header['user-agent'];
      
      const result = await this.paymentService.createPayment({
        amount: parseFloat(amount),
        body,
        attach,
        time_expire,
        notify_url,
        client_ip,
        user_agent
      });
      
      ResponseUtil.success(ctx, result, '支付订单创建成功');
      
    } catch (error) {
      logger.error('创建支付订单失败', error);
      ResponseUtil.error(ctx, error.message || '创建支付订单失败');
    }
  }
  
  /**
   * 查询支付订单
   */
  async queryPayment(ctx) {
    try {
      const { out_trade_no } = ctx.params;
      
      if (!out_trade_no) {
        return ResponseUtil.badRequest(ctx, '缺少订单号参数');
      }
      
      const result = await this.paymentService.queryPayment(out_trade_no);
      
      ResponseUtil.success(ctx, result, '查询成功');
      
    } catch (error) {
      logger.error('查询支付订单失败', error);
      ResponseUtil.error(ctx, error.message || '查询支付订单失败');
    }
  }
  
  /**
   * 处理支付通知
   */
  async handleNotify(ctx) {
    try {
      const notifyData = ctx.request.body;
      
      // 获取客户端信息
      const client_ip = ctx.request.ip || ctx.request.header['x-forwarded-for'] || ctx.request.socket.remoteAddress;
      const user_agent = ctx.request.header['user-agent'];
      
      logger.payment('收到支付通知请求', {
        client_ip,
        data: notifyData
      });
      
      const result = await this.paymentService.handlePaymentNotify(
        notifyData,
        client_ip,
        user_agent
      );
      
      // 返回处理结果给蓝兔支付
      ctx.body = result;
      
    } catch (error) {
      logger.error('处理支付通知失败', error);
      ctx.body = 'FAIL';
    }
  }
  
  /**
   * 获取订单列表
   */
  async getOrderList(ctx) {
    try {
      const {
        page = 1,
        pageSize = 10,
        pay_status,
        start_date,
        end_date,
        out_trade_no
      } = ctx.query;
      
      const result = await this.paymentService.getOrderList({
        page,
        pageSize,
        pay_status,
        start_date,
        end_date,
        out_trade_no
      });
      
      ResponseUtil.page(ctx, result.list, result.total, result.page, result.pageSize);
      
    } catch (error) {
      logger.error('获取订单列表失败', error);
      ResponseUtil.error(ctx, error.message || '获取订单列表失败');
    }
  }
  
  /**
   * 获取支付统计信息
   */
  async getPaymentStats(ctx) {
    try {
      const { start_date, end_date } = ctx.query;
      
      // 这里可以添加统计逻辑
      const stats = {
        total_orders: 0,
        paid_orders: 0,
        total_amount: 0,
        success_rate: 0
      };
      
      ResponseUtil.success(ctx, stats, '获取统计信息成功');
      
    } catch (error) {
      logger.error('获取支付统计失败', error);
      ResponseUtil.error(ctx, error.message || '获取支付统计失败');
    }
  }
  
  /**
   * 测试接口
   */
  async test(ctx) {
    try {
      const testData = {
        message: '支付系统运行正常',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      };
      
      ResponseUtil.success(ctx, testData, '测试成功');
      
    } catch (error) {
      logger.error('测试接口失败', error);
      ResponseUtil.error(ctx, error.message || '测试失败');
    }
  }
}

module.exports = PaymentController;
