const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 订单模型
 * 存储支付订单的基本信息
 */
const Order = sequelize.define('Order', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '订单ID'
  },
  
  // 商户订单号（唯一）
  out_trade_no: {
    type: DataTypes.STRING(64),
    allowNull: false,
    unique: true,
    comment: '商户订单号'
  },
  
  // 系统订单号（蓝兔支付返回）
  order_no: {
    type: DataTypes.STRING(64),
    allowNull: true,
    comment: '系统订单号'
  },
  
  // 支付订单号（微信/支付宝返回）
  pay_no: {
    type: DataTypes.STRING(64),
    allowNull: true,
    comment: '支付订单号'
  },
  
  // 订单金额（分）
  total_fee: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '订单金额（分）'
  },
  
  // 商品描述
  body: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '商品描述'
  },
  
  // 支付状态
  pay_status: {
    type: DataTypes.ENUM('pending', 'paid', 'failed', 'cancelled', 'expired'),
    defaultValue: 'pending',
    comment: '支付状态：pending-待支付，paid-已支付，failed-支付失败，cancelled-已取消，expired-已过期'
  },
  
  // 支付方式
  pay_channel: {
    type: DataTypes.ENUM('wxpay', 'alipay'),
    allowNull: true,
    comment: '支付渠道：wxpay-微信支付，alipay-支付宝'
  },
  
  // 支付类型
  trade_type: {
    type: DataTypes.ENUM('NATIVE', 'H5', 'APP', 'JSAPI', 'MINIPROGRAM'),
    allowNull: true,
    comment: '支付类型：NATIVE-扫码支付，H5-H5支付，APP-APP支付，JSAPI-公众号支付，MINIPROGRAM-小程序支付'
  },
  
  // 支付二维码链接
  code_url: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '微信原生支付链接'
  },
  
  // 二维码图片链接
  qrcode_url: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '二维码图片链接'
  },
  
  // 支付完成时间
  success_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '支付完成时间'
  },
  
  // 订单过期时间
  expire_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '订单过期时间'
  },
  
  // 附加数据
  attach: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '附加数据'
  },
  
  // 支付者信息
  openid: {
    type: DataTypes.STRING(128),
    allowNull: true,
    comment: '支付者openid'
  },
  
  // 通知地址
  notify_url: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '支付通知地址'
  },
  
  // 请求ID
  request_id: {
    type: DataTypes.STRING(64),
    allowNull: true,
    comment: '请求ID'
  },
  
  // 客户端IP
  client_ip: {
    type: DataTypes.STRING(45),
    allowNull: true,
    comment: '客户端IP地址'
  },
  
  // 用户代理
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '用户代理信息'
  }
}, {
  tableName: 'orders',
  comment: '支付订单表',
  indexes: [
    {
      fields: ['out_trade_no']
    },
    {
      fields: ['order_no']
    },
    {
      fields: ['pay_no']
    },
    {
      fields: ['pay_status']
    },
    {
      fields: ['created_at']
    }
  ]
});

module.exports = Order;
