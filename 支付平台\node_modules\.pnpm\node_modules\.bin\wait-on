#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/wait-on@8.0.3_debug@4.4.1/node_modules/wait-on/bin/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/wait-on@8.0.3_debug@4.4.1/node_modules/wait-on/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/wait-on@8.0.3_debug@4.4.1/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/wait-on@8.0.3_debug@4.4.1/node_modules/wait-on/bin/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/wait-on@8.0.3_debug@4.4.1/node_modules/wait-on/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/wait-on@8.0.3_debug@4.4.1/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../wait-on/bin/wait-on" "$@"
else
  exec node  "$basedir/../wait-on/bin/wait-on" "$@"
fi
