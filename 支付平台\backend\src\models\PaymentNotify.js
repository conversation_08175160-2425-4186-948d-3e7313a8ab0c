const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 支付通知记录模型
 * 记录所有支付通知的详细信息
 */
const PaymentNotify = sequelize.define('PaymentNotify', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '通知记录ID'
  },
  
  // 关联订单号
  out_trade_no: {
    type: DataTypes.STRING(64),
    allowNull: false,
    comment: '商户订单号'
  },
  
  // 系统订单号
  order_no: {
    type: DataTypes.STRING(64),
    allowNull: true,
    comment: '系统订单号'
  },
  
  // 支付订单号
  pay_no: {
    type: DataTypes.STRING(64),
    allowNull: true,
    comment: '支付订单号'
  },
  
  // 通知状态码
  code: {
    type: DataTypes.STRING(10),
    allowNull: false,
    comment: '通知状态码：0-成功，1-失败'
  },
  
  // 订单金额
  total_fee: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '订单金额'
  },
  
  // 商户号
  mch_id: {
    type: DataTypes.STRING(32),
    allowNull: true,
    comment: '商户号'
  },
  
  // 时间戳
  timestamp: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '通知时间戳'
  },
  
  // 签名
  sign: {
    type: DataTypes.STRING(64),
    allowNull: true,
    comment: '签名'
  },
  
  // 支付渠道
  pay_channel: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '支付渠道'
  },
  
  // 支付类型
  trade_type: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '支付类型'
  },
  
  // 支付完成时间
  success_time: {
    type: DataTypes.STRING(30),
    allowNull: true,
    comment: '支付完成时间'
  },
  
  // 附加数据
  attach: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '附加数据'
  },
  
  // 支付者信息
  openid: {
    type: DataTypes.STRING(128),
    allowNull: true,
    comment: '支付者openid'
  },
  
  // 原始通知数据
  raw_data: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '原始通知数据（JSON格式）'
  },
  
  // 签名验证结果
  sign_valid: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '签名验证结果'
  },
  
  // 处理状态
  process_status: {
    type: DataTypes.ENUM('pending', 'success', 'failed'),
    defaultValue: 'pending',
    comment: '处理状态：pending-待处理，success-处理成功，failed-处理失败'
  },
  
  // 处理结果
  process_result: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '处理结果描述'
  },
  
  // 处理时间
  process_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '处理时间'
  },
  
  // 客户端IP
  client_ip: {
    type: DataTypes.STRING(45),
    allowNull: true,
    comment: '通知来源IP'
  },
  
  // 用户代理
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '用户代理信息'
  }
}, {
  tableName: 'payment_notifies',
  comment: '支付通知记录表',
  indexes: [
    {
      fields: ['out_trade_no']
    },
    {
      fields: ['order_no']
    },
    {
      fields: ['pay_no']
    },
    {
      fields: ['process_status']
    },
    {
      fields: ['created_at']
    }
  ]
});

module.exports = PaymentNotify;
