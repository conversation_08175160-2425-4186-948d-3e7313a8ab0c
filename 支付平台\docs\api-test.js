/**
 * 蓝兔支付系统API测试脚本
 * 使用Node.js运行: node api-test.js
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:3000';
const client = axios.create({
  baseURL: BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 测试用例
class PaymentAPITest {
  constructor() {
    this.testResults = [];
  }

  // 记录测试结果
  logResult(testName, success, message, data = null) {
    const result = {
      test: testName,
      success,
      message,
      data,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    const status = success ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${testName}: ${message}`);
    
    if (data && success) {
      console.log('   Response:', JSON.stringify(data, null, 2));
    }
    console.log('');
  }

  // 测试健康检查
  async testHealthCheck() {
    try {
      const response = await client.get('/health');
      this.logResult(
        '健康检查',
        response.status === 200,
        `状态码: ${response.status}`,
        response.data
      );
      return response.data;
    } catch (error) {
      this.logResult(
        '健康检查',
        false,
        `请求失败: ${error.message}`
      );
      return null;
    }
  }

  // 测试API信息
  async testAPIInfo() {
    try {
      const response = await client.get('/api');
      this.logResult(
        'API信息',
        response.status === 200,
        `状态码: ${response.status}`,
        response.data
      );
      return response.data;
    } catch (error) {
      this.logResult(
        'API信息',
        false,
        `请求失败: ${error.message}`
      );
      return null;
    }
  }

  // 测试创建支付订单
  async testCreatePayment() {
    try {
      const paymentData = {
        amount: 0.01,
        body: '测试商品',
        attach: '测试数据',
        time_expire: '30m'
      };

      const response = await client.post('/api/payment/create', paymentData);
      
      const success = response.status === 200 && response.data.code === 0;
      this.logResult(
        '创建支付订单',
        success,
        success ? '订单创建成功' : `创建失败: ${response.data.message}`,
        response.data
      );
      
      return success ? response.data.data : null;
    } catch (error) {
      this.logResult(
        '创建支付订单',
        false,
        `请求失败: ${error.message}`
      );
      return null;
    }
  }

  // 测试查询支付订单
  async testQueryPayment(outTradeNo) {
    try {
      const response = await client.get(`/api/payment/query/${outTradeNo}`);
      
      const success = response.status === 200 && response.data.code === 0;
      this.logResult(
        '查询支付订单',
        success,
        success ? '查询成功' : `查询失败: ${response.data.message}`,
        response.data
      );
      
      return success ? response.data.data : null;
    } catch (error) {
      this.logResult(
        '查询支付订单',
        false,
        `请求失败: ${error.message}`
      );
      return null;
    }
  }

  // 测试获取订单列表
  async testGetOrderList() {
    try {
      const response = await client.get('/api/payment/orders?page=1&pageSize=5');
      
      const success = response.status === 200 && response.data.code === 0;
      this.logResult(
        '获取订单列表',
        success,
        success ? `获取成功，共${response.data.data.pagination.total}条记录` : `获取失败: ${response.data.message}`,
        response.data
      );
      
      return success ? response.data.data : null;
    } catch (error) {
      this.logResult(
        '获取订单列表',
        false,
        `请求失败: ${error.message}`
      );
      return null;
    }
  }

  // 测试获取支付统计
  async testGetPaymentStats() {
    try {
      const response = await client.get('/api/payment/stats');
      
      const success = response.status === 200 && response.data.code === 0;
      this.logResult(
        '获取支付统计',
        success,
        success ? '统计获取成功' : `获取失败: ${response.data.message}`,
        response.data
      );
      
      return success ? response.data.data : null;
    } catch (error) {
      this.logResult(
        '获取支付统计',
        false,
        `请求失败: ${error.message}`
      );
      return null;
    }
  }

  // 测试接口
  async testPaymentTest() {
    try {
      const response = await client.get('/api/payment/test');
      
      const success = response.status === 200 && response.data.code === 0;
      this.logResult(
        '测试接口',
        success,
        success ? '测试通过' : `测试失败: ${response.data.message}`,
        response.data
      );
      
      return success ? response.data.data : null;
    } catch (error) {
      this.logResult(
        '测试接口',
        false,
        `请求失败: ${error.message}`
      );
      return null;
    }
  }

  // 运行所有测试
  async runAllTests() {
    console.log('🚀 开始运行蓝兔支付系统API测试...\n');
    console.log(`📡 测试服务器: ${BASE_URL}\n`);

    // 1. 测试系统接口
    console.log('📋 测试系统接口:');
    await this.testHealthCheck();
    await this.testAPIInfo();

    // 2. 测试支付接口
    console.log('💰 测试支付接口:');
    
    // 创建订单
    const orderData = await this.testCreatePayment();
    
    // 如果订单创建成功，测试查询
    if (orderData && orderData.out_trade_no) {
      await this.testQueryPayment(orderData.out_trade_no);
    }
    
    // 测试订单列表
    await this.testGetOrderList();
    
    // 测试统计
    await this.testGetPaymentStats();
    
    // 测试接口
    await this.testPaymentTest();

    // 输出测试总结
    this.printSummary();
  }

  // 打印测试总结
  printSummary() {
    console.log('📊 测试总结:');
    console.log('=' * 50);
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests} ✅`);
    console.log(`失败: ${failedTests} ❌`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(2)}%`);
    
    if (failedTests > 0) {
      console.log('\n❌ 失败的测试:');
      this.testResults
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`  - ${r.test}: ${r.message}`);
        });
    }
    
    console.log('\n🎉 测试完成!');
  }
}

// 运行测试
if (require.main === module) {
  const tester = new PaymentAPITest();
  tester.runAllTests().catch(error => {
    console.error('❌ 测试运行失败:', error.message);
    process.exit(1);
  });
}

module.exports = PaymentAPITest;
