# 蓝兔支付系统 API 接口文档

## 基本信息

- **API 基础地址**: `http://localhost:3000`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **请求方式**: HTTP/HTTPS

## 通用响应格式

### 成功响应
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {},
  "timestamp": 1640995200000
}
```

### 错误响应
```json
{
  "code": 1,
  "message": "错误信息",
  "data": null,
  "timestamp": 1640995200000
}
```

### 分页响应
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "list": [],
    "pagination": {
      "total": 100,
      "page": 1,
      "pageSize": 10,
      "totalPages": 10
    }
  },
  "timestamp": 1640995200000
}
```

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1 | 业务错误 |
| 400 | 参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 系统接口

### 1. 健康检查

**接口地址**: `GET /health`

**接口描述**: 检查系统运行状态

**请求参数**: 无

**响应示例**:
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 3600,
  "memory": {
    "rss": 50331648,
    "heapTotal": 20971520,
    "heapUsed": 15728640,
    "external": 1048576
  },
  "version": "1.0.0"
}
```

### 2. API 信息

**接口地址**: `GET /api`

**接口描述**: 获取API基本信息和接口列表

**请求参数**: 无

**响应示例**:
```json
{
  "message": "蓝兔支付系统 API",
  "version": "1.0.0",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "endpoints": [
    "POST /api/payment/create - 创建支付订单",
    "GET /api/payment/query/:out_trade_no - 查询支付订单",
    "POST /api/payment/notify - 支付通知回调",
    "GET /api/payment/orders - 获取订单列表",
    "GET /api/payment/stats - 获取支付统计",
    "GET /api/payment/test - 测试接口"
  ]
}
```

## 支付接口

### 1. 创建支付订单

**接口地址**: `POST /api/payment/create`

**接口描述**: 创建新的支付订单，生成支付二维码

**请求参数**:
```json
{
  "amount": 0.01,
  "body": "测试商品",
  "attach": "附加数据",
  "time_expire": "30m",
  "notify_url": "http://localhost:3000/api/payment/notify"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| amount | Number | 是 | 支付金额（元），最小0.01 |
| body | String | 是 | 商品描述，最长128字符 |
| attach | String | 否 | 附加数据，最长127字符 |
| time_expire | String | 否 | 过期时间，格式：30m/2h，默认30m |
| notify_url | String | 否 | 支付通知地址，默认使用系统配置 |

**响应示例**:
```json
{
  "code": 0,
  "message": "支付订单创建成功",
  "data": {
    "out_trade_no": "PAY16409952000001234",
    "amount": 0.01,
    "code_url": "weixin://wxpay/bizpayurl?pr=abc123",
    "qrcode_url": "https://api.ltzf.cn/api/qrcode/abc123.png",
    "expire_time": "2024-01-01T00:30:00.000Z",
    "order_id": 1
  },
  "timestamp": 1640995200000
}
```

### 2. 查询支付订单

**接口地址**: `GET /api/payment/query/:out_trade_no`

**接口描述**: 根据商户订单号查询订单状态

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| out_trade_no | String | 是 | 商户订单号（路径参数） |

**响应示例**:
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "out_trade_no": "PAY16409952000001234",
    "order_no": "LT2024010100001",
    "pay_no": "4200001234567890123456789",
    "amount": 0.01,
    "body": "测试商品",
    "pay_status": "paid",
    "pay_channel": "wxpay",
    "trade_type": "NATIVE",
    "code_url": "weixin://wxpay/bizpayurl?pr=abc123",
    "qrcode_url": "https://api.ltzf.cn/api/qrcode/abc123.png",
    "success_time": "2024-01-01T00:15:00.000Z",
    "expire_time": "2024-01-01T00:30:00.000Z",
    "attach": "附加数据",
    "openid": "oUpF8uMuAJO_M2pxb1Q9zNjWeS6o",
    "created_at": "2024-01-01T00:00:00.000Z",
    "updated_at": "2024-01-01T00:15:00.000Z"
  },
  "timestamp": 1640995200000
}
```

**支付状态说明**:
| 状态值 | 说明 |
|--------|------|
| pending | 待支付 |
| paid | 已支付 |
| failed | 支付失败 |
| cancelled | 已取消 |
| expired | 已过期 |

### 3. 支付通知回调

**接口地址**: `POST /api/payment/notify`

**接口描述**: 蓝兔支付系统的支付通知回调接口（由蓝兔支付系统调用）

**请求参数**:
```json
{
  "code": "0",
  "out_trade_no": "PAY16409952000001234",
  "order_no": "LT2024010100001",
  "pay_no": "4200001234567890123456789",
  "total_fee": "1",
  "mch_id": "1684186147",
  "timestamp": "1640995200",
  "sign": "ABC123DEF456",
  "pay_channel": "wxpay",
  "trade_type": "NATIVE",
  "success_time": "2024-01-01 00:15:00",
  "attach": "附加数据",
  "openid": "oUpF8uMuAJO_M2pxb1Q9zNjWeS6o"
}
```

**响应格式**: 
- 成功: 返回字符串 `SUCCESS`
- 失败: 返回字符串 `FAIL`

### 4. 获取订单列表

**接口地址**: `GET /api/payment/orders`

**接口描述**: 分页获取订单列表，支持条件筛选

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | Number | 否 | 页码，默认1 |
| pageSize | Number | 否 | 每页数量，默认10 |
| pay_status | String | 否 | 支付状态筛选 |
| start_date | String | 否 | 开始日期，格式：YYYY-MM-DD |
| end_date | String | 否 | 结束日期，格式：YYYY-MM-DD |
| out_trade_no | String | 否 | 订单号搜索 |

**响应示例**:
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "list": [
      {
        "out_trade_no": "PAY16409952000001234",
        "order_no": "LT2024010100001",
        "pay_no": "4200001234567890123456789",
        "amount": 0.01,
        "body": "测试商品",
        "pay_status": "paid",
        "pay_channel": "wxpay",
        "trade_type": "NATIVE",
        "success_time": "2024-01-01T00:15:00.000Z",
        "created_at": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "total": 1,
      "page": 1,
      "pageSize": 10,
      "totalPages": 1
    }
  },
  "timestamp": 1640995200000
}
```

### 5. 获取支付统计

**接口地址**: `GET /api/payment/stats`

**接口描述**: 获取支付统计数据

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| start_date | String | 否 | 开始日期，格式：YYYY-MM-DD |
| end_date | String | 否 | 结束日期，格式：YYYY-MM-DD |

**响应示例**:
```json
{
  "code": 0,
  "message": "获取统计信息成功",
  "data": {
    "total_orders": 100,
    "paid_orders": 85,
    "total_amount": 1250.50,
    "success_rate": 85.0,
    "today_orders": 10,
    "today_amount": 125.50,
    "channel_stats": {
      "wxpay": {
        "orders": 80,
        "amount": 1000.00
      },
      "alipay": {
        "orders": 5,
        "amount": 250.50
      }
    }
  },
  "timestamp": 1640995200000
}
```

### 6. 测试接口

**接口地址**: `GET /api/payment/test`

**接口描述**: 测试接口连通性

**请求参数**: 无

**响应示例**:
```json
{
  "code": 0,
  "message": "测试成功",
  "data": {
    "message": "支付系统运行正常",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "version": "1.0.0"
  },
  "timestamp": 1640995200000
}
```

## 错误码说明

### 业务错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 1001 | 缺少必要参数 | 请求参数不完整 |
| 1002 | 参数格式错误 | 参数类型或格式不正确 |
| 1003 | 金额格式错误 | 金额必须大于0.01元 |
| 1004 | 订单不存在 | 指定的订单号不存在 |
| 1005 | 订单已过期 | 订单超过有效期 |
| 1006 | 订单已支付 | 订单状态为已支付 |
| 1007 | 签名验证失败 | 请求签名不正确 |
| 1008 | 支付创建失败 | 调用支付接口失败 |
| 1009 | 订单查询失败 | 查询订单状态失败 |
| 1010 | 数据库操作失败 | 数据库连接或操作异常 |

## 接口调用示例

### JavaScript (Axios)

```javascript
// 创建支付订单
const createPayment = async () => {
  try {
    const response = await axios.post('http://localhost:3000/api/payment/create', {
      amount: 0.01,
      body: '测试商品',
      attach: '测试数据'
    });

    console.log('订单创建成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('订单创建失败:', error.response.data);
  }
};

// 查询订单状态
const queryPayment = async (outTradeNo) => {
  try {
    const response = await axios.get(`http://localhost:3000/api/payment/query/${outTradeNo}`);
    console.log('订单查询成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('订单查询失败:', error.response.data);
  }
};

// 获取订单列表
const getOrderList = async (params = {}) => {
  try {
    const response = await axios.get('http://localhost:3000/api/payment/orders', {
      params: {
        page: 1,
        pageSize: 10,
        ...params
      }
    });

    console.log('订单列表:', response.data);
    return response.data;
  } catch (error) {
    console.error('获取订单列表失败:', error.response.data);
  }
};
```

### cURL

```bash
# 创建支付订单
curl -X POST http://localhost:3000/api/payment/create \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 0.01,
    "body": "测试商品",
    "attach": "测试数据"
  }'

# 查询订单状态
curl -X GET http://localhost:3000/api/payment/query/PAY16409952000001234

# 获取订单列表
curl -X GET "http://localhost:3000/api/payment/orders?page=1&pageSize=10&pay_status=paid"

# 获取支付统计
curl -X GET "http://localhost:3000/api/payment/stats?start_date=2024-01-01&end_date=2024-01-31"

# 测试接口
curl -X GET http://localhost:3000/api/payment/test
```

## 注意事项

1. **环境配置**: 确保正确配置数据库连接和蓝兔支付参数
2. **签名验证**: 支付通知回调会进行签名验证，确保数据安全
3. **错误处理**: 建议对所有API调用进行适当的错误处理
4. **超时设置**: 建议设置合理的请求超时时间（30秒）
5. **重试机制**: 对于网络异常，建议实现重试机制
6. **日志记录**: 系统会自动记录所有支付相关的操作日志
7. **数据库**: 确保MySQL数据库正常运行并已创建相应的表结构

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持微信扫码支付
- 支持订单查询和管理
- 支持支付通知处理
- 支持统计数据查询
