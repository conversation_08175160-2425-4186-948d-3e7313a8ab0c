查询订单API
商户可以通过查询订单接口主动查询订单状态，完成下一步的业务逻辑。
注意：

此接口请求频率限制：1qps/5s，即5秒钟请求限制为1次。
特别提醒：调用接口前请先在控制台微信支付商户管理修改设置白名单IP。

接口说明
适用对象：个人、个体户、企业

请求URL：https://api.ltzf.cn/api/wxpay/get_pay_order

请求方式：POST

请求参数
参数名	参数类型	必填	描述
mch_id	String	是	商户号
示例值：1230000109
out_trade_no	String	是	商户订单号
示例值：LTZF2022112264463
timestamp	String	是	当前时间戳
示例值：1669518774
sign	String	是	签名，数据签名的算法请参考《签名算法》。
示例值：4440B462E792B604BD56A37EA41E5B8F
请求示例代码

PHP
Java
Python
C#
Go
JavaScript
Node
R
const unirest = require("unirest");

const req = unirest("POST", "https://api.ltzf.cn/api/wxpay/get_pay_order");

req.headers({
  "content-type": "application/x-www-form-urlencoded"
});

req.form({
  "mch_id": "1230000109",
  "out_trade_no": "LTZF2022112264463",
  "timestamp": "1669518774",
  "sign": "4440B462E792B604BD56A37EA41E5B8F"
});

req.end(function (res) {
  if (res.error) throw new Error(res.error);

  console.log(res.body);
});
返回参数
成功参数

参数名	参数类型	必填	描述
code	Integer	是	返回状态，枚举值：
0：成功
1：失败
示例值：0
data	Object	是	返回数据
data.add_time	String	是	下单时间
示例值：2022-11-22 11:55:09
data.mch_id	String	是	商户号
示例值：1230000109
data.order_no	String	是	系统订单号
示例值：WX202211221155084844072633
data.out_trade_no	String	是	商户订单号
示例值：LTZF2022112264463
data.pay_no	String	否	微信支付订单号，当支付状态为已支付时返回此参数。
示例值：4200001635202211222291508463
data.body	String	是	商品描述
示例值：Image形象店-深圳腾大-QQ公仔
data.total_fee	String	是	支付金额
示例值：0.01
data.trade_type	String	是	支付类型，枚举值：
NATIVE：扫码支付
H5：H5支付
APP：APP支付
JSAPI：公众号支付
MINIPROGRAM：小程序支付
示例值：NATIVE
data.success_time	String	否	支付完成时间，当支付状态为已支付时返回此参数。
示例值：2022-11-22 11:55:42
data.attach	String	是	附加数据，在支付接口中填写的数据，可作为自定义参数使用。
示例值：自定义数据
data.openid	String	否	支付者信息，当支付状态为已支付时返回此参数。
示例值：o5wq46GAKVxVKpsdcI4aU4cBpgT0
data.pay_status	Integer	是	支付状态，枚举值：
0：未支付
1：已支付
示例值：1
msg	String	是	消息
示例值：查询成功
request_id	String	是	唯一请求ID，每次请求都会返回，定位问题时需要提供该次请求的request_id。
示例值：1bc65e47-77c3-b68a-c37c-b56a97d18d24
成功示例

{
    "code": 0, 
    "data": {
        "add_time": "2022-11-22 11:55:09", 
        "mch_id": "1230000109", 
        "order_no": "WX202211221155084844072633", 
        "out_trade_no": "LTZF2022112264463", 
        "pay_no": "4200001635202211222291508463", 
        "body": "Image形象店-深圳腾大-QQ公仔", 
        "total_fee": "0.01", 
        "trade_type": "NATIVE", 
        "success_time": "2022-11-22 11:55:42", 
        "attach": "自定义数据", 
        "openid": "o5wq46GAKVxVKpsdcI4aU4cBpgT0", 
        "pay_status": 1
    }, 
    "msg": "查询成功", 
    "request_id": "1bc65e47-77c3-b68a-c37c-b56a97d18d24"
}
失败参数

参数名	参数类型	必填	描述
code	Integer	是	返回状态，枚举值：
0：成功
1：失败
示例值：1
msg	String	是	消息
示例值：商户订单号不存在
request_id	String	是	唯一请求ID，每次请求都会返回，定位问题时需要提供该次请求的request_id。
示例值：f46b1932-7f95-b728-7e0c-17663f7a3c7e
失败示例

{
    "code": 1, 
    "msg": "商户订单号不存在", 
    "request_id": "f46b1932-7f95-b728-7e0c-17663f7a3c7e"
}