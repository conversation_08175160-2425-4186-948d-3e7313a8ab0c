const fs = require('fs');
const path = require('path');

/**
 * 简单的日志工具类
 * 提供基本的日志记录功能
 */
class Logger {
  constructor() {
    this.logDir = path.join(__dirname, '../../logs');
    this.ensureLogDir();
  }
  
  /**
   * 确保日志目录存在
   */
  ensureLogDir() {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }
  
  /**
   * 格式化日志消息
   * @param {string} level - 日志级别
   * @param {string} message - 日志消息
   * @param {*} data - 附加数据
   * @returns {string} 格式化后的日志
   */
  formatMessage(level, message, data = null) {
    const timestamp = new Date().toISOString();
    const dataStr = data ? ` | Data: ${JSON.stringify(data)}` : '';
    return `[${timestamp}] [${level.toUpperCase()}] ${message}${dataStr}\n`;
  }
  
  /**
   * 写入日志文件
   * @param {string} filename - 文件名
   * @param {string} content - 日志内容
   */
  writeToFile(filename, content) {
    const filePath = path.join(this.logDir, filename);
    fs.appendFileSync(filePath, content, 'utf8');
  }
  
  /**
   * 获取日期字符串
   * @returns {string} YYYY-MM-DD格式的日期
   */
  getDateString() {
    return new Date().toISOString().split('T')[0];
  }
  
  /**
   * 信息日志
   * @param {string} message - 日志消息
   * @param {*} data - 附加数据
   */
  info(message, data = null) {
    const logMessage = this.formatMessage('info', message, data);
    console.log(logMessage.trim());
    this.writeToFile(`app-${this.getDateString()}.log`, logMessage);
  }
  
  /**
   * 错误日志
   * @param {string} message - 日志消息
   * @param {*} error - 错误对象
   */
  error(message, error = null) {
    const errorData = error ? {
      message: error.message,
      stack: error.stack,
      ...error
    } : null;
    
    const logMessage = this.formatMessage('error', message, errorData);
    console.error(logMessage.trim());
    this.writeToFile(`error-${this.getDateString()}.log`, logMessage);
  }
  
  /**
   * 警告日志
   * @param {string} message - 日志消息
   * @param {*} data - 附加数据
   */
  warn(message, data = null) {
    const logMessage = this.formatMessage('warn', message, data);
    console.warn(logMessage.trim());
    this.writeToFile(`app-${this.getDateString()}.log`, logMessage);
  }
  
  /**
   * 调试日志
   * @param {string} message - 日志消息
   * @param {*} data - 附加数据
   */
  debug(message, data = null) {
    if (process.env.NODE_ENV === 'development') {
      const logMessage = this.formatMessage('debug', message, data);
      console.debug(logMessage.trim());
      this.writeToFile(`debug-${this.getDateString()}.log`, logMessage);
    }
  }
  
  /**
   * 支付相关日志
   * @param {string} message - 日志消息
   * @param {*} data - 附加数据
   */
  payment(message, data = null) {
    const logMessage = this.formatMessage('payment', message, data);
    console.log(logMessage.trim());
    this.writeToFile(`payment-${this.getDateString()}.log`, logMessage);
  }
}

// 创建全局日志实例
const logger = new Logger();

module.exports = logger;
