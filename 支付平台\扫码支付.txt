扫码支付API
蓝兔支付后台系统返回二维码地址和微信原生的支付链接，商户可自行使用原生链接生成二维码图片，用户使用微信客户端扫码后发起支付。
注意：

此接口不支持通过从相册识别二维码，如果需要通过相册识别二维码，请使用《公众号支付便捷版》支付接口。
特别提醒：调用接口前请先在控制台微信支付商户管理修改设置白名单域名和白名单IP。

接口说明
适用对象：个人、个体户、企业

请求URL：https://api.ltzf.cn/api/wxpay/native

请求方式：POST

请求参数
参数名	参数类型	必填	描述
mch_id	String	是	商户号
示例值：1230000109
out_trade_no	String	是	商户订单号，只能是数字、大小写字母_-且在同一个商户号下唯一。
示例值：LTZF2022113023096
total_fee	String	是	支付金额
示例值：0.01
body	String	是	商品描述
示例值：Image形象店-深圳腾大-QQ公仔
timestamp	String	是	当前时间戳
示例值：1669533132
notify_url	String	是	支付通知地址，通知URL必须为直接可访问的URL，不允许携带查询串，要求必须为http或https地址，回调通知参数请参考《支付通知》。
示例值：https://www.weixin.qq.com/wxpay/pay.php
attach	String	否	附加数据，在支付通知中原样返回，可作为自定义参数使用。
示例值：自定义数据
time_expire	String	否	订单失效时间，枚举值：
m：分钟
h：小时
取值范围：1m～2h（接口请求后开始计算时间）
示例值：5m
developer_appid	String	否	开发者应用ID
示例值：1041589049015120
sign	String	是	签名，数据签名的算法请参考《签名算法》。
示例值：B7337098E280841EB5F4D28261B60C07
请求示例代码

PHP
Java
Python
C#
Go
JavaScript
Node
R
const unirest = require("unirest");

const req = unirest("POST", "https://api.ltzf.cn/api/wxpay/native");

req.headers({
  "content-type": "application/x-www-form-urlencoded"
});

req.form({
  "mch_id": "1230000109",
  "out_trade_no": "LTZF2022113023096",
  "total_fee": "0.01",
  "body": "Image形象店-深圳腾大-QQ公仔",
  "timestamp": "1669533132",
  "notify_url": "https://www.weixin.qq.com/wxpay/pay.php",
  "attach": "自定义数据",
  "time_expire": "5m",
  "developer_appid": "1041589049015120",
  "sign": "B7337098E280841EB5F4D28261B60C07"
});

req.end(function (res) {
  if (res.error) throw new Error(res.error);

  console.log(res.body);
});
返回参数
成功参数

参数名	参数类型	必填	描述
code	Integer	是	返回状态，枚举值：
0：成功
1：失败
示例值：0
data	Object	是	返回数据
data.code_url	String	是	微信原生支付链接，此URL用于生成支付二维码，然后提供给用户扫码支付。
示例值：weixin://wxpay/bizpayurl?pr=i8SfEeFzz
data.QRcode_url	String	是	蓝兔支付生成的二维码链接地址
示例值：https://api.ltzf.cn/uploads/QRcode/wxpay/1667888007846.png
msg	String	是	消息
示例值：微信Native下单成功
request_id	String	是	唯一请求ID，每次请求都会返回，定位问题时需要提供该次请求的request_id。
示例值：8fbbcb94-94f0-dcfa-3b25-892b45579b12
成功示例

{
    "code": 0, 
    "data": {
        "code_url": "weixin://wxpay/bizpayurl?pr=i8SfEeFzz", 
        "QRcode_url": "https://api.ltzf.cn/uploads/QRcode/wxpay/1667888007846.png"
    }, 
    "msg": "微信Native下单成功", 
    "request_id": "8fbbcb94-94f0-dcfa-3b25-892b45579b12"
}
失败参数

参数名	参数类型	必填	描述
code	Integer	是	返回状态，枚举值：
0：成功
1：失败
示例值：1
msg	String	是	消息
示例值：签名错误
request_id	String	是	唯一请求ID，每次请求都会返回，定位问题时需要提供该次请求的request_id。
示例值：d42ad311-989f-ba93-c8a6-f4520f6d8169
失败示例

{
    "code": 1, 
    "msg": "签名错误", 
    "request_id": "d42ad311-989f-ba93-c8a6-f4520f6d8169"
}