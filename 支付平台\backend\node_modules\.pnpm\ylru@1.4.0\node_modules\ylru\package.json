{"name": "ylru", "description": "Extends LRU base on hashlru", "version": "1.4.0", "homepage": "https://github.com/node-modules/ylru", "repository": {"type": "git", "url": "git://github.com/node-modules/ylru.git"}, "dependencies": {}, "devDependencies": {"@types/node": "^12.0.8", "beautify-benchmark": "^0.2.4", "benchmark": "^2.1.3", "egg-bin": "^1.10.0", "egg-ci": "^1.19.0", "eslint": "^4.19.1", "eslint-config-egg": "^6.0.0", "git-contributor": "^1.0.10", "hashlru": "^1.0.3", "ko-sleep": "^1.0.2", "lru-cache": "^4.0.2", "runscript": "^1.5.2", "typescript": "^4.6.2"}, "main": "index.js", "files": ["index.js", "index.d.ts"], "scripts": {"contributor": "git-contributor", "lint": "eslint test *.js", "test": "npm run lint -- --fix && npm run test-local", "test-local": "egg-bin test", "cov": "egg-bin cov", "ci": "npm run lint && npm run cov"}, "author": "fengmk2", "engines": {"node": ">= 4.0.0"}, "ci": {"version": "8, 10, 12, 14, 16", "type": "github", "os": {"github": "linux"}, "npminstall": false}, "license": "MIT"}