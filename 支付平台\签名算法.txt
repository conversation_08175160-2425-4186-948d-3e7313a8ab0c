签名算法
签名生成的通用步骤如下：
设所有发送或者接收到的数据为集合M，将集合M内非空参数值的参数按照参数名ASCII码从小到大排序（字典序），使用URL键值对的格式（即key1=value1&key2=value2…）拼接成字符串stringA。
在stringA最后拼接上 &key=密钥 得到stringSignTemp字符串，并对stringSignTemp进行MD5运算，再将得到的字符串所有字符转换为大写，得到sign值。
注意：只有必填参数才参与签名！！！

注意：只有必填参数才参与签名！！！

注意：只有必填参数才参与签名！！！

温馨提示：蓝兔支付签名算法与微信支付V2签名算法一致，签名校验工具 >>点击此处。

JS示例代码：
function wxPaySign(params, key) {
    const paramsArr = Object.keys(params);
    paramsArr.sort();
    const stringArr = [];
    paramsArr.map(key => {
        stringArr.push(key + '=' + params[key]);
    });
    // 最后加上商户Key
    stringArr.push("key=" + key);
    const string = stringArr.join('&');
    return md5(string).toString().toUpperCase();
}