const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 商户配置模型
 * 存储商户的基本信息和支付配置
 */
const Merchant = sequelize.define('Merchant', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '商户ID'
  },
  
  // 商户号
  mch_id: {
    type: DataTypes.STRING(32),
    allowNull: false,
    unique: true,
    comment: '商户号'
  },
  
  // 商户名称
  mch_name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '商户名称'
  },
  
  // API密钥
  api_key: {
    type: DataTypes.STRING(64),
    allowNull: false,
    comment: 'API密钥'
  },
  
  // 商户状态
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'suspended'),
    defaultValue: 'active',
    comment: '商户状态：active-正常，inactive-停用，suspended-暂停'
  },
  
  // 支持的支付方式
  supported_channels: {
    type: DataTypes.JSON,
    defaultValue: ['wxpay'],
    comment: '支持的支付渠道'
  },
  
  // 支持的支付类型
  supported_types: {
    type: DataTypes.JSON,
    defaultValue: ['NATIVE'],
    comment: '支持的支付类型'
  },
  
  // 默认通知地址
  default_notify_url: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '默认通知地址'
  },
  
  // 白名单IP
  whitelist_ips: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'IP白名单'
  },
  
  // 费率配置
  rate_config: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '费率配置'
  },
  
  // 联系人信息
  contact_name: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '联系人姓名'
  },
  
  contact_phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '联系电话'
  },
  
  contact_email: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '联系邮箱'
  },
  
  // 备注信息
  remark: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '备注信息'
  },
  
  // 最后登录时间
  last_login_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '最后登录时间'
  },
  
  // 最后登录IP
  last_login_ip: {
    type: DataTypes.STRING(45),
    allowNull: true,
    comment: '最后登录IP'
  }
}, {
  tableName: 'merchants',
  comment: '商户配置表',
  indexes: [
    {
      fields: ['mch_id']
    },
    {
      fields: ['status']
    },
    {
      fields: ['created_at']
    }
  ]
});

module.exports = Merchant;
