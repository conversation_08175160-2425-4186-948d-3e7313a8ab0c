const Koa = require('koa');
const bodyParser = require('koa-bodyparser');
const cors = require('koa-cors');
const koaStatic = require('koa-static');
const path = require('path');
require('dotenv').config();

const router = require('./src/routes');
const { initDatabase, testConnection } = require('./src/config/database');
const logger = require('./src/utils/logger');
const ResponseUtil = require('./src/utils/response');

const app = new Koa();

// 错误处理中间件
app.use(async (ctx, next) => {
  try {
    await next();
  } catch (error) {
    logger.error('请求处理异常', {
      url: ctx.url,
      method: ctx.method,
      error: error.message,
      stack: error.stack
    });
    
    ResponseUtil.serverError(ctx, '服务器内部错误', error);
  }
});

// 请求日志中间件
app.use(async (ctx, next) => {
  const start = Date.now();
  
  logger.info(`请求开始: ${ctx.method} ${ctx.url}`, {
    method: ctx.method,
    url: ctx.url,
    ip: ctx.request.ip,
    userAgent: ctx.request.header['user-agent']
  });
  
  await next();
  
  const duration = Date.now() - start;
  
  logger.info(`请求完成: ${ctx.method} ${ctx.url} - ${ctx.status} - ${duration}ms`, {
    method: ctx.method,
    url: ctx.url,
    status: ctx.status,
    duration
  });
});

// CORS中间件
app.use(cors({
  origin: function(ctx) {
    // 在生产环境中应该配置具体的域名
    return ctx.header.origin || '*';
  },
  credentials: true,
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization', 'Accept', 'X-Requested-With']
}));

// 请求体解析中间件
app.use(bodyParser({
  enableTypes: ['json', 'form', 'text'],
  jsonLimit: '10mb',
  formLimit: '10mb',
  textLimit: '10mb'
}));

// 静态文件服务
app.use(koaStatic(path.join(__dirname, 'public')));

// 路由中间件
app.use(router.routes());
app.use(router.allowedMethods());

// 404处理
app.use(async (ctx) => {
  ResponseUtil.notFound(ctx, '接口不存在');
});

// 启动服务器
const PORT = process.env.PORT || 3000;

async function startServer() {
  try {
    // 测试数据库连接
    await testConnection();
    
    // 初始化数据库
    await initDatabase();
    
    // 启动HTTP服务器
    app.listen(PORT, () => {
      logger.info(`服务器启动成功`, {
        port: PORT,
        env: process.env.NODE_ENV || 'development',
        pid: process.pid
      });
      
      console.log(`
╔══════════════════════════════════════╗
║        蓝兔支付系统后端服务          ║
║                                      ║
║  服务地址: http://localhost:${PORT}     ║
║  环境模式: ${(process.env.NODE_ENV || 'development').padEnd(10)} ║
║  进程ID:   ${process.pid.toString().padEnd(10)} ║
║                                      ║
║  API文档: http://localhost:${PORT}/api  ║
║  健康检查: http://localhost:${PORT}/health ║
╚══════════════════════════════════════╝
      `);
    });
    
  } catch (error) {
    logger.error('服务器启动失败', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('收到SIGTERM信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('收到SIGINT信号，正在关闭服务器...');
  process.exit(0);
});

// 未捕获异常处理
process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的Promise拒绝', { reason, promise });
  process.exit(1);
});

// 启动服务器
startServer();

module.exports = app;
