[2025-07-08T07:17:17.982Z] [ERROR] 服务器启动失败 | Data: {"message":"initDatabase is not a function","stack":"TypeError: initDatabase is not a function\n    at startServer (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\app.js:94:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}
[2025-07-08T07:20:08.433Z] [ERROR] 创建支付订单失败 | Data: {"message":"签名错误","stack":"Error: 签名错误\n    at PaymentService.createPayment (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\src\\services\\PaymentService.js:52:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.createPayment (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\src\\controllers\\PaymentController.js:34:22)\n    at async serve (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\node_modules\\.pnpm\\koa-static@5.0.0\\node_modules\\koa-static\\index.js:53:9)\n    at async bodyParser (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\node_modules\\.pnpm\\koa-bodyparser@4.4.1\\node_modules\\koa-bodyparser\\index.js:78:5)"}
[2025-07-08T07:20:08.435Z] [ERROR] 创建支付订单失败 | Data: {"message":"签名错误","stack":"Error: 签名错误\n    at PaymentService.createPayment (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\src\\services\\PaymentService.js:52:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.createPayment (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\src\\controllers\\PaymentController.js:34:22)\n    at async serve (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\node_modules\\.pnpm\\koa-static@5.0.0\\node_modules\\koa-static\\index.js:53:9)\n    at async bodyParser (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\node_modules\\.pnpm\\koa-bodyparser@4.4.1\\node_modules\\koa-bodyparser\\index.js:78:5)"}
[2025-07-08T07:28:06.670Z] [ERROR] 创建支付订单失败 | Data: {"message":"签名错误","stack":"Error: 签名错误\n    at PaymentService.createPayment (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\src\\services\\PaymentService.js:52:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.createPayment (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\src\\controllers\\PaymentController.js:34:22)\n    at async serve (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\node_modules\\.pnpm\\koa-static@5.0.0\\node_modules\\koa-static\\index.js:53:9)\n    at async bodyParser (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\node_modules\\.pnpm\\koa-bodyparser@4.4.1\\node_modules\\koa-bodyparser\\index.js:78:5)"}
[2025-07-08T07:28:06.670Z] [ERROR] 创建支付订单失败 | Data: {"message":"签名错误","stack":"Error: 签名错误\n    at PaymentService.createPayment (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\src\\services\\PaymentService.js:52:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.createPayment (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\src\\controllers\\PaymentController.js:34:22)\n    at async serve (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\node_modules\\.pnpm\\koa-static@5.0.0\\node_modules\\koa-static\\index.js:53:9)\n    at async bodyParser (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\node_modules\\.pnpm\\koa-bodyparser@4.4.1\\node_modules\\koa-bodyparser\\index.js:78:5)"}
[2025-07-08T07:47:51.866Z] [ERROR] 创建支付订单失败 | Data: {"message":"签名错误","stack":"Error: 签名错误\n    at PaymentService.createPayment (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\src\\services\\PaymentService.js:52:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.createPayment (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\src\\controllers\\PaymentController.js:34:22)\n    at async serve (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\node_modules\\.pnpm\\koa-static@5.0.0\\node_modules\\koa-static\\index.js:53:9)\n    at async bodyParser (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\node_modules\\.pnpm\\koa-bodyparser@4.4.1\\node_modules\\koa-bodyparser\\index.js:78:5)"}
[2025-07-08T07:47:51.867Z] [ERROR] 创建支付订单失败 | Data: {"message":"签名错误","stack":"Error: 签名错误\n    at PaymentService.createPayment (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\src\\services\\PaymentService.js:52:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.createPayment (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\src\\controllers\\PaymentController.js:34:22)\n    at async serve (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\node_modules\\.pnpm\\koa-static@5.0.0\\node_modules\\koa-static\\index.js:53:9)\n    at async bodyParser (C:\\Users\\<USER>\\Desktop\\支付平台\\backend\\node_modules\\.pnpm\\koa-bodyparser@4.4.1\\node_modules\\koa-bodyparser\\index.js:78:5)"}
