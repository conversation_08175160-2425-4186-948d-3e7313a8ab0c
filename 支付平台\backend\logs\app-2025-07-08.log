[2025-07-08T07:17:46.935Z] [INFO] 服务器启动成功 | Data: {"port":"3000","env":"development","pid":28924}
[2025-07-08T07:18:42.660Z] [INFO] 请求开始: GET / | Data: {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
[2025-07-08T07:18:42.666Z] [INFO] 请求完成: GET / - 404 - 6ms | Data: {"method":"GET","url":"/","status":404,"duration":6}
[2025-07-08T07:18:42.683Z] [INFO] 请求开始: GET / | Data: {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
[2025-07-08T07:18:42.686Z] [INFO] 请求完成: GET / - 404 - 3ms | Data: {"method":"GET","url":"/","status":404,"duration":3}
[2025-07-08T07:18:42.723Z] [INFO] 请求开始: GET /favicon.ico | Data: {"method":"GET","url":"/favicon.ico","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
[2025-07-08T07:18:42.725Z] [INFO] 请求完成: GET /favicon.ico - 404 - 3ms | Data: {"method":"GET","url":"/favicon.ico","status":404,"duration":3}
[2025-07-08T07:18:42.727Z] [INFO] 请求开始: GET /.well-known/appspecific/com.chrome.devtools.json | Data: {"method":"GET","url":"/.well-known/appspecific/com.chrome.devtools.json","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
[2025-07-08T07:18:42.728Z] [INFO] 请求完成: GET /.well-known/appspecific/com.chrome.devtools.json - 404 - 1ms | Data: {"method":"GET","url":"/.well-known/appspecific/com.chrome.devtools.json","status":404,"duration":1}
[2025-07-08T07:18:47.045Z] [INFO] 请求开始: GET / | Data: {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
[2025-07-08T07:18:47.047Z] [INFO] 请求完成: GET / - 404 - 2ms | Data: {"method":"GET","url":"/","status":404,"duration":2}
[2025-07-08T07:18:47.073Z] [INFO] 请求开始: GET /.well-known/appspecific/com.chrome.devtools.json | Data: {"method":"GET","url":"/.well-known/appspecific/com.chrome.devtools.json","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
[2025-07-08T07:18:47.075Z] [INFO] 请求完成: GET /.well-known/appspecific/com.chrome.devtools.json - 404 - 2ms | Data: {"method":"GET","url":"/.well-known/appspecific/com.chrome.devtools.json","status":404,"duration":2}
[2025-07-08T07:18:50.359Z] [INFO] 请求开始: GET /api | Data: {"method":"GET","url":"/api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
[2025-07-08T07:18:50.362Z] [INFO] 请求完成: GET /api - 200 - 3ms | Data: {"method":"GET","url":"/api","status":200,"duration":3}
[2025-07-08T07:20:08.170Z] [INFO] 请求开始: OPTIONS /api/payment/create | Data: {"method":"OPTIONS","url":"/api/payment/create","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
[2025-07-08T07:20:08.172Z] [INFO] 请求完成: OPTIONS /api/payment/create - 204 - 2ms | Data: {"method":"OPTIONS","url":"/api/payment/create","status":204,"duration":2}
[2025-07-08T07:20:08.175Z] [INFO] 请求开始: POST /api/payment/create | Data: {"method":"POST","url":"/api/payment/create","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
[2025-07-08T07:20:08.437Z] [INFO] 请求完成: POST /api/payment/create - 200 - 262ms | Data: {"method":"POST","url":"/api/payment/create","status":200,"duration":262}
[2025-07-08T07:23:25.714Z] [INFO] 请求开始: GET /health | Data: {"method":"GET","url":"/health","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202"}
[2025-07-08T07:23:25.716Z] [INFO] 请求完成: GET /health - 200 - 2ms | Data: {"method":"GET","url":"/health","status":200,"duration":2}
[2025-07-08T07:23:47.144Z] [INFO] 请求开始: GET /api | Data: {"method":"GET","url":"/api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202"}
[2025-07-08T07:23:47.146Z] [INFO] 请求完成: GET /api - 200 - 2ms | Data: {"method":"GET","url":"/api","status":200,"duration":2}
[2025-07-08T07:28:06.475Z] [INFO] 请求开始: POST /api/payment/create | Data: {"method":"POST","url":"/api/payment/create","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202"}
[2025-07-08T07:28:06.671Z] [INFO] 请求完成: POST /api/payment/create - 200 - 196ms | Data: {"method":"POST","url":"/api/payment/create","status":200,"duration":196}
[2025-07-08T07:46:07.851Z] [INFO] 请求开始: GET /api/payment/test | Data: {"method":"GET","url":"/api/payment/test","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202"}
[2025-07-08T07:46:07.852Z] [INFO] 请求完成: GET /api/payment/test - 200 - 1ms | Data: {"method":"GET","url":"/api/payment/test","status":200,"duration":1}
[2025-07-08T07:47:51.711Z] [INFO] 请求开始: OPTIONS /api/payment/create | Data: {"method":"OPTIONS","url":"/api/payment/create","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
[2025-07-08T07:47:51.712Z] [INFO] 请求完成: OPTIONS /api/payment/create - 204 - 1ms | Data: {"method":"OPTIONS","url":"/api/payment/create","status":204,"duration":1}
[2025-07-08T07:47:51.714Z] [INFO] 请求开始: POST /api/payment/create | Data: {"method":"POST","url":"/api/payment/create","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
[2025-07-08T07:47:51.868Z] [INFO] 请求完成: POST /api/payment/create - 200 - 154ms | Data: {"method":"POST","url":"/api/payment/create","status":200,"duration":154}
[2025-07-08T07:48:00.098Z] [INFO] 收到SIGINT信号，正在关闭服务器...
