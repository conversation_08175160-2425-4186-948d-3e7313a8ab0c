const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/codeMirrorModule-DRsk21vu.js","assets/codeMirrorModule-C3UTv-Ge.css"])))=>i.map(i=>d[i]);
(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))l(o);new MutationObserver(o=>{for(const u of o)if(u.type==="childList")for(const f of u.addedNodes)f.tagName==="LINK"&&f.rel==="modulepreload"&&l(f)}).observe(document,{childList:!0,subtree:!0});function r(o){const u={};return o.integrity&&(u.integrity=o.integrity),o.referrerPolicy&&(u.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?u.credentials="include":o.crossOrigin==="anonymous"?u.credentials="omit":u.credentials="same-origin",u}function l(o){if(o.ep)return;o.ep=!0;const u=r(o);fetch(o.href,u)}})();function Ym(i){return i&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}var Ea={exports:{}},Ns={},Na={exports:{}},ae={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sd;function Gm(){if(sd)return ae;sd=1;var i=Symbol.for("react.element"),n=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),f=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),x=Symbol.for("react.lazy"),S=Symbol.iterator;function T(C){return C===null||typeof C!="object"?null:(C=S&&C[S]||C["@@iterator"],typeof C=="function"?C:null)}var E={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},O=Object.assign,v={};function w(C,D,oe){this.props=C,this.context=D,this.refs=v,this.updater=oe||E}w.prototype.isReactComponent={},w.prototype.setState=function(C,D){if(typeof C!="object"&&typeof C!="function"&&C!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,C,D,"setState")},w.prototype.forceUpdate=function(C){this.updater.enqueueForceUpdate(this,C,"forceUpdate")};function I(){}I.prototype=w.prototype;function A(C,D,oe){this.props=C,this.context=D,this.refs=v,this.updater=oe||E}var F=A.prototype=new I;F.constructor=A,O(F,w.prototype),F.isPureReactComponent=!0;var V=Array.isArray,W=Object.prototype.hasOwnProperty,j={current:null},Y={key:!0,ref:!0,__self:!0,__source:!0};function z(C,D,oe){var ce,de={},he=null,we=null;if(D!=null)for(ce in D.ref!==void 0&&(we=D.ref),D.key!==void 0&&(he=""+D.key),D)W.call(D,ce)&&!Y.hasOwnProperty(ce)&&(de[ce]=D[ce]);var me=arguments.length-2;if(me===1)de.children=oe;else if(1<me){for(var Te=Array(me),ht=0;ht<me;ht++)Te[ht]=arguments[ht+2];de.children=Te}if(C&&C.defaultProps)for(ce in me=C.defaultProps,me)de[ce]===void 0&&(de[ce]=me[ce]);return{$$typeof:i,type:C,key:he,ref:we,props:de,_owner:j.current}}function P(C,D){return{$$typeof:i,type:C.type,key:D,ref:C.ref,props:C.props,_owner:C._owner}}function le(C){return typeof C=="object"&&C!==null&&C.$$typeof===i}function ue(C){var D={"=":"=0",":":"=2"};return"$"+C.replace(/[=:]/g,function(oe){return D[oe]})}var M=/\/+/g;function ee(C,D){return typeof C=="object"&&C!==null&&C.key!=null?ue(""+C.key):D.toString(36)}function ke(C,D,oe,ce,de){var he=typeof C;(he==="undefined"||he==="boolean")&&(C=null);var we=!1;if(C===null)we=!0;else switch(he){case"string":case"number":we=!0;break;case"object":switch(C.$$typeof){case i:case n:we=!0}}if(we)return we=C,de=de(we),C=ce===""?"."+ee(we,0):ce,V(de)?(oe="",C!=null&&(oe=C.replace(M,"$&/")+"/"),ke(de,D,oe,"",function(ht){return ht})):de!=null&&(le(de)&&(de=P(de,oe+(!de.key||we&&we.key===de.key?"":(""+de.key).replace(M,"$&/")+"/")+C)),D.push(de)),1;if(we=0,ce=ce===""?".":ce+":",V(C))for(var me=0;me<C.length;me++){he=C[me];var Te=ce+ee(he,me);we+=ke(he,D,oe,Te,de)}else if(Te=T(C),typeof Te=="function")for(C=Te.call(C),me=0;!(he=C.next()).done;)he=he.value,Te=ce+ee(he,me++),we+=ke(he,D,oe,Te,de);else if(he==="object")throw D=String(C),Error("Objects are not valid as a React child (found: "+(D==="[object Object]"?"object with keys {"+Object.keys(C).join(", ")+"}":D)+"). If you meant to render a collection of children, use an array instead.");return we}function rt(C,D,oe){if(C==null)return C;var ce=[],de=0;return ke(C,ce,"","",function(he){return D.call(oe,he,de++)}),ce}function He(C){if(C._status===-1){var D=C._result;D=D(),D.then(function(oe){(C._status===0||C._status===-1)&&(C._status=1,C._result=oe)},function(oe){(C._status===0||C._status===-1)&&(C._status=2,C._result=oe)}),C._status===-1&&(C._status=0,C._result=D)}if(C._status===1)return C._result.default;throw C._result}var xe={current:null},K={transition:null},re={ReactCurrentDispatcher:xe,ReactCurrentBatchConfig:K,ReactCurrentOwner:j};function J(){throw Error("act(...) is not supported in production builds of React.")}return ae.Children={map:rt,forEach:function(C,D,oe){rt(C,function(){D.apply(this,arguments)},oe)},count:function(C){var D=0;return rt(C,function(){D++}),D},toArray:function(C){return rt(C,function(D){return D})||[]},only:function(C){if(!le(C))throw Error("React.Children.only expected to receive a single React element child.");return C}},ae.Component=w,ae.Fragment=r,ae.Profiler=o,ae.PureComponent=A,ae.StrictMode=l,ae.Suspense=p,ae.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=re,ae.act=J,ae.cloneElement=function(C,D,oe){if(C==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+C+".");var ce=O({},C.props),de=C.key,he=C.ref,we=C._owner;if(D!=null){if(D.ref!==void 0&&(he=D.ref,we=j.current),D.key!==void 0&&(de=""+D.key),C.type&&C.type.defaultProps)var me=C.type.defaultProps;for(Te in D)W.call(D,Te)&&!Y.hasOwnProperty(Te)&&(ce[Te]=D[Te]===void 0&&me!==void 0?me[Te]:D[Te])}var Te=arguments.length-2;if(Te===1)ce.children=oe;else if(1<Te){me=Array(Te);for(var ht=0;ht<Te;ht++)me[ht]=arguments[ht+2];ce.children=me}return{$$typeof:i,type:C.type,key:de,ref:he,props:ce,_owner:we}},ae.createContext=function(C){return C={$$typeof:f,_currentValue:C,_currentValue2:C,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},C.Provider={$$typeof:u,_context:C},C.Consumer=C},ae.createElement=z,ae.createFactory=function(C){var D=z.bind(null,C);return D.type=C,D},ae.createRef=function(){return{current:null}},ae.forwardRef=function(C){return{$$typeof:h,render:C}},ae.isValidElement=le,ae.lazy=function(C){return{$$typeof:x,_payload:{_status:-1,_result:C},_init:He}},ae.memo=function(C,D){return{$$typeof:g,type:C,compare:D===void 0?null:D}},ae.startTransition=function(C){var D=K.transition;K.transition={};try{C()}finally{K.transition=D}},ae.unstable_act=J,ae.useCallback=function(C,D){return xe.current.useCallback(C,D)},ae.useContext=function(C){return xe.current.useContext(C)},ae.useDebugValue=function(){},ae.useDeferredValue=function(C){return xe.current.useDeferredValue(C)},ae.useEffect=function(C,D){return xe.current.useEffect(C,D)},ae.useId=function(){return xe.current.useId()},ae.useImperativeHandle=function(C,D,oe){return xe.current.useImperativeHandle(C,D,oe)},ae.useInsertionEffect=function(C,D){return xe.current.useInsertionEffect(C,D)},ae.useLayoutEffect=function(C,D){return xe.current.useLayoutEffect(C,D)},ae.useMemo=function(C,D){return xe.current.useMemo(C,D)},ae.useReducer=function(C,D,oe){return xe.current.useReducer(C,D,oe)},ae.useRef=function(C){return xe.current.useRef(C)},ae.useState=function(C){return xe.current.useState(C)},ae.useSyncExternalStore=function(C,D,oe){return xe.current.useSyncExternalStore(C,D,oe)},ae.useTransition=function(){return xe.current.useTransition()},ae.version="18.3.1",ae}var id;function qa(){return id||(id=1,Na.exports=Gm()),Na.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ld;function Xm(){if(ld)return Ns;ld=1;var i=qa(),n=Symbol.for("react.element"),r=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,o=i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function f(h,p,g){var x,S={},T=null,E=null;g!==void 0&&(T=""+g),p.key!==void 0&&(T=""+p.key),p.ref!==void 0&&(E=p.ref);for(x in p)l.call(p,x)&&!u.hasOwnProperty(x)&&(S[x]=p[x]);if(h&&h.defaultProps)for(x in p=h.defaultProps,p)S[x]===void 0&&(S[x]=p[x]);return{$$typeof:n,type:h,key:T,ref:E,props:S,_owner:o.current}}return Ns.Fragment=r,Ns.jsx=f,Ns.jsxs=f,Ns}var od;function Zm(){return od||(od=1,Ea.exports=Xm()),Ea.exports}var H=Zm(),ye=qa();const Tr=Ym(ye);function jd(){const i=Tr.useRef(null),[n,r]=Tr.useState(new DOMRect(0,0,10,10));return Tr.useLayoutEffect(()=>{const l=i.current;if(!l)return;const o=l.getBoundingClientRect();r(new DOMRect(0,0,o.width,o.height));const u=new ResizeObserver(f=>{const h=f[f.length-1];h&&h.contentRect&&r(h.contentRect)});return u.observe(l),()=>u.disconnect()},[i]),[n,i]}function eg(i){if(i<0||!isFinite(i))return"-";if(i===0)return"0";if(i<1e3)return i.toFixed(0)+"ms";const n=i/1e3;if(n<60)return n.toFixed(1)+"s";const r=n/60;if(r<60)return r.toFixed(1)+"m";const l=r/60;return l<24?l.toFixed(1)+"h":(l/24).toFixed(1)+"d"}function ad(i){const n=document.createElement("textarea");n.style.position="absolute",n.style.zIndex="-1000",n.value=i,document.body.appendChild(n),n.select(),document.execCommand("copy"),n.remove()}function Da(i,n){i&&(n=Dn.getObject(i,n));const[r,l]=Tr.useState(n),o=Tr.useCallback(u=>{i?Dn.setObject(i,u):l(u)},[i,l]);return Tr.useEffect(()=>{if(i){const u=()=>l(Dn.getObject(i,n));return Dn.onChangeEmitter.addEventListener(i,u),()=>Dn.onChangeEmitter.removeEventListener(i,u)}},[n,i]),[r,o]}class tg{constructor(){this.onChangeEmitter=new EventTarget}getString(n,r){return localStorage[n]||r}setString(n,r){var l;localStorage[n]=r,this.onChangeEmitter.dispatchEvent(new Event(n)),(l=window.saveSettings)==null||l.call(window)}getObject(n,r){if(!localStorage[n])return r;try{return JSON.parse(localStorage[n])}catch{return r}}setObject(n,r){var l;localStorage[n]=JSON.stringify(r),this.onChangeEmitter.dispatchEvent(new Event(n)),(l=window.saveSettings)==null||l.call(window)}}const Dn=new tg;function Un(...i){return i.filter(Boolean).join(" ")}const ud="\\u0000-\\u0020\\u007f-\\u009f",ng=new RegExp("(?:[a-zA-Z][a-zA-Z0-9+.-]{2,}:\\/\\/|www\\.)[^\\s"+ud+'"]{2,}[^\\s'+ud+`"')}\\],:;.!?]`,"ug");function rg(){if(document.playwrightThemeInitialized)return;document.playwrightThemeInitialized=!0,document.defaultView.addEventListener("focus",l=>{l.target.document.nodeType===Node.DOCUMENT_NODE&&document.body.classList.remove("inactive")},!1),document.defaultView.addEventListener("blur",l=>{document.body.classList.add("inactive")},!1);const n=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark-mode":"light-mode";Dn.getString("theme",n)==="dark-mode"&&document.body.classList.add("dark-mode")}const sg=new Set;function ig(){const i=lg(),n=i==="dark-mode"?"light-mode":"dark-mode";i&&document.body.classList.remove(i),document.body.classList.add(n),Dn.setString("theme",n);for(const r of sg)r(n)}function lg(){return document.body.classList.contains("dark-mode")?"dark-mode":"light-mode"}var Wi={},xa={exports:{}},ut={},Ta={exports:{}},Ca={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cd;function og(){return cd||(cd=1,function(i){function n(K,re){var J=K.length;K.push(re);e:for(;0<J;){var C=J-1>>>1,D=K[C];if(0<o(D,re))K[C]=re,K[J]=D,J=C;else break e}}function r(K){return K.length===0?null:K[0]}function l(K){if(K.length===0)return null;var re=K[0],J=K.pop();if(J!==re){K[0]=J;e:for(var C=0,D=K.length,oe=D>>>1;C<oe;){var ce=2*(C+1)-1,de=K[ce],he=ce+1,we=K[he];if(0>o(de,J))he<D&&0>o(we,de)?(K[C]=we,K[he]=J,C=he):(K[C]=de,K[ce]=J,C=ce);else if(he<D&&0>o(we,J))K[C]=we,K[he]=J,C=he;else break e}}return re}function o(K,re){var J=K.sortIndex-re.sortIndex;return J!==0?J:K.id-re.id}if(typeof performance=="object"&&typeof performance.now=="function"){var u=performance;i.unstable_now=function(){return u.now()}}else{var f=Date,h=f.now();i.unstable_now=function(){return f.now()-h}}var p=[],g=[],x=1,S=null,T=3,E=!1,O=!1,v=!1,w=typeof setTimeout=="function"?setTimeout:null,I=typeof clearTimeout=="function"?clearTimeout:null,A=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function F(K){for(var re=r(g);re!==null;){if(re.callback===null)l(g);else if(re.startTime<=K)l(g),re.sortIndex=re.expirationTime,n(p,re);else break;re=r(g)}}function V(K){if(v=!1,F(K),!O)if(r(p)!==null)O=!0,He(W);else{var re=r(g);re!==null&&xe(V,re.startTime-K)}}function W(K,re){O=!1,v&&(v=!1,I(z),z=-1),E=!0;var J=T;try{for(F(re),S=r(p);S!==null&&(!(S.expirationTime>re)||K&&!ue());){var C=S.callback;if(typeof C=="function"){S.callback=null,T=S.priorityLevel;var D=C(S.expirationTime<=re);re=i.unstable_now(),typeof D=="function"?S.callback=D:S===r(p)&&l(p),F(re)}else l(p);S=r(p)}if(S!==null)var oe=!0;else{var ce=r(g);ce!==null&&xe(V,ce.startTime-re),oe=!1}return oe}finally{S=null,T=J,E=!1}}var j=!1,Y=null,z=-1,P=5,le=-1;function ue(){return!(i.unstable_now()-le<P)}function M(){if(Y!==null){var K=i.unstable_now();le=K;var re=!0;try{re=Y(!0,K)}finally{re?ee():(j=!1,Y=null)}}else j=!1}var ee;if(typeof A=="function")ee=function(){A(M)};else if(typeof MessageChannel<"u"){var ke=new MessageChannel,rt=ke.port2;ke.port1.onmessage=M,ee=function(){rt.postMessage(null)}}else ee=function(){w(M,0)};function He(K){Y=K,j||(j=!0,ee())}function xe(K,re){z=w(function(){K(i.unstable_now())},re)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(K){K.callback=null},i.unstable_continueExecution=function(){O||E||(O=!0,He(W))},i.unstable_forceFrameRate=function(K){0>K||125<K?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):P=0<K?Math.floor(1e3/K):5},i.unstable_getCurrentPriorityLevel=function(){return T},i.unstable_getFirstCallbackNode=function(){return r(p)},i.unstable_next=function(K){switch(T){case 1:case 2:case 3:var re=3;break;default:re=T}var J=T;T=re;try{return K()}finally{T=J}},i.unstable_pauseExecution=function(){},i.unstable_requestPaint=function(){},i.unstable_runWithPriority=function(K,re){switch(K){case 1:case 2:case 3:case 4:case 5:break;default:K=3}var J=T;T=K;try{return re()}finally{T=J}},i.unstable_scheduleCallback=function(K,re,J){var C=i.unstable_now();switch(typeof J=="object"&&J!==null?(J=J.delay,J=typeof J=="number"&&0<J?C+J:C):J=C,K){case 1:var D=-1;break;case 2:D=250;break;case 5:D=**********;break;case 4:D=1e4;break;default:D=5e3}return D=J+D,K={id:x++,callback:re,priorityLevel:K,startTime:J,expirationTime:D,sortIndex:-1},J>C?(K.sortIndex=J,n(g,K),r(p)===null&&K===r(g)&&(v?(I(z),z=-1):v=!0,xe(V,J-C))):(K.sortIndex=D,n(p,K),O||E||(O=!0,He(W))),K},i.unstable_shouldYield=ue,i.unstable_wrapCallback=function(K){var re=T;return function(){var J=T;T=re;try{return K.apply(this,arguments)}finally{T=J}}}}(Ca)),Ca}var fd;function ag(){return fd||(fd=1,Ta.exports=og()),Ta.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dd;function ug(){if(dd)return ut;dd=1;var i=qa(),n=ag();function r(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,s=1;s<arguments.length;s++)t+="&args[]="+encodeURIComponent(arguments[s]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var l=new Set,o={};function u(e,t){f(e,t),f(e+"Capture",t)}function f(e,t){for(o[e]=t,e=0;e<t.length;e++)l.add(t[e])}var h=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),p=Object.prototype.hasOwnProperty,g=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,x={},S={};function T(e){return p.call(S,e)?!0:p.call(x,e)?!1:g.test(e)?S[e]=!0:(x[e]=!0,!1)}function E(e,t,s,a){if(s!==null&&s.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return a?!1:s!==null?!s.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function O(e,t,s,a){if(t===null||typeof t>"u"||E(e,t,s,a))return!0;if(a)return!1;if(s!==null)switch(s.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function v(e,t,s,a,c,d,m){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=a,this.attributeNamespace=c,this.mustUseProperty=s,this.propertyName=e,this.type=t,this.sanitizeURL=d,this.removeEmptyString=m}var w={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){w[e]=new v(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];w[t]=new v(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){w[e]=new v(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){w[e]=new v(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){w[e]=new v(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){w[e]=new v(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){w[e]=new v(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){w[e]=new v(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){w[e]=new v(e,5,!1,e.toLowerCase(),null,!1,!1)});var I=/[\-:]([a-z])/g;function A(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(I,A);w[t]=new v(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(I,A);w[t]=new v(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(I,A);w[t]=new v(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){w[e]=new v(e,1,!1,e.toLowerCase(),null,!1,!1)}),w.xlinkHref=new v("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){w[e]=new v(e,1,!1,e.toLowerCase(),null,!0,!0)});function F(e,t,s,a){var c=w.hasOwnProperty(t)?w[t]:null;(c!==null?c.type!==0:a||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(O(t,s,c,a)&&(s=null),a||c===null?T(t)&&(s===null?e.removeAttribute(t):e.setAttribute(t,""+s)):c.mustUseProperty?e[c.propertyName]=s===null?c.type===3?!1:"":s:(t=c.attributeName,a=c.attributeNamespace,s===null?e.removeAttribute(t):(c=c.type,s=c===3||c===4&&s===!0?"":""+s,a?e.setAttributeNS(a,t,s):e.setAttribute(t,s))))}var V=i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,W=Symbol.for("react.element"),j=Symbol.for("react.portal"),Y=Symbol.for("react.fragment"),z=Symbol.for("react.strict_mode"),P=Symbol.for("react.profiler"),le=Symbol.for("react.provider"),ue=Symbol.for("react.context"),M=Symbol.for("react.forward_ref"),ee=Symbol.for("react.suspense"),ke=Symbol.for("react.suspense_list"),rt=Symbol.for("react.memo"),He=Symbol.for("react.lazy"),xe=Symbol.for("react.offscreen"),K=Symbol.iterator;function re(e){return e===null||typeof e!="object"?null:(e=K&&e[K]||e["@@iterator"],typeof e=="function"?e:null)}var J=Object.assign,C;function D(e){if(C===void 0)try{throw Error()}catch(s){var t=s.stack.trim().match(/\n( *(at )?)/);C=t&&t[1]||""}return`
`+C+e}var oe=!1;function ce(e,t){if(!e||oe)return"";oe=!0;var s=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch($){var a=$}Reflect.construct(e,[],t)}else{try{t.call()}catch($){a=$}e.call(t.prototype)}else{try{throw Error()}catch($){a=$}e()}}catch($){if($&&a&&typeof $.stack=="string"){for(var c=$.stack.split(`
`),d=a.stack.split(`
`),m=c.length-1,y=d.length-1;1<=m&&0<=y&&c[m]!==d[y];)y--;for(;1<=m&&0<=y;m--,y--)if(c[m]!==d[y]){if(m!==1||y!==1)do if(m--,y--,0>y||c[m]!==d[y]){var k=`
`+c[m].replace(" at new "," at ");return e.displayName&&k.includes("<anonymous>")&&(k=k.replace("<anonymous>",e.displayName)),k}while(1<=m&&0<=y);break}}}finally{oe=!1,Error.prepareStackTrace=s}return(e=e?e.displayName||e.name:"")?D(e):""}function de(e){switch(e.tag){case 5:return D(e.type);case 16:return D("Lazy");case 13:return D("Suspense");case 19:return D("SuspenseList");case 0:case 2:case 15:return e=ce(e.type,!1),e;case 11:return e=ce(e.type.render,!1),e;case 1:return e=ce(e.type,!0),e;default:return""}}function he(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Y:return"Fragment";case j:return"Portal";case P:return"Profiler";case z:return"StrictMode";case ee:return"Suspense";case ke:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case ue:return(e.displayName||"Context")+".Consumer";case le:return(e._context.displayName||"Context")+".Provider";case M:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case rt:return t=e.displayName||null,t!==null?t:he(e.type)||"Memo";case He:t=e._payload,e=e._init;try{return he(e(t))}catch{}}return null}function we(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return he(t);case 8:return t===z?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function me(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Te(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function ht(e){var t=Te(e)?"checked":"value",s=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&typeof s<"u"&&typeof s.get=="function"&&typeof s.set=="function"){var c=s.get,d=s.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return c.call(this)},set:function(m){a=""+m,d.call(this,m)}}),Object.defineProperty(e,t,{enumerable:s.enumerable}),{getValue:function(){return a},setValue:function(m){a=""+m},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function js(e){e._valueTracker||(e._valueTracker=ht(e))}function uu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var s=t.getValue(),a="";return e&&(a=Te(e)?e.checked?"true":"false":e.value),e=a,e!==s?(t.setValue(e),!0):!1}function Rs(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ll(e,t){var s=t.checked;return J({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:s??e._wrapperState.initialChecked})}function cu(e,t){var s=t.defaultValue==null?"":t.defaultValue,a=t.checked!=null?t.checked:t.defaultChecked;s=me(t.value!=null?t.value:s),e._wrapperState={initialChecked:a,initialValue:s,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function fu(e,t){t=t.checked,t!=null&&F(e,"checked",t,!1)}function Ol(e,t){fu(e,t);var s=me(t.value),a=t.type;if(s!=null)a==="number"?(s===0&&e.value===""||e.value!=s)&&(e.value=""+s):e.value!==""+s&&(e.value=""+s);else if(a==="submit"||a==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?$l(e,t.type,s):t.hasOwnProperty("defaultValue")&&$l(e,t.type,me(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function du(e,t,s){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var a=t.type;if(!(a!=="submit"&&a!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,s||t===e.value||(e.value=t),e.defaultValue=t}s=e.name,s!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,s!==""&&(e.name=s)}function $l(e,t,s){(t!=="number"||Rs(e.ownerDocument)!==e)&&(s==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+s&&(e.defaultValue=""+s))}var br=Array.isArray;function qn(e,t,s,a){if(e=e.options,t){t={};for(var c=0;c<s.length;c++)t["$"+s[c]]=!0;for(s=0;s<e.length;s++)c=t.hasOwnProperty("$"+e[s].value),e[s].selected!==c&&(e[s].selected=c),c&&a&&(e[s].defaultSelected=!0)}else{for(s=""+me(s),t=null,c=0;c<e.length;c++){if(e[c].value===s){e[c].selected=!0,a&&(e[c].defaultSelected=!0);return}t!==null||e[c].disabled||(t=e[c])}t!==null&&(t.selected=!0)}}function Il(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(r(91));return J({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function hu(e,t){var s=t.value;if(s==null){if(s=t.children,t=t.defaultValue,s!=null){if(t!=null)throw Error(r(92));if(br(s)){if(1<s.length)throw Error(r(93));s=s[0]}t=s}t==null&&(t=""),s=t}e._wrapperState={initialValue:me(s)}}function pu(e,t){var s=me(t.value),a=me(t.defaultValue);s!=null&&(s=""+s,s!==e.value&&(e.value=s),t.defaultValue==null&&e.defaultValue!==s&&(e.defaultValue=s)),a!=null&&(e.defaultValue=""+a)}function mu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function gu(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Al(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?gu(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ds,yu=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,s,a,c){MSApp.execUnsafeLocalFunction(function(){return e(t,s,a,c)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ds=Ds||document.createElement("div"),Ds.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ds.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Br(e,t){if(t){var s=e.firstChild;if(s&&s===e.lastChild&&s.nodeType===3){s.nodeValue=t;return}}e.textContent=t}var Fr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},ep=["Webkit","ms","Moz","O"];Object.keys(Fr).forEach(function(e){ep.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Fr[t]=Fr[e]})});function vu(e,t,s){return t==null||typeof t=="boolean"||t===""?"":s||typeof t!="number"||t===0||Fr.hasOwnProperty(e)&&Fr[e]?(""+t).trim():t+"px"}function wu(e,t){e=e.style;for(var s in t)if(t.hasOwnProperty(s)){var a=s.indexOf("--")===0,c=vu(s,t[s],a);s==="float"&&(s="cssFloat"),a?e.setProperty(s,c):e[s]=c}}var tp=J({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Pl(e,t){if(t){if(tp[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(r(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(r(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(r(61))}if(t.style!=null&&typeof t.style!="object")throw Error(r(62))}}function Ml(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var jl=null;function Rl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Dl=null,Hn=null,Qn=null;function Su(e){if(e=as(e)){if(typeof Dl!="function")throw Error(r(280));var t=e.stateNode;t&&(t=li(t),Dl(e.stateNode,e.type,t))}}function ku(e){Hn?Qn?Qn.push(e):Qn=[e]:Hn=e}function Eu(){if(Hn){var e=Hn,t=Qn;if(Qn=Hn=null,Su(e),t)for(e=0;e<t.length;e++)Su(t[e])}}function Nu(e,t){return e(t)}function xu(){}var bl=!1;function Tu(e,t,s){if(bl)return e(t,s);bl=!0;try{return Nu(e,t,s)}finally{bl=!1,(Hn!==null||Qn!==null)&&(xu(),Eu())}}function zr(e,t){var s=e.stateNode;if(s===null)return null;var a=li(s);if(a===null)return null;s=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(s&&typeof s!="function")throw Error(r(231,t,typeof s));return s}var Bl=!1;if(h)try{var Ur={};Object.defineProperty(Ur,"passive",{get:function(){Bl=!0}}),window.addEventListener("test",Ur,Ur),window.removeEventListener("test",Ur,Ur)}catch{Bl=!1}function np(e,t,s,a,c,d,m,y,k){var $=Array.prototype.slice.call(arguments,3);try{t.apply(s,$)}catch(b){this.onError(b)}}var Vr=!1,bs=null,Bs=!1,Fl=null,rp={onError:function(e){Vr=!0,bs=e}};function sp(e,t,s,a,c,d,m,y,k){Vr=!1,bs=null,np.apply(rp,arguments)}function ip(e,t,s,a,c,d,m,y,k){if(sp.apply(this,arguments),Vr){if(Vr){var $=bs;Vr=!1,bs=null}else throw Error(r(198));Bs||(Bs=!0,Fl=$)}}function xn(e){var t=e,s=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(s=t.return),e=t.return;while(e)}return t.tag===3?s:null}function Cu(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function _u(e){if(xn(e)!==e)throw Error(r(188))}function lp(e){var t=e.alternate;if(!t){if(t=xn(e),t===null)throw Error(r(188));return t!==e?null:e}for(var s=e,a=t;;){var c=s.return;if(c===null)break;var d=c.alternate;if(d===null){if(a=c.return,a!==null){s=a;continue}break}if(c.child===d.child){for(d=c.child;d;){if(d===s)return _u(c),e;if(d===a)return _u(c),t;d=d.sibling}throw Error(r(188))}if(s.return!==a.return)s=c,a=d;else{for(var m=!1,y=c.child;y;){if(y===s){m=!0,s=c,a=d;break}if(y===a){m=!0,a=c,s=d;break}y=y.sibling}if(!m){for(y=d.child;y;){if(y===s){m=!0,s=d,a=c;break}if(y===a){m=!0,a=d,s=c;break}y=y.sibling}if(!m)throw Error(r(189))}}if(s.alternate!==a)throw Error(r(190))}if(s.tag!==3)throw Error(r(188));return s.stateNode.current===s?e:t}function Lu(e){return e=lp(e),e!==null?Ou(e):null}function Ou(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Ou(e);if(t!==null)return t;e=e.sibling}return null}var $u=n.unstable_scheduleCallback,Iu=n.unstable_cancelCallback,op=n.unstable_shouldYield,ap=n.unstable_requestPaint,Pe=n.unstable_now,up=n.unstable_getCurrentPriorityLevel,zl=n.unstable_ImmediatePriority,Au=n.unstable_UserBlockingPriority,Fs=n.unstable_NormalPriority,cp=n.unstable_LowPriority,Pu=n.unstable_IdlePriority,zs=null,Dt=null;function fp(e){if(Dt&&typeof Dt.onCommitFiberRoot=="function")try{Dt.onCommitFiberRoot(zs,e,void 0,(e.current.flags&128)===128)}catch{}}var Lt=Math.clz32?Math.clz32:pp,dp=Math.log,hp=Math.LN2;function pp(e){return e>>>=0,e===0?32:31-(dp(e)/hp|0)|0}var Us=64,Vs=4194304;function Kr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ks(e,t){var s=e.pendingLanes;if(s===0)return 0;var a=0,c=e.suspendedLanes,d=e.pingedLanes,m=s&268435455;if(m!==0){var y=m&~c;y!==0?a=Kr(y):(d&=m,d!==0&&(a=Kr(d)))}else m=s&~c,m!==0?a=Kr(m):d!==0&&(a=Kr(d));if(a===0)return 0;if(t!==0&&t!==a&&(t&c)===0&&(c=a&-a,d=t&-t,c>=d||c===16&&(d&4194240)!==0))return t;if((a&4)!==0&&(a|=s&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=a;0<t;)s=31-Lt(t),c=1<<s,a|=e[s],t&=~c;return a}function mp(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function gp(e,t){for(var s=e.suspendedLanes,a=e.pingedLanes,c=e.expirationTimes,d=e.pendingLanes;0<d;){var m=31-Lt(d),y=1<<m,k=c[m];k===-1?((y&s)===0||(y&a)!==0)&&(c[m]=mp(y,t)):k<=t&&(e.expiredLanes|=y),d&=~y}}function Ul(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Mu(){var e=Us;return Us<<=1,(Us&4194240)===0&&(Us=64),e}function Vl(e){for(var t=[],s=0;31>s;s++)t.push(e);return t}function Wr(e,t,s){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Lt(t),e[t]=s}function yp(e,t){var s=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var a=e.eventTimes;for(e=e.expirationTimes;0<s;){var c=31-Lt(s),d=1<<c;t[c]=0,a[c]=-1,e[c]=-1,s&=~d}}function Kl(e,t){var s=e.entangledLanes|=t;for(e=e.entanglements;s;){var a=31-Lt(s),c=1<<a;c&t|e[a]&t&&(e[a]|=t),s&=~c}}var ge=0;function ju(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var Ru,Wl,Du,bu,Bu,ql=!1,Ws=[],tn=null,nn=null,rn=null,qr=new Map,Hr=new Map,sn=[],vp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Fu(e,t){switch(e){case"focusin":case"focusout":tn=null;break;case"dragenter":case"dragleave":nn=null;break;case"mouseover":case"mouseout":rn=null;break;case"pointerover":case"pointerout":qr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Hr.delete(t.pointerId)}}function Qr(e,t,s,a,c,d){return e===null||e.nativeEvent!==d?(e={blockedOn:t,domEventName:s,eventSystemFlags:a,nativeEvent:d,targetContainers:[c]},t!==null&&(t=as(t),t!==null&&Wl(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,c!==null&&t.indexOf(c)===-1&&t.push(c),e)}function wp(e,t,s,a,c){switch(t){case"focusin":return tn=Qr(tn,e,t,s,a,c),!0;case"dragenter":return nn=Qr(nn,e,t,s,a,c),!0;case"mouseover":return rn=Qr(rn,e,t,s,a,c),!0;case"pointerover":var d=c.pointerId;return qr.set(d,Qr(qr.get(d)||null,e,t,s,a,c)),!0;case"gotpointercapture":return d=c.pointerId,Hr.set(d,Qr(Hr.get(d)||null,e,t,s,a,c)),!0}return!1}function zu(e){var t=Tn(e.target);if(t!==null){var s=xn(t);if(s!==null){if(t=s.tag,t===13){if(t=Cu(s),t!==null){e.blockedOn=t,Bu(e.priority,function(){Du(s)});return}}else if(t===3&&s.stateNode.current.memoizedState.isDehydrated){e.blockedOn=s.tag===3?s.stateNode.containerInfo:null;return}}}e.blockedOn=null}function qs(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var s=Ql(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(s===null){s=e.nativeEvent;var a=new s.constructor(s.type,s);jl=a,s.target.dispatchEvent(a),jl=null}else return t=as(s),t!==null&&Wl(t),e.blockedOn=s,!1;t.shift()}return!0}function Uu(e,t,s){qs(e)&&s.delete(t)}function Sp(){ql=!1,tn!==null&&qs(tn)&&(tn=null),nn!==null&&qs(nn)&&(nn=null),rn!==null&&qs(rn)&&(rn=null),qr.forEach(Uu),Hr.forEach(Uu)}function Jr(e,t){e.blockedOn===t&&(e.blockedOn=null,ql||(ql=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,Sp)))}function Yr(e){function t(c){return Jr(c,e)}if(0<Ws.length){Jr(Ws[0],e);for(var s=1;s<Ws.length;s++){var a=Ws[s];a.blockedOn===e&&(a.blockedOn=null)}}for(tn!==null&&Jr(tn,e),nn!==null&&Jr(nn,e),rn!==null&&Jr(rn,e),qr.forEach(t),Hr.forEach(t),s=0;s<sn.length;s++)a=sn[s],a.blockedOn===e&&(a.blockedOn=null);for(;0<sn.length&&(s=sn[0],s.blockedOn===null);)zu(s),s.blockedOn===null&&sn.shift()}var Jn=V.ReactCurrentBatchConfig,Hs=!0;function kp(e,t,s,a){var c=ge,d=Jn.transition;Jn.transition=null;try{ge=1,Hl(e,t,s,a)}finally{ge=c,Jn.transition=d}}function Ep(e,t,s,a){var c=ge,d=Jn.transition;Jn.transition=null;try{ge=4,Hl(e,t,s,a)}finally{ge=c,Jn.transition=d}}function Hl(e,t,s,a){if(Hs){var c=Ql(e,t,s,a);if(c===null)fo(e,t,a,Qs,s),Fu(e,a);else if(wp(c,e,t,s,a))a.stopPropagation();else if(Fu(e,a),t&4&&-1<vp.indexOf(e)){for(;c!==null;){var d=as(c);if(d!==null&&Ru(d),d=Ql(e,t,s,a),d===null&&fo(e,t,a,Qs,s),d===c)break;c=d}c!==null&&a.stopPropagation()}else fo(e,t,a,null,s)}}var Qs=null;function Ql(e,t,s,a){if(Qs=null,e=Rl(a),e=Tn(e),e!==null)if(t=xn(e),t===null)e=null;else if(s=t.tag,s===13){if(e=Cu(t),e!==null)return e;e=null}else if(s===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Qs=e,null}function Vu(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(up()){case zl:return 1;case Au:return 4;case Fs:case cp:return 16;case Pu:return 536870912;default:return 16}default:return 16}}var ln=null,Jl=null,Js=null;function Ku(){if(Js)return Js;var e,t=Jl,s=t.length,a,c="value"in ln?ln.value:ln.textContent,d=c.length;for(e=0;e<s&&t[e]===c[e];e++);var m=s-e;for(a=1;a<=m&&t[s-a]===c[d-a];a++);return Js=c.slice(e,1<a?1-a:void 0)}function Ys(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Gs(){return!0}function Wu(){return!1}function pt(e){function t(s,a,c,d,m){this._reactName=s,this._targetInst=c,this.type=a,this.nativeEvent=d,this.target=m,this.currentTarget=null;for(var y in e)e.hasOwnProperty(y)&&(s=e[y],this[y]=s?s(d):d[y]);return this.isDefaultPrevented=(d.defaultPrevented!=null?d.defaultPrevented:d.returnValue===!1)?Gs:Wu,this.isPropagationStopped=Wu,this}return J(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var s=this.nativeEvent;s&&(s.preventDefault?s.preventDefault():typeof s.returnValue!="unknown"&&(s.returnValue=!1),this.isDefaultPrevented=Gs)},stopPropagation:function(){var s=this.nativeEvent;s&&(s.stopPropagation?s.stopPropagation():typeof s.cancelBubble!="unknown"&&(s.cancelBubble=!0),this.isPropagationStopped=Gs)},persist:function(){},isPersistent:Gs}),t}var Yn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Yl=pt(Yn),Gr=J({},Yn,{view:0,detail:0}),Np=pt(Gr),Gl,Xl,Xr,Xs=J({},Gr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:eo,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Xr&&(Xr&&e.type==="mousemove"?(Gl=e.screenX-Xr.screenX,Xl=e.screenY-Xr.screenY):Xl=Gl=0,Xr=e),Gl)},movementY:function(e){return"movementY"in e?e.movementY:Xl}}),qu=pt(Xs),xp=J({},Xs,{dataTransfer:0}),Tp=pt(xp),Cp=J({},Gr,{relatedTarget:0}),Zl=pt(Cp),_p=J({},Yn,{animationName:0,elapsedTime:0,pseudoElement:0}),Lp=pt(_p),Op=J({},Yn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),$p=pt(Op),Ip=J({},Yn,{data:0}),Hu=pt(Ip),Ap={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Pp={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Mp={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function jp(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Mp[e])?!!t[e]:!1}function eo(){return jp}var Rp=J({},Gr,{key:function(e){if(e.key){var t=Ap[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ys(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Pp[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:eo,charCode:function(e){return e.type==="keypress"?Ys(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ys(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Dp=pt(Rp),bp=J({},Xs,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Qu=pt(bp),Bp=J({},Gr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:eo}),Fp=pt(Bp),zp=J({},Yn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Up=pt(zp),Vp=J({},Xs,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Kp=pt(Vp),Wp=[9,13,27,32],to=h&&"CompositionEvent"in window,Zr=null;h&&"documentMode"in document&&(Zr=document.documentMode);var qp=h&&"TextEvent"in window&&!Zr,Ju=h&&(!to||Zr&&8<Zr&&11>=Zr),Yu=" ",Gu=!1;function Xu(e,t){switch(e){case"keyup":return Wp.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Zu(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Gn=!1;function Hp(e,t){switch(e){case"compositionend":return Zu(t);case"keypress":return t.which!==32?null:(Gu=!0,Yu);case"textInput":return e=t.data,e===Yu&&Gu?null:e;default:return null}}function Qp(e,t){if(Gn)return e==="compositionend"||!to&&Xu(e,t)?(e=Ku(),Js=Jl=ln=null,Gn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ju&&t.locale!=="ko"?null:t.data;default:return null}}var Jp={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ec(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Jp[e.type]:t==="textarea"}function tc(e,t,s,a){ku(a),t=ri(t,"onChange"),0<t.length&&(s=new Yl("onChange","change",null,s,a),e.push({event:s,listeners:t}))}var es=null,ts=null;function Yp(e){wc(e,0)}function Zs(e){var t=nr(e);if(uu(t))return e}function Gp(e,t){if(e==="change")return t}var nc=!1;if(h){var no;if(h){var ro="oninput"in document;if(!ro){var rc=document.createElement("div");rc.setAttribute("oninput","return;"),ro=typeof rc.oninput=="function"}no=ro}else no=!1;nc=no&&(!document.documentMode||9<document.documentMode)}function sc(){es&&(es.detachEvent("onpropertychange",ic),ts=es=null)}function ic(e){if(e.propertyName==="value"&&Zs(ts)){var t=[];tc(t,ts,e,Rl(e)),Tu(Yp,t)}}function Xp(e,t,s){e==="focusin"?(sc(),es=t,ts=s,es.attachEvent("onpropertychange",ic)):e==="focusout"&&sc()}function Zp(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Zs(ts)}function em(e,t){if(e==="click")return Zs(t)}function tm(e,t){if(e==="input"||e==="change")return Zs(t)}function nm(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ot=typeof Object.is=="function"?Object.is:nm;function ns(e,t){if(Ot(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var s=Object.keys(e),a=Object.keys(t);if(s.length!==a.length)return!1;for(a=0;a<s.length;a++){var c=s[a];if(!p.call(t,c)||!Ot(e[c],t[c]))return!1}return!0}function lc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function oc(e,t){var s=lc(e);e=0;for(var a;s;){if(s.nodeType===3){if(a=e+s.textContent.length,e<=t&&a>=t)return{node:s,offset:t-e};e=a}e:{for(;s;){if(s.nextSibling){s=s.nextSibling;break e}s=s.parentNode}s=void 0}s=lc(s)}}function ac(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?ac(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function uc(){for(var e=window,t=Rs();t instanceof e.HTMLIFrameElement;){try{var s=typeof t.contentWindow.location.href=="string"}catch{s=!1}if(s)e=t.contentWindow;else break;t=Rs(e.document)}return t}function so(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function rm(e){var t=uc(),s=e.focusedElem,a=e.selectionRange;if(t!==s&&s&&s.ownerDocument&&ac(s.ownerDocument.documentElement,s)){if(a!==null&&so(s)){if(t=a.start,e=a.end,e===void 0&&(e=t),"selectionStart"in s)s.selectionStart=t,s.selectionEnd=Math.min(e,s.value.length);else if(e=(t=s.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var c=s.textContent.length,d=Math.min(a.start,c);a=a.end===void 0?d:Math.min(a.end,c),!e.extend&&d>a&&(c=a,a=d,d=c),c=oc(s,d);var m=oc(s,a);c&&m&&(e.rangeCount!==1||e.anchorNode!==c.node||e.anchorOffset!==c.offset||e.focusNode!==m.node||e.focusOffset!==m.offset)&&(t=t.createRange(),t.setStart(c.node,c.offset),e.removeAllRanges(),d>a?(e.addRange(t),e.extend(m.node,m.offset)):(t.setEnd(m.node,m.offset),e.addRange(t)))}}for(t=[],e=s;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof s.focus=="function"&&s.focus(),s=0;s<t.length;s++)e=t[s],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var sm=h&&"documentMode"in document&&11>=document.documentMode,Xn=null,io=null,rs=null,lo=!1;function cc(e,t,s){var a=s.window===s?s.document:s.nodeType===9?s:s.ownerDocument;lo||Xn==null||Xn!==Rs(a)||(a=Xn,"selectionStart"in a&&so(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),rs&&ns(rs,a)||(rs=a,a=ri(io,"onSelect"),0<a.length&&(t=new Yl("onSelect","select",null,t,s),e.push({event:t,listeners:a}),t.target=Xn)))}function ei(e,t){var s={};return s[e.toLowerCase()]=t.toLowerCase(),s["Webkit"+e]="webkit"+t,s["Moz"+e]="moz"+t,s}var Zn={animationend:ei("Animation","AnimationEnd"),animationiteration:ei("Animation","AnimationIteration"),animationstart:ei("Animation","AnimationStart"),transitionend:ei("Transition","TransitionEnd")},oo={},fc={};h&&(fc=document.createElement("div").style,"AnimationEvent"in window||(delete Zn.animationend.animation,delete Zn.animationiteration.animation,delete Zn.animationstart.animation),"TransitionEvent"in window||delete Zn.transitionend.transition);function ti(e){if(oo[e])return oo[e];if(!Zn[e])return e;var t=Zn[e],s;for(s in t)if(t.hasOwnProperty(s)&&s in fc)return oo[e]=t[s];return e}var dc=ti("animationend"),hc=ti("animationiteration"),pc=ti("animationstart"),mc=ti("transitionend"),gc=new Map,yc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function on(e,t){gc.set(e,t),u(t,[e])}for(var ao=0;ao<yc.length;ao++){var uo=yc[ao],im=uo.toLowerCase(),lm=uo[0].toUpperCase()+uo.slice(1);on(im,"on"+lm)}on(dc,"onAnimationEnd"),on(hc,"onAnimationIteration"),on(pc,"onAnimationStart"),on("dblclick","onDoubleClick"),on("focusin","onFocus"),on("focusout","onBlur"),on(mc,"onTransitionEnd"),f("onMouseEnter",["mouseout","mouseover"]),f("onMouseLeave",["mouseout","mouseover"]),f("onPointerEnter",["pointerout","pointerover"]),f("onPointerLeave",["pointerout","pointerover"]),u("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),u("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),u("onBeforeInput",["compositionend","keypress","textInput","paste"]),u("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),u("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),u("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ss="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),om=new Set("cancel close invalid load scroll toggle".split(" ").concat(ss));function vc(e,t,s){var a=e.type||"unknown-event";e.currentTarget=s,ip(a,t,void 0,e),e.currentTarget=null}function wc(e,t){t=(t&4)!==0;for(var s=0;s<e.length;s++){var a=e[s],c=a.event;a=a.listeners;e:{var d=void 0;if(t)for(var m=a.length-1;0<=m;m--){var y=a[m],k=y.instance,$=y.currentTarget;if(y=y.listener,k!==d&&c.isPropagationStopped())break e;vc(c,y,$),d=k}else for(m=0;m<a.length;m++){if(y=a[m],k=y.instance,$=y.currentTarget,y=y.listener,k!==d&&c.isPropagationStopped())break e;vc(c,y,$),d=k}}}if(Bs)throw e=Fl,Bs=!1,Fl=null,e}function Ee(e,t){var s=t[vo];s===void 0&&(s=t[vo]=new Set);var a=e+"__bubble";s.has(a)||(Sc(t,e,2,!1),s.add(a))}function co(e,t,s){var a=0;t&&(a|=4),Sc(s,e,a,t)}var ni="_reactListening"+Math.random().toString(36).slice(2);function is(e){if(!e[ni]){e[ni]=!0,l.forEach(function(s){s!=="selectionchange"&&(om.has(s)||co(s,!1,e),co(s,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ni]||(t[ni]=!0,co("selectionchange",!1,t))}}function Sc(e,t,s,a){switch(Vu(t)){case 1:var c=kp;break;case 4:c=Ep;break;default:c=Hl}s=c.bind(null,t,s,e),c=void 0,!Bl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(c=!0),a?c!==void 0?e.addEventListener(t,s,{capture:!0,passive:c}):e.addEventListener(t,s,!0):c!==void 0?e.addEventListener(t,s,{passive:c}):e.addEventListener(t,s,!1)}function fo(e,t,s,a,c){var d=a;if((t&1)===0&&(t&2)===0&&a!==null)e:for(;;){if(a===null)return;var m=a.tag;if(m===3||m===4){var y=a.stateNode.containerInfo;if(y===c||y.nodeType===8&&y.parentNode===c)break;if(m===4)for(m=a.return;m!==null;){var k=m.tag;if((k===3||k===4)&&(k=m.stateNode.containerInfo,k===c||k.nodeType===8&&k.parentNode===c))return;m=m.return}for(;y!==null;){if(m=Tn(y),m===null)return;if(k=m.tag,k===5||k===6){a=d=m;continue e}y=y.parentNode}}a=a.return}Tu(function(){var $=d,b=Rl(s),B=[];e:{var R=gc.get(e);if(R!==void 0){var q=Yl,G=e;switch(e){case"keypress":if(Ys(s)===0)break e;case"keydown":case"keyup":q=Dp;break;case"focusin":G="focus",q=Zl;break;case"focusout":G="blur",q=Zl;break;case"beforeblur":case"afterblur":q=Zl;break;case"click":if(s.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":q=qu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":q=Tp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":q=Fp;break;case dc:case hc:case pc:q=Lp;break;case mc:q=Up;break;case"scroll":q=Np;break;case"wheel":q=Kp;break;case"copy":case"cut":case"paste":q=$p;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":q=Qu}var X=(t&4)!==0,Me=!X&&e==="scroll",_=X?R!==null?R+"Capture":null:R;X=[];for(var N=$,L;N!==null;){L=N;var U=L.stateNode;if(L.tag===5&&U!==null&&(L=U,_!==null&&(U=zr(N,_),U!=null&&X.push(ls(N,U,L)))),Me)break;N=N.return}0<X.length&&(R=new q(R,G,null,s,b),B.push({event:R,listeners:X}))}}if((t&7)===0){e:{if(R=e==="mouseover"||e==="pointerover",q=e==="mouseout"||e==="pointerout",R&&s!==jl&&(G=s.relatedTarget||s.fromElement)&&(Tn(G)||G[Kt]))break e;if((q||R)&&(R=b.window===b?b:(R=b.ownerDocument)?R.defaultView||R.parentWindow:window,q?(G=s.relatedTarget||s.toElement,q=$,G=G?Tn(G):null,G!==null&&(Me=xn(G),G!==Me||G.tag!==5&&G.tag!==6)&&(G=null)):(q=null,G=$),q!==G)){if(X=qu,U="onMouseLeave",_="onMouseEnter",N="mouse",(e==="pointerout"||e==="pointerover")&&(X=Qu,U="onPointerLeave",_="onPointerEnter",N="pointer"),Me=q==null?R:nr(q),L=G==null?R:nr(G),R=new X(U,N+"leave",q,s,b),R.target=Me,R.relatedTarget=L,U=null,Tn(b)===$&&(X=new X(_,N+"enter",G,s,b),X.target=L,X.relatedTarget=Me,U=X),Me=U,q&&G)t:{for(X=q,_=G,N=0,L=X;L;L=er(L))N++;for(L=0,U=_;U;U=er(U))L++;for(;0<N-L;)X=er(X),N--;for(;0<L-N;)_=er(_),L--;for(;N--;){if(X===_||_!==null&&X===_.alternate)break t;X=er(X),_=er(_)}X=null}else X=null;q!==null&&kc(B,R,q,X,!1),G!==null&&Me!==null&&kc(B,Me,G,X,!0)}}e:{if(R=$?nr($):window,q=R.nodeName&&R.nodeName.toLowerCase(),q==="select"||q==="input"&&R.type==="file")var Z=Gp;else if(ec(R))if(nc)Z=tm;else{Z=Zp;var te=Xp}else(q=R.nodeName)&&q.toLowerCase()==="input"&&(R.type==="checkbox"||R.type==="radio")&&(Z=em);if(Z&&(Z=Z(e,$))){tc(B,Z,s,b);break e}te&&te(e,R,$),e==="focusout"&&(te=R._wrapperState)&&te.controlled&&R.type==="number"&&$l(R,"number",R.value)}switch(te=$?nr($):window,e){case"focusin":(ec(te)||te.contentEditable==="true")&&(Xn=te,io=$,rs=null);break;case"focusout":rs=io=Xn=null;break;case"mousedown":lo=!0;break;case"contextmenu":case"mouseup":case"dragend":lo=!1,cc(B,s,b);break;case"selectionchange":if(sm)break;case"keydown":case"keyup":cc(B,s,b)}var ne;if(to)e:{switch(e){case"compositionstart":var se="onCompositionStart";break e;case"compositionend":se="onCompositionEnd";break e;case"compositionupdate":se="onCompositionUpdate";break e}se=void 0}else Gn?Xu(e,s)&&(se="onCompositionEnd"):e==="keydown"&&s.keyCode===229&&(se="onCompositionStart");se&&(Ju&&s.locale!=="ko"&&(Gn||se!=="onCompositionStart"?se==="onCompositionEnd"&&Gn&&(ne=Ku()):(ln=b,Jl="value"in ln?ln.value:ln.textContent,Gn=!0)),te=ri($,se),0<te.length&&(se=new Hu(se,e,null,s,b),B.push({event:se,listeners:te}),ne?se.data=ne:(ne=Zu(s),ne!==null&&(se.data=ne)))),(ne=qp?Hp(e,s):Qp(e,s))&&($=ri($,"onBeforeInput"),0<$.length&&(b=new Hu("onBeforeInput","beforeinput",null,s,b),B.push({event:b,listeners:$}),b.data=ne))}wc(B,t)})}function ls(e,t,s){return{instance:e,listener:t,currentTarget:s}}function ri(e,t){for(var s=t+"Capture",a=[];e!==null;){var c=e,d=c.stateNode;c.tag===5&&d!==null&&(c=d,d=zr(e,s),d!=null&&a.unshift(ls(e,d,c)),d=zr(e,t),d!=null&&a.push(ls(e,d,c))),e=e.return}return a}function er(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function kc(e,t,s,a,c){for(var d=t._reactName,m=[];s!==null&&s!==a;){var y=s,k=y.alternate,$=y.stateNode;if(k!==null&&k===a)break;y.tag===5&&$!==null&&(y=$,c?(k=zr(s,d),k!=null&&m.unshift(ls(s,k,y))):c||(k=zr(s,d),k!=null&&m.push(ls(s,k,y)))),s=s.return}m.length!==0&&e.push({event:t,listeners:m})}var am=/\r\n?/g,um=/\u0000|\uFFFD/g;function Ec(e){return(typeof e=="string"?e:""+e).replace(am,`
`).replace(um,"")}function si(e,t,s){if(t=Ec(t),Ec(e)!==t&&s)throw Error(r(425))}function ii(){}var ho=null,po=null;function mo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var go=typeof setTimeout=="function"?setTimeout:void 0,cm=typeof clearTimeout=="function"?clearTimeout:void 0,Nc=typeof Promise=="function"?Promise:void 0,fm=typeof queueMicrotask=="function"?queueMicrotask:typeof Nc<"u"?function(e){return Nc.resolve(null).then(e).catch(dm)}:go;function dm(e){setTimeout(function(){throw e})}function yo(e,t){var s=t,a=0;do{var c=s.nextSibling;if(e.removeChild(s),c&&c.nodeType===8)if(s=c.data,s==="/$"){if(a===0){e.removeChild(c),Yr(t);return}a--}else s!=="$"&&s!=="$?"&&s!=="$!"||a++;s=c}while(s);Yr(t)}function an(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function xc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var s=e.data;if(s==="$"||s==="$!"||s==="$?"){if(t===0)return e;t--}else s==="/$"&&t++}e=e.previousSibling}return null}var tr=Math.random().toString(36).slice(2),bt="__reactFiber$"+tr,os="__reactProps$"+tr,Kt="__reactContainer$"+tr,vo="__reactEvents$"+tr,hm="__reactListeners$"+tr,pm="__reactHandles$"+tr;function Tn(e){var t=e[bt];if(t)return t;for(var s=e.parentNode;s;){if(t=s[Kt]||s[bt]){if(s=t.alternate,t.child!==null||s!==null&&s.child!==null)for(e=xc(e);e!==null;){if(s=e[bt])return s;e=xc(e)}return t}e=s,s=e.parentNode}return null}function as(e){return e=e[bt]||e[Kt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function nr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(r(33))}function li(e){return e[os]||null}var wo=[],rr=-1;function un(e){return{current:e}}function Ne(e){0>rr||(e.current=wo[rr],wo[rr]=null,rr--)}function Se(e,t){rr++,wo[rr]=e.current,e.current=t}var cn={},Qe=un(cn),st=un(!1),Cn=cn;function sr(e,t){var s=e.type.contextTypes;if(!s)return cn;var a=e.stateNode;if(a&&a.__reactInternalMemoizedUnmaskedChildContext===t)return a.__reactInternalMemoizedMaskedChildContext;var c={},d;for(d in s)c[d]=t[d];return a&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=c),c}function it(e){return e=e.childContextTypes,e!=null}function oi(){Ne(st),Ne(Qe)}function Tc(e,t,s){if(Qe.current!==cn)throw Error(r(168));Se(Qe,t),Se(st,s)}function Cc(e,t,s){var a=e.stateNode;if(t=t.childContextTypes,typeof a.getChildContext!="function")return s;a=a.getChildContext();for(var c in a)if(!(c in t))throw Error(r(108,we(e)||"Unknown",c));return J({},s,a)}function ai(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||cn,Cn=Qe.current,Se(Qe,e),Se(st,st.current),!0}function _c(e,t,s){var a=e.stateNode;if(!a)throw Error(r(169));s?(e=Cc(e,t,Cn),a.__reactInternalMemoizedMergedChildContext=e,Ne(st),Ne(Qe),Se(Qe,e)):Ne(st),Se(st,s)}var Wt=null,ui=!1,So=!1;function Lc(e){Wt===null?Wt=[e]:Wt.push(e)}function mm(e){ui=!0,Lc(e)}function fn(){if(!So&&Wt!==null){So=!0;var e=0,t=ge;try{var s=Wt;for(ge=1;e<s.length;e++){var a=s[e];do a=a(!0);while(a!==null)}Wt=null,ui=!1}catch(c){throw Wt!==null&&(Wt=Wt.slice(e+1)),$u(zl,fn),c}finally{ge=t,So=!1}}return null}var ir=[],lr=0,ci=null,fi=0,St=[],kt=0,_n=null,qt=1,Ht="";function Ln(e,t){ir[lr++]=fi,ir[lr++]=ci,ci=e,fi=t}function Oc(e,t,s){St[kt++]=qt,St[kt++]=Ht,St[kt++]=_n,_n=e;var a=qt;e=Ht;var c=32-Lt(a)-1;a&=~(1<<c),s+=1;var d=32-Lt(t)+c;if(30<d){var m=c-c%5;d=(a&(1<<m)-1).toString(32),a>>=m,c-=m,qt=1<<32-Lt(t)+c|s<<c|a,Ht=d+e}else qt=1<<d|s<<c|a,Ht=e}function ko(e){e.return!==null&&(Ln(e,1),Oc(e,1,0))}function Eo(e){for(;e===ci;)ci=ir[--lr],ir[lr]=null,fi=ir[--lr],ir[lr]=null;for(;e===_n;)_n=St[--kt],St[kt]=null,Ht=St[--kt],St[kt]=null,qt=St[--kt],St[kt]=null}var mt=null,gt=null,Ce=!1,$t=null;function $c(e,t){var s=Tt(5,null,null,0);s.elementType="DELETED",s.stateNode=t,s.return=e,t=e.deletions,t===null?(e.deletions=[s],e.flags|=16):t.push(s)}function Ic(e,t){switch(e.tag){case 5:var s=e.type;return t=t.nodeType!==1||s.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,mt=e,gt=an(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,mt=e,gt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(s=_n!==null?{id:qt,overflow:Ht}:null,e.memoizedState={dehydrated:t,treeContext:s,retryLane:1073741824},s=Tt(18,null,null,0),s.stateNode=t,s.return=e,e.child=s,mt=e,gt=null,!0):!1;default:return!1}}function No(e){return(e.mode&1)!==0&&(e.flags&128)===0}function xo(e){if(Ce){var t=gt;if(t){var s=t;if(!Ic(e,t)){if(No(e))throw Error(r(418));t=an(s.nextSibling);var a=mt;t&&Ic(e,t)?$c(a,s):(e.flags=e.flags&-4097|2,Ce=!1,mt=e)}}else{if(No(e))throw Error(r(418));e.flags=e.flags&-4097|2,Ce=!1,mt=e}}}function Ac(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;mt=e}function di(e){if(e!==mt)return!1;if(!Ce)return Ac(e),Ce=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!mo(e.type,e.memoizedProps)),t&&(t=gt)){if(No(e))throw Pc(),Error(r(418));for(;t;)$c(e,t),t=an(t.nextSibling)}if(Ac(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(r(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var s=e.data;if(s==="/$"){if(t===0){gt=an(e.nextSibling);break e}t--}else s!=="$"&&s!=="$!"&&s!=="$?"||t++}e=e.nextSibling}gt=null}}else gt=mt?an(e.stateNode.nextSibling):null;return!0}function Pc(){for(var e=gt;e;)e=an(e.nextSibling)}function or(){gt=mt=null,Ce=!1}function To(e){$t===null?$t=[e]:$t.push(e)}var gm=V.ReactCurrentBatchConfig;function us(e,t,s){if(e=s.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(s._owner){if(s=s._owner,s){if(s.tag!==1)throw Error(r(309));var a=s.stateNode}if(!a)throw Error(r(147,e));var c=a,d=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===d?t.ref:(t=function(m){var y=c.refs;m===null?delete y[d]:y[d]=m},t._stringRef=d,t)}if(typeof e!="string")throw Error(r(284));if(!s._owner)throw Error(r(290,e))}return e}function hi(e,t){throw e=Object.prototype.toString.call(t),Error(r(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Mc(e){var t=e._init;return t(e._payload)}function jc(e){function t(_,N){if(e){var L=_.deletions;L===null?(_.deletions=[N],_.flags|=16):L.push(N)}}function s(_,N){if(!e)return null;for(;N!==null;)t(_,N),N=N.sibling;return null}function a(_,N){for(_=new Map;N!==null;)N.key!==null?_.set(N.key,N):_.set(N.index,N),N=N.sibling;return _}function c(_,N){return _=wn(_,N),_.index=0,_.sibling=null,_}function d(_,N,L){return _.index=L,e?(L=_.alternate,L!==null?(L=L.index,L<N?(_.flags|=2,N):L):(_.flags|=2,N)):(_.flags|=1048576,N)}function m(_){return e&&_.alternate===null&&(_.flags|=2),_}function y(_,N,L,U){return N===null||N.tag!==6?(N=ga(L,_.mode,U),N.return=_,N):(N=c(N,L),N.return=_,N)}function k(_,N,L,U){var Z=L.type;return Z===Y?b(_,N,L.props.children,U,L.key):N!==null&&(N.elementType===Z||typeof Z=="object"&&Z!==null&&Z.$$typeof===He&&Mc(Z)===N.type)?(U=c(N,L.props),U.ref=us(_,N,L),U.return=_,U):(U=Di(L.type,L.key,L.props,null,_.mode,U),U.ref=us(_,N,L),U.return=_,U)}function $(_,N,L,U){return N===null||N.tag!==4||N.stateNode.containerInfo!==L.containerInfo||N.stateNode.implementation!==L.implementation?(N=ya(L,_.mode,U),N.return=_,N):(N=c(N,L.children||[]),N.return=_,N)}function b(_,N,L,U,Z){return N===null||N.tag!==7?(N=Rn(L,_.mode,U,Z),N.return=_,N):(N=c(N,L),N.return=_,N)}function B(_,N,L){if(typeof N=="string"&&N!==""||typeof N=="number")return N=ga(""+N,_.mode,L),N.return=_,N;if(typeof N=="object"&&N!==null){switch(N.$$typeof){case W:return L=Di(N.type,N.key,N.props,null,_.mode,L),L.ref=us(_,null,N),L.return=_,L;case j:return N=ya(N,_.mode,L),N.return=_,N;case He:var U=N._init;return B(_,U(N._payload),L)}if(br(N)||re(N))return N=Rn(N,_.mode,L,null),N.return=_,N;hi(_,N)}return null}function R(_,N,L,U){var Z=N!==null?N.key:null;if(typeof L=="string"&&L!==""||typeof L=="number")return Z!==null?null:y(_,N,""+L,U);if(typeof L=="object"&&L!==null){switch(L.$$typeof){case W:return L.key===Z?k(_,N,L,U):null;case j:return L.key===Z?$(_,N,L,U):null;case He:return Z=L._init,R(_,N,Z(L._payload),U)}if(br(L)||re(L))return Z!==null?null:b(_,N,L,U,null);hi(_,L)}return null}function q(_,N,L,U,Z){if(typeof U=="string"&&U!==""||typeof U=="number")return _=_.get(L)||null,y(N,_,""+U,Z);if(typeof U=="object"&&U!==null){switch(U.$$typeof){case W:return _=_.get(U.key===null?L:U.key)||null,k(N,_,U,Z);case j:return _=_.get(U.key===null?L:U.key)||null,$(N,_,U,Z);case He:var te=U._init;return q(_,N,L,te(U._payload),Z)}if(br(U)||re(U))return _=_.get(L)||null,b(N,_,U,Z,null);hi(N,U)}return null}function G(_,N,L,U){for(var Z=null,te=null,ne=N,se=N=0,Ve=null;ne!==null&&se<L.length;se++){ne.index>se?(Ve=ne,ne=null):Ve=ne.sibling;var pe=R(_,ne,L[se],U);if(pe===null){ne===null&&(ne=Ve);break}e&&ne&&pe.alternate===null&&t(_,ne),N=d(pe,N,se),te===null?Z=pe:te.sibling=pe,te=pe,ne=Ve}if(se===L.length)return s(_,ne),Ce&&Ln(_,se),Z;if(ne===null){for(;se<L.length;se++)ne=B(_,L[se],U),ne!==null&&(N=d(ne,N,se),te===null?Z=ne:te.sibling=ne,te=ne);return Ce&&Ln(_,se),Z}for(ne=a(_,ne);se<L.length;se++)Ve=q(ne,_,se,L[se],U),Ve!==null&&(e&&Ve.alternate!==null&&ne.delete(Ve.key===null?se:Ve.key),N=d(Ve,N,se),te===null?Z=Ve:te.sibling=Ve,te=Ve);return e&&ne.forEach(function(Sn){return t(_,Sn)}),Ce&&Ln(_,se),Z}function X(_,N,L,U){var Z=re(L);if(typeof Z!="function")throw Error(r(150));if(L=Z.call(L),L==null)throw Error(r(151));for(var te=Z=null,ne=N,se=N=0,Ve=null,pe=L.next();ne!==null&&!pe.done;se++,pe=L.next()){ne.index>se?(Ve=ne,ne=null):Ve=ne.sibling;var Sn=R(_,ne,pe.value,U);if(Sn===null){ne===null&&(ne=Ve);break}e&&ne&&Sn.alternate===null&&t(_,ne),N=d(Sn,N,se),te===null?Z=Sn:te.sibling=Sn,te=Sn,ne=Ve}if(pe.done)return s(_,ne),Ce&&Ln(_,se),Z;if(ne===null){for(;!pe.done;se++,pe=L.next())pe=B(_,pe.value,U),pe!==null&&(N=d(pe,N,se),te===null?Z=pe:te.sibling=pe,te=pe);return Ce&&Ln(_,se),Z}for(ne=a(_,ne);!pe.done;se++,pe=L.next())pe=q(ne,_,se,pe.value,U),pe!==null&&(e&&pe.alternate!==null&&ne.delete(pe.key===null?se:pe.key),N=d(pe,N,se),te===null?Z=pe:te.sibling=pe,te=pe);return e&&ne.forEach(function(Jm){return t(_,Jm)}),Ce&&Ln(_,se),Z}function Me(_,N,L,U){if(typeof L=="object"&&L!==null&&L.type===Y&&L.key===null&&(L=L.props.children),typeof L=="object"&&L!==null){switch(L.$$typeof){case W:e:{for(var Z=L.key,te=N;te!==null;){if(te.key===Z){if(Z=L.type,Z===Y){if(te.tag===7){s(_,te.sibling),N=c(te,L.props.children),N.return=_,_=N;break e}}else if(te.elementType===Z||typeof Z=="object"&&Z!==null&&Z.$$typeof===He&&Mc(Z)===te.type){s(_,te.sibling),N=c(te,L.props),N.ref=us(_,te,L),N.return=_,_=N;break e}s(_,te);break}else t(_,te);te=te.sibling}L.type===Y?(N=Rn(L.props.children,_.mode,U,L.key),N.return=_,_=N):(U=Di(L.type,L.key,L.props,null,_.mode,U),U.ref=us(_,N,L),U.return=_,_=U)}return m(_);case j:e:{for(te=L.key;N!==null;){if(N.key===te)if(N.tag===4&&N.stateNode.containerInfo===L.containerInfo&&N.stateNode.implementation===L.implementation){s(_,N.sibling),N=c(N,L.children||[]),N.return=_,_=N;break e}else{s(_,N);break}else t(_,N);N=N.sibling}N=ya(L,_.mode,U),N.return=_,_=N}return m(_);case He:return te=L._init,Me(_,N,te(L._payload),U)}if(br(L))return G(_,N,L,U);if(re(L))return X(_,N,L,U);hi(_,L)}return typeof L=="string"&&L!==""||typeof L=="number"?(L=""+L,N!==null&&N.tag===6?(s(_,N.sibling),N=c(N,L),N.return=_,_=N):(s(_,N),N=ga(L,_.mode,U),N.return=_,_=N),m(_)):s(_,N)}return Me}var ar=jc(!0),Rc=jc(!1),pi=un(null),mi=null,ur=null,Co=null;function _o(){Co=ur=mi=null}function Lo(e){var t=pi.current;Ne(pi),e._currentValue=t}function Oo(e,t,s){for(;e!==null;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===s)break;e=e.return}}function cr(e,t){mi=e,Co=ur=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(lt=!0),e.firstContext=null)}function Et(e){var t=e._currentValue;if(Co!==e)if(e={context:e,memoizedValue:t,next:null},ur===null){if(mi===null)throw Error(r(308));ur=e,mi.dependencies={lanes:0,firstContext:e}}else ur=ur.next=e;return t}var On=null;function $o(e){On===null?On=[e]:On.push(e)}function Dc(e,t,s,a){var c=t.interleaved;return c===null?(s.next=s,$o(t)):(s.next=c.next,c.next=s),t.interleaved=s,Qt(e,a)}function Qt(e,t){e.lanes|=t;var s=e.alternate;for(s!==null&&(s.lanes|=t),s=e,e=e.return;e!==null;)e.childLanes|=t,s=e.alternate,s!==null&&(s.childLanes|=t),s=e,e=e.return;return s.tag===3?s.stateNode:null}var dn=!1;function Io(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function bc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Jt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function hn(e,t,s){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,(fe&2)!==0){var c=a.pending;return c===null?t.next=t:(t.next=c.next,c.next=t),a.pending=t,Qt(e,s)}return c=a.interleaved,c===null?(t.next=t,$o(a)):(t.next=c.next,c.next=t),a.interleaved=t,Qt(e,s)}function gi(e,t,s){if(t=t.updateQueue,t!==null&&(t=t.shared,(s&4194240)!==0)){var a=t.lanes;a&=e.pendingLanes,s|=a,t.lanes=s,Kl(e,s)}}function Bc(e,t){var s=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,s===a)){var c=null,d=null;if(s=s.firstBaseUpdate,s!==null){do{var m={eventTime:s.eventTime,lane:s.lane,tag:s.tag,payload:s.payload,callback:s.callback,next:null};d===null?c=d=m:d=d.next=m,s=s.next}while(s!==null);d===null?c=d=t:d=d.next=t}else c=d=t;s={baseState:a.baseState,firstBaseUpdate:c,lastBaseUpdate:d,shared:a.shared,effects:a.effects},e.updateQueue=s;return}e=s.lastBaseUpdate,e===null?s.firstBaseUpdate=t:e.next=t,s.lastBaseUpdate=t}function yi(e,t,s,a){var c=e.updateQueue;dn=!1;var d=c.firstBaseUpdate,m=c.lastBaseUpdate,y=c.shared.pending;if(y!==null){c.shared.pending=null;var k=y,$=k.next;k.next=null,m===null?d=$:m.next=$,m=k;var b=e.alternate;b!==null&&(b=b.updateQueue,y=b.lastBaseUpdate,y!==m&&(y===null?b.firstBaseUpdate=$:y.next=$,b.lastBaseUpdate=k))}if(d!==null){var B=c.baseState;m=0,b=$=k=null,y=d;do{var R=y.lane,q=y.eventTime;if((a&R)===R){b!==null&&(b=b.next={eventTime:q,lane:0,tag:y.tag,payload:y.payload,callback:y.callback,next:null});e:{var G=e,X=y;switch(R=t,q=s,X.tag){case 1:if(G=X.payload,typeof G=="function"){B=G.call(q,B,R);break e}B=G;break e;case 3:G.flags=G.flags&-65537|128;case 0:if(G=X.payload,R=typeof G=="function"?G.call(q,B,R):G,R==null)break e;B=J({},B,R);break e;case 2:dn=!0}}y.callback!==null&&y.lane!==0&&(e.flags|=64,R=c.effects,R===null?c.effects=[y]:R.push(y))}else q={eventTime:q,lane:R,tag:y.tag,payload:y.payload,callback:y.callback,next:null},b===null?($=b=q,k=B):b=b.next=q,m|=R;if(y=y.next,y===null){if(y=c.shared.pending,y===null)break;R=y,y=R.next,R.next=null,c.lastBaseUpdate=R,c.shared.pending=null}}while(!0);if(b===null&&(k=B),c.baseState=k,c.firstBaseUpdate=$,c.lastBaseUpdate=b,t=c.shared.interleaved,t!==null){c=t;do m|=c.lane,c=c.next;while(c!==t)}else d===null&&(c.shared.lanes=0);An|=m,e.lanes=m,e.memoizedState=B}}function Fc(e,t,s){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var a=e[t],c=a.callback;if(c!==null){if(a.callback=null,a=s,typeof c!="function")throw Error(r(191,c));c.call(a)}}}var cs={},Bt=un(cs),fs=un(cs),ds=un(cs);function $n(e){if(e===cs)throw Error(r(174));return e}function Ao(e,t){switch(Se(ds,t),Se(fs,e),Se(Bt,cs),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Al(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Al(t,e)}Ne(Bt),Se(Bt,t)}function fr(){Ne(Bt),Ne(fs),Ne(ds)}function zc(e){$n(ds.current);var t=$n(Bt.current),s=Al(t,e.type);t!==s&&(Se(fs,e),Se(Bt,s))}function Po(e){fs.current===e&&(Ne(Bt),Ne(fs))}var Le=un(0);function vi(e){for(var t=e;t!==null;){if(t.tag===13){var s=t.memoizedState;if(s!==null&&(s=s.dehydrated,s===null||s.data==="$?"||s.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Mo=[];function jo(){for(var e=0;e<Mo.length;e++)Mo[e]._workInProgressVersionPrimary=null;Mo.length=0}var wi=V.ReactCurrentDispatcher,Ro=V.ReactCurrentBatchConfig,In=0,Oe=null,be=null,ze=null,Si=!1,hs=!1,ps=0,ym=0;function Je(){throw Error(r(321))}function Do(e,t){if(t===null)return!1;for(var s=0;s<t.length&&s<e.length;s++)if(!Ot(e[s],t[s]))return!1;return!0}function bo(e,t,s,a,c,d){if(In=d,Oe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,wi.current=e===null||e.memoizedState===null?km:Em,e=s(a,c),hs){d=0;do{if(hs=!1,ps=0,25<=d)throw Error(r(301));d+=1,ze=be=null,t.updateQueue=null,wi.current=Nm,e=s(a,c)}while(hs)}if(wi.current=Ni,t=be!==null&&be.next!==null,In=0,ze=be=Oe=null,Si=!1,t)throw Error(r(300));return e}function Bo(){var e=ps!==0;return ps=0,e}function Ft(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ze===null?Oe.memoizedState=ze=e:ze=ze.next=e,ze}function Nt(){if(be===null){var e=Oe.alternate;e=e!==null?e.memoizedState:null}else e=be.next;var t=ze===null?Oe.memoizedState:ze.next;if(t!==null)ze=t,be=e;else{if(e===null)throw Error(r(310));be=e,e={memoizedState:be.memoizedState,baseState:be.baseState,baseQueue:be.baseQueue,queue:be.queue,next:null},ze===null?Oe.memoizedState=ze=e:ze=ze.next=e}return ze}function ms(e,t){return typeof t=="function"?t(e):t}function Fo(e){var t=Nt(),s=t.queue;if(s===null)throw Error(r(311));s.lastRenderedReducer=e;var a=be,c=a.baseQueue,d=s.pending;if(d!==null){if(c!==null){var m=c.next;c.next=d.next,d.next=m}a.baseQueue=c=d,s.pending=null}if(c!==null){d=c.next,a=a.baseState;var y=m=null,k=null,$=d;do{var b=$.lane;if((In&b)===b)k!==null&&(k=k.next={lane:0,action:$.action,hasEagerState:$.hasEagerState,eagerState:$.eagerState,next:null}),a=$.hasEagerState?$.eagerState:e(a,$.action);else{var B={lane:b,action:$.action,hasEagerState:$.hasEagerState,eagerState:$.eagerState,next:null};k===null?(y=k=B,m=a):k=k.next=B,Oe.lanes|=b,An|=b}$=$.next}while($!==null&&$!==d);k===null?m=a:k.next=y,Ot(a,t.memoizedState)||(lt=!0),t.memoizedState=a,t.baseState=m,t.baseQueue=k,s.lastRenderedState=a}if(e=s.interleaved,e!==null){c=e;do d=c.lane,Oe.lanes|=d,An|=d,c=c.next;while(c!==e)}else c===null&&(s.lanes=0);return[t.memoizedState,s.dispatch]}function zo(e){var t=Nt(),s=t.queue;if(s===null)throw Error(r(311));s.lastRenderedReducer=e;var a=s.dispatch,c=s.pending,d=t.memoizedState;if(c!==null){s.pending=null;var m=c=c.next;do d=e(d,m.action),m=m.next;while(m!==c);Ot(d,t.memoizedState)||(lt=!0),t.memoizedState=d,t.baseQueue===null&&(t.baseState=d),s.lastRenderedState=d}return[d,a]}function Uc(){}function Vc(e,t){var s=Oe,a=Nt(),c=t(),d=!Ot(a.memoizedState,c);if(d&&(a.memoizedState=c,lt=!0),a=a.queue,Uo(qc.bind(null,s,a,e),[e]),a.getSnapshot!==t||d||ze!==null&&ze.memoizedState.tag&1){if(s.flags|=2048,gs(9,Wc.bind(null,s,a,c,t),void 0,null),Ue===null)throw Error(r(349));(In&30)!==0||Kc(s,t,c)}return c}function Kc(e,t,s){e.flags|=16384,e={getSnapshot:t,value:s},t=Oe.updateQueue,t===null?(t={lastEffect:null,stores:null},Oe.updateQueue=t,t.stores=[e]):(s=t.stores,s===null?t.stores=[e]:s.push(e))}function Wc(e,t,s,a){t.value=s,t.getSnapshot=a,Hc(t)&&Qc(e)}function qc(e,t,s){return s(function(){Hc(t)&&Qc(e)})}function Hc(e){var t=e.getSnapshot;e=e.value;try{var s=t();return!Ot(e,s)}catch{return!0}}function Qc(e){var t=Qt(e,1);t!==null&&Mt(t,e,1,-1)}function Jc(e){var t=Ft();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ms,lastRenderedState:e},t.queue=e,e=e.dispatch=Sm.bind(null,Oe,e),[t.memoizedState,e]}function gs(e,t,s,a){return e={tag:e,create:t,destroy:s,deps:a,next:null},t=Oe.updateQueue,t===null?(t={lastEffect:null,stores:null},Oe.updateQueue=t,t.lastEffect=e.next=e):(s=t.lastEffect,s===null?t.lastEffect=e.next=e:(a=s.next,s.next=e,e.next=a,t.lastEffect=e)),e}function Yc(){return Nt().memoizedState}function ki(e,t,s,a){var c=Ft();Oe.flags|=e,c.memoizedState=gs(1|t,s,void 0,a===void 0?null:a)}function Ei(e,t,s,a){var c=Nt();a=a===void 0?null:a;var d=void 0;if(be!==null){var m=be.memoizedState;if(d=m.destroy,a!==null&&Do(a,m.deps)){c.memoizedState=gs(t,s,d,a);return}}Oe.flags|=e,c.memoizedState=gs(1|t,s,d,a)}function Gc(e,t){return ki(8390656,8,e,t)}function Uo(e,t){return Ei(2048,8,e,t)}function Xc(e,t){return Ei(4,2,e,t)}function Zc(e,t){return Ei(4,4,e,t)}function ef(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function tf(e,t,s){return s=s!=null?s.concat([e]):null,Ei(4,4,ef.bind(null,t,e),s)}function Vo(){}function nf(e,t){var s=Nt();t=t===void 0?null:t;var a=s.memoizedState;return a!==null&&t!==null&&Do(t,a[1])?a[0]:(s.memoizedState=[e,t],e)}function rf(e,t){var s=Nt();t=t===void 0?null:t;var a=s.memoizedState;return a!==null&&t!==null&&Do(t,a[1])?a[0]:(e=e(),s.memoizedState=[e,t],e)}function sf(e,t,s){return(In&21)===0?(e.baseState&&(e.baseState=!1,lt=!0),e.memoizedState=s):(Ot(s,t)||(s=Mu(),Oe.lanes|=s,An|=s,e.baseState=!0),t)}function vm(e,t){var s=ge;ge=s!==0&&4>s?s:4,e(!0);var a=Ro.transition;Ro.transition={};try{e(!1),t()}finally{ge=s,Ro.transition=a}}function lf(){return Nt().memoizedState}function wm(e,t,s){var a=yn(e);if(s={lane:a,action:s,hasEagerState:!1,eagerState:null,next:null},of(e))af(t,s);else if(s=Dc(e,t,s,a),s!==null){var c=tt();Mt(s,e,a,c),uf(s,t,a)}}function Sm(e,t,s){var a=yn(e),c={lane:a,action:s,hasEagerState:!1,eagerState:null,next:null};if(of(e))af(t,c);else{var d=e.alternate;if(e.lanes===0&&(d===null||d.lanes===0)&&(d=t.lastRenderedReducer,d!==null))try{var m=t.lastRenderedState,y=d(m,s);if(c.hasEagerState=!0,c.eagerState=y,Ot(y,m)){var k=t.interleaved;k===null?(c.next=c,$o(t)):(c.next=k.next,k.next=c),t.interleaved=c;return}}catch{}finally{}s=Dc(e,t,c,a),s!==null&&(c=tt(),Mt(s,e,a,c),uf(s,t,a))}}function of(e){var t=e.alternate;return e===Oe||t!==null&&t===Oe}function af(e,t){hs=Si=!0;var s=e.pending;s===null?t.next=t:(t.next=s.next,s.next=t),e.pending=t}function uf(e,t,s){if((s&4194240)!==0){var a=t.lanes;a&=e.pendingLanes,s|=a,t.lanes=s,Kl(e,s)}}var Ni={readContext:Et,useCallback:Je,useContext:Je,useEffect:Je,useImperativeHandle:Je,useInsertionEffect:Je,useLayoutEffect:Je,useMemo:Je,useReducer:Je,useRef:Je,useState:Je,useDebugValue:Je,useDeferredValue:Je,useTransition:Je,useMutableSource:Je,useSyncExternalStore:Je,useId:Je,unstable_isNewReconciler:!1},km={readContext:Et,useCallback:function(e,t){return Ft().memoizedState=[e,t===void 0?null:t],e},useContext:Et,useEffect:Gc,useImperativeHandle:function(e,t,s){return s=s!=null?s.concat([e]):null,ki(4194308,4,ef.bind(null,t,e),s)},useLayoutEffect:function(e,t){return ki(4194308,4,e,t)},useInsertionEffect:function(e,t){return ki(4,2,e,t)},useMemo:function(e,t){var s=Ft();return t=t===void 0?null:t,e=e(),s.memoizedState=[e,t],e},useReducer:function(e,t,s){var a=Ft();return t=s!==void 0?s(t):t,a.memoizedState=a.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},a.queue=e,e=e.dispatch=wm.bind(null,Oe,e),[a.memoizedState,e]},useRef:function(e){var t=Ft();return e={current:e},t.memoizedState=e},useState:Jc,useDebugValue:Vo,useDeferredValue:function(e){return Ft().memoizedState=e},useTransition:function(){var e=Jc(!1),t=e[0];return e=vm.bind(null,e[1]),Ft().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,s){var a=Oe,c=Ft();if(Ce){if(s===void 0)throw Error(r(407));s=s()}else{if(s=t(),Ue===null)throw Error(r(349));(In&30)!==0||Kc(a,t,s)}c.memoizedState=s;var d={value:s,getSnapshot:t};return c.queue=d,Gc(qc.bind(null,a,d,e),[e]),a.flags|=2048,gs(9,Wc.bind(null,a,d,s,t),void 0,null),s},useId:function(){var e=Ft(),t=Ue.identifierPrefix;if(Ce){var s=Ht,a=qt;s=(a&~(1<<32-Lt(a)-1)).toString(32)+s,t=":"+t+"R"+s,s=ps++,0<s&&(t+="H"+s.toString(32)),t+=":"}else s=ym++,t=":"+t+"r"+s.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Em={readContext:Et,useCallback:nf,useContext:Et,useEffect:Uo,useImperativeHandle:tf,useInsertionEffect:Xc,useLayoutEffect:Zc,useMemo:rf,useReducer:Fo,useRef:Yc,useState:function(){return Fo(ms)},useDebugValue:Vo,useDeferredValue:function(e){var t=Nt();return sf(t,be.memoizedState,e)},useTransition:function(){var e=Fo(ms)[0],t=Nt().memoizedState;return[e,t]},useMutableSource:Uc,useSyncExternalStore:Vc,useId:lf,unstable_isNewReconciler:!1},Nm={readContext:Et,useCallback:nf,useContext:Et,useEffect:Uo,useImperativeHandle:tf,useInsertionEffect:Xc,useLayoutEffect:Zc,useMemo:rf,useReducer:zo,useRef:Yc,useState:function(){return zo(ms)},useDebugValue:Vo,useDeferredValue:function(e){var t=Nt();return be===null?t.memoizedState=e:sf(t,be.memoizedState,e)},useTransition:function(){var e=zo(ms)[0],t=Nt().memoizedState;return[e,t]},useMutableSource:Uc,useSyncExternalStore:Vc,useId:lf,unstable_isNewReconciler:!1};function It(e,t){if(e&&e.defaultProps){t=J({},t),e=e.defaultProps;for(var s in e)t[s]===void 0&&(t[s]=e[s]);return t}return t}function Ko(e,t,s,a){t=e.memoizedState,s=s(a,t),s=s==null?t:J({},t,s),e.memoizedState=s,e.lanes===0&&(e.updateQueue.baseState=s)}var xi={isMounted:function(e){return(e=e._reactInternals)?xn(e)===e:!1},enqueueSetState:function(e,t,s){e=e._reactInternals;var a=tt(),c=yn(e),d=Jt(a,c);d.payload=t,s!=null&&(d.callback=s),t=hn(e,d,c),t!==null&&(Mt(t,e,c,a),gi(t,e,c))},enqueueReplaceState:function(e,t,s){e=e._reactInternals;var a=tt(),c=yn(e),d=Jt(a,c);d.tag=1,d.payload=t,s!=null&&(d.callback=s),t=hn(e,d,c),t!==null&&(Mt(t,e,c,a),gi(t,e,c))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var s=tt(),a=yn(e),c=Jt(s,a);c.tag=2,t!=null&&(c.callback=t),t=hn(e,c,a),t!==null&&(Mt(t,e,a,s),gi(t,e,a))}};function cf(e,t,s,a,c,d,m){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,d,m):t.prototype&&t.prototype.isPureReactComponent?!ns(s,a)||!ns(c,d):!0}function ff(e,t,s){var a=!1,c=cn,d=t.contextType;return typeof d=="object"&&d!==null?d=Et(d):(c=it(t)?Cn:Qe.current,a=t.contextTypes,d=(a=a!=null)?sr(e,c):cn),t=new t(s,d),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=xi,e.stateNode=t,t._reactInternals=e,a&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=c,e.__reactInternalMemoizedMaskedChildContext=d),t}function df(e,t,s,a){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(s,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(s,a),t.state!==e&&xi.enqueueReplaceState(t,t.state,null)}function Wo(e,t,s,a){var c=e.stateNode;c.props=s,c.state=e.memoizedState,c.refs={},Io(e);var d=t.contextType;typeof d=="object"&&d!==null?c.context=Et(d):(d=it(t)?Cn:Qe.current,c.context=sr(e,d)),c.state=e.memoizedState,d=t.getDerivedStateFromProps,typeof d=="function"&&(Ko(e,t,d,s),c.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(t=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),t!==c.state&&xi.enqueueReplaceState(c,c.state,null),yi(e,s,c,a),c.state=e.memoizedState),typeof c.componentDidMount=="function"&&(e.flags|=4194308)}function dr(e,t){try{var s="",a=t;do s+=de(a),a=a.return;while(a);var c=s}catch(d){c=`
Error generating stack: `+d.message+`
`+d.stack}return{value:e,source:t,stack:c,digest:null}}function qo(e,t,s){return{value:e,source:null,stack:s??null,digest:t??null}}function Ho(e,t){try{console.error(t.value)}catch(s){setTimeout(function(){throw s})}}var xm=typeof WeakMap=="function"?WeakMap:Map;function hf(e,t,s){s=Jt(-1,s),s.tag=3,s.payload={element:null};var a=t.value;return s.callback=function(){Ii||(Ii=!0,aa=a),Ho(e,t)},s}function pf(e,t,s){s=Jt(-1,s),s.tag=3;var a=e.type.getDerivedStateFromError;if(typeof a=="function"){var c=t.value;s.payload=function(){return a(c)},s.callback=function(){Ho(e,t)}}var d=e.stateNode;return d!==null&&typeof d.componentDidCatch=="function"&&(s.callback=function(){Ho(e,t),typeof a!="function"&&(mn===null?mn=new Set([this]):mn.add(this));var m=t.stack;this.componentDidCatch(t.value,{componentStack:m!==null?m:""})}),s}function mf(e,t,s){var a=e.pingCache;if(a===null){a=e.pingCache=new xm;var c=new Set;a.set(t,c)}else c=a.get(t),c===void 0&&(c=new Set,a.set(t,c));c.has(s)||(c.add(s),e=bm.bind(null,e,t,s),t.then(e,e))}function gf(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function yf(e,t,s,a,c){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,s.flags|=131072,s.flags&=-52805,s.tag===1&&(s.alternate===null?s.tag=17:(t=Jt(-1,1),t.tag=2,hn(s,t,1))),s.lanes|=1),e):(e.flags|=65536,e.lanes=c,e)}var Tm=V.ReactCurrentOwner,lt=!1;function et(e,t,s,a){t.child=e===null?Rc(t,null,s,a):ar(t,e.child,s,a)}function vf(e,t,s,a,c){s=s.render;var d=t.ref;return cr(t,c),a=bo(e,t,s,a,d,c),s=Bo(),e!==null&&!lt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~c,Yt(e,t,c)):(Ce&&s&&ko(t),t.flags|=1,et(e,t,a,c),t.child)}function wf(e,t,s,a,c){if(e===null){var d=s.type;return typeof d=="function"&&!ma(d)&&d.defaultProps===void 0&&s.compare===null&&s.defaultProps===void 0?(t.tag=15,t.type=d,Sf(e,t,d,a,c)):(e=Di(s.type,null,a,t,t.mode,c),e.ref=t.ref,e.return=t,t.child=e)}if(d=e.child,(e.lanes&c)===0){var m=d.memoizedProps;if(s=s.compare,s=s!==null?s:ns,s(m,a)&&e.ref===t.ref)return Yt(e,t,c)}return t.flags|=1,e=wn(d,a),e.ref=t.ref,e.return=t,t.child=e}function Sf(e,t,s,a,c){if(e!==null){var d=e.memoizedProps;if(ns(d,a)&&e.ref===t.ref)if(lt=!1,t.pendingProps=a=d,(e.lanes&c)!==0)(e.flags&131072)!==0&&(lt=!0);else return t.lanes=e.lanes,Yt(e,t,c)}return Qo(e,t,s,a,c)}function kf(e,t,s){var a=t.pendingProps,c=a.children,d=e!==null?e.memoizedState:null;if(a.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Se(pr,yt),yt|=s;else{if((s&1073741824)===0)return e=d!==null?d.baseLanes|s:s,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Se(pr,yt),yt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},a=d!==null?d.baseLanes:s,Se(pr,yt),yt|=a}else d!==null?(a=d.baseLanes|s,t.memoizedState=null):a=s,Se(pr,yt),yt|=a;return et(e,t,c,s),t.child}function Ef(e,t){var s=t.ref;(e===null&&s!==null||e!==null&&e.ref!==s)&&(t.flags|=512,t.flags|=2097152)}function Qo(e,t,s,a,c){var d=it(s)?Cn:Qe.current;return d=sr(t,d),cr(t,c),s=bo(e,t,s,a,d,c),a=Bo(),e!==null&&!lt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~c,Yt(e,t,c)):(Ce&&a&&ko(t),t.flags|=1,et(e,t,s,c),t.child)}function Nf(e,t,s,a,c){if(it(s)){var d=!0;ai(t)}else d=!1;if(cr(t,c),t.stateNode===null)Ci(e,t),ff(t,s,a),Wo(t,s,a,c),a=!0;else if(e===null){var m=t.stateNode,y=t.memoizedProps;m.props=y;var k=m.context,$=s.contextType;typeof $=="object"&&$!==null?$=Et($):($=it(s)?Cn:Qe.current,$=sr(t,$));var b=s.getDerivedStateFromProps,B=typeof b=="function"||typeof m.getSnapshotBeforeUpdate=="function";B||typeof m.UNSAFE_componentWillReceiveProps!="function"&&typeof m.componentWillReceiveProps!="function"||(y!==a||k!==$)&&df(t,m,a,$),dn=!1;var R=t.memoizedState;m.state=R,yi(t,a,m,c),k=t.memoizedState,y!==a||R!==k||st.current||dn?(typeof b=="function"&&(Ko(t,s,b,a),k=t.memoizedState),(y=dn||cf(t,s,y,a,R,k,$))?(B||typeof m.UNSAFE_componentWillMount!="function"&&typeof m.componentWillMount!="function"||(typeof m.componentWillMount=="function"&&m.componentWillMount(),typeof m.UNSAFE_componentWillMount=="function"&&m.UNSAFE_componentWillMount()),typeof m.componentDidMount=="function"&&(t.flags|=4194308)):(typeof m.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=k),m.props=a,m.state=k,m.context=$,a=y):(typeof m.componentDidMount=="function"&&(t.flags|=4194308),a=!1)}else{m=t.stateNode,bc(e,t),y=t.memoizedProps,$=t.type===t.elementType?y:It(t.type,y),m.props=$,B=t.pendingProps,R=m.context,k=s.contextType,typeof k=="object"&&k!==null?k=Et(k):(k=it(s)?Cn:Qe.current,k=sr(t,k));var q=s.getDerivedStateFromProps;(b=typeof q=="function"||typeof m.getSnapshotBeforeUpdate=="function")||typeof m.UNSAFE_componentWillReceiveProps!="function"&&typeof m.componentWillReceiveProps!="function"||(y!==B||R!==k)&&df(t,m,a,k),dn=!1,R=t.memoizedState,m.state=R,yi(t,a,m,c);var G=t.memoizedState;y!==B||R!==G||st.current||dn?(typeof q=="function"&&(Ko(t,s,q,a),G=t.memoizedState),($=dn||cf(t,s,$,a,R,G,k)||!1)?(b||typeof m.UNSAFE_componentWillUpdate!="function"&&typeof m.componentWillUpdate!="function"||(typeof m.componentWillUpdate=="function"&&m.componentWillUpdate(a,G,k),typeof m.UNSAFE_componentWillUpdate=="function"&&m.UNSAFE_componentWillUpdate(a,G,k)),typeof m.componentDidUpdate=="function"&&(t.flags|=4),typeof m.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof m.componentDidUpdate!="function"||y===e.memoizedProps&&R===e.memoizedState||(t.flags|=4),typeof m.getSnapshotBeforeUpdate!="function"||y===e.memoizedProps&&R===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=G),m.props=a,m.state=G,m.context=k,a=$):(typeof m.componentDidUpdate!="function"||y===e.memoizedProps&&R===e.memoizedState||(t.flags|=4),typeof m.getSnapshotBeforeUpdate!="function"||y===e.memoizedProps&&R===e.memoizedState||(t.flags|=1024),a=!1)}return Jo(e,t,s,a,d,c)}function Jo(e,t,s,a,c,d){Ef(e,t);var m=(t.flags&128)!==0;if(!a&&!m)return c&&_c(t,s,!1),Yt(e,t,d);a=t.stateNode,Tm.current=t;var y=m&&typeof s.getDerivedStateFromError!="function"?null:a.render();return t.flags|=1,e!==null&&m?(t.child=ar(t,e.child,null,d),t.child=ar(t,null,y,d)):et(e,t,y,d),t.memoizedState=a.state,c&&_c(t,s,!0),t.child}function xf(e){var t=e.stateNode;t.pendingContext?Tc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Tc(e,t.context,!1),Ao(e,t.containerInfo)}function Tf(e,t,s,a,c){return or(),To(c),t.flags|=256,et(e,t,s,a),t.child}var Yo={dehydrated:null,treeContext:null,retryLane:0};function Go(e){return{baseLanes:e,cachePool:null,transitions:null}}function Cf(e,t,s){var a=t.pendingProps,c=Le.current,d=!1,m=(t.flags&128)!==0,y;if((y=m)||(y=e!==null&&e.memoizedState===null?!1:(c&2)!==0),y?(d=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(c|=1),Se(Le,c&1),e===null)return xo(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(m=a.children,e=a.fallback,d?(a=t.mode,d=t.child,m={mode:"hidden",children:m},(a&1)===0&&d!==null?(d.childLanes=0,d.pendingProps=m):d=bi(m,a,0,null),e=Rn(e,a,s,null),d.return=t,e.return=t,d.sibling=e,t.child=d,t.child.memoizedState=Go(s),t.memoizedState=Yo,e):Xo(t,m));if(c=e.memoizedState,c!==null&&(y=c.dehydrated,y!==null))return Cm(e,t,m,a,y,c,s);if(d){d=a.fallback,m=t.mode,c=e.child,y=c.sibling;var k={mode:"hidden",children:a.children};return(m&1)===0&&t.child!==c?(a=t.child,a.childLanes=0,a.pendingProps=k,t.deletions=null):(a=wn(c,k),a.subtreeFlags=c.subtreeFlags&14680064),y!==null?d=wn(y,d):(d=Rn(d,m,s,null),d.flags|=2),d.return=t,a.return=t,a.sibling=d,t.child=a,a=d,d=t.child,m=e.child.memoizedState,m=m===null?Go(s):{baseLanes:m.baseLanes|s,cachePool:null,transitions:m.transitions},d.memoizedState=m,d.childLanes=e.childLanes&~s,t.memoizedState=Yo,a}return d=e.child,e=d.sibling,a=wn(d,{mode:"visible",children:a.children}),(t.mode&1)===0&&(a.lanes=s),a.return=t,a.sibling=null,e!==null&&(s=t.deletions,s===null?(t.deletions=[e],t.flags|=16):s.push(e)),t.child=a,t.memoizedState=null,a}function Xo(e,t){return t=bi({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ti(e,t,s,a){return a!==null&&To(a),ar(t,e.child,null,s),e=Xo(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Cm(e,t,s,a,c,d,m){if(s)return t.flags&256?(t.flags&=-257,a=qo(Error(r(422))),Ti(e,t,m,a)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(d=a.fallback,c=t.mode,a=bi({mode:"visible",children:a.children},c,0,null),d=Rn(d,c,m,null),d.flags|=2,a.return=t,d.return=t,a.sibling=d,t.child=a,(t.mode&1)!==0&&ar(t,e.child,null,m),t.child.memoizedState=Go(m),t.memoizedState=Yo,d);if((t.mode&1)===0)return Ti(e,t,m,null);if(c.data==="$!"){if(a=c.nextSibling&&c.nextSibling.dataset,a)var y=a.dgst;return a=y,d=Error(r(419)),a=qo(d,a,void 0),Ti(e,t,m,a)}if(y=(m&e.childLanes)!==0,lt||y){if(a=Ue,a!==null){switch(m&-m){case 4:c=2;break;case 16:c=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:c=32;break;case 536870912:c=268435456;break;default:c=0}c=(c&(a.suspendedLanes|m))!==0?0:c,c!==0&&c!==d.retryLane&&(d.retryLane=c,Qt(e,c),Mt(a,e,c,-1))}return pa(),a=qo(Error(r(421))),Ti(e,t,m,a)}return c.data==="$?"?(t.flags|=128,t.child=e.child,t=Bm.bind(null,e),c._reactRetry=t,null):(e=d.treeContext,gt=an(c.nextSibling),mt=t,Ce=!0,$t=null,e!==null&&(St[kt++]=qt,St[kt++]=Ht,St[kt++]=_n,qt=e.id,Ht=e.overflow,_n=t),t=Xo(t,a.children),t.flags|=4096,t)}function _f(e,t,s){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t),Oo(e.return,t,s)}function Zo(e,t,s,a,c){var d=e.memoizedState;d===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:s,tailMode:c}:(d.isBackwards=t,d.rendering=null,d.renderingStartTime=0,d.last=a,d.tail=s,d.tailMode=c)}function Lf(e,t,s){var a=t.pendingProps,c=a.revealOrder,d=a.tail;if(et(e,t,a.children,s),a=Le.current,(a&2)!==0)a=a&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&_f(e,s,t);else if(e.tag===19)_f(e,s,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}if(Se(Le,a),(t.mode&1)===0)t.memoizedState=null;else switch(c){case"forwards":for(s=t.child,c=null;s!==null;)e=s.alternate,e!==null&&vi(e)===null&&(c=s),s=s.sibling;s=c,s===null?(c=t.child,t.child=null):(c=s.sibling,s.sibling=null),Zo(t,!1,c,s,d);break;case"backwards":for(s=null,c=t.child,t.child=null;c!==null;){if(e=c.alternate,e!==null&&vi(e)===null){t.child=c;break}e=c.sibling,c.sibling=s,s=c,c=e}Zo(t,!0,s,null,d);break;case"together":Zo(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ci(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Yt(e,t,s){if(e!==null&&(t.dependencies=e.dependencies),An|=t.lanes,(s&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(r(153));if(t.child!==null){for(e=t.child,s=wn(e,e.pendingProps),t.child=s,s.return=t;e.sibling!==null;)e=e.sibling,s=s.sibling=wn(e,e.pendingProps),s.return=t;s.sibling=null}return t.child}function _m(e,t,s){switch(t.tag){case 3:xf(t),or();break;case 5:zc(t);break;case 1:it(t.type)&&ai(t);break;case 4:Ao(t,t.stateNode.containerInfo);break;case 10:var a=t.type._context,c=t.memoizedProps.value;Se(pi,a._currentValue),a._currentValue=c;break;case 13:if(a=t.memoizedState,a!==null)return a.dehydrated!==null?(Se(Le,Le.current&1),t.flags|=128,null):(s&t.child.childLanes)!==0?Cf(e,t,s):(Se(Le,Le.current&1),e=Yt(e,t,s),e!==null?e.sibling:null);Se(Le,Le.current&1);break;case 19:if(a=(s&t.childLanes)!==0,(e.flags&128)!==0){if(a)return Lf(e,t,s);t.flags|=128}if(c=t.memoizedState,c!==null&&(c.rendering=null,c.tail=null,c.lastEffect=null),Se(Le,Le.current),a)break;return null;case 22:case 23:return t.lanes=0,kf(e,t,s)}return Yt(e,t,s)}var Of,ea,$f,If;Of=function(e,t){for(var s=t.child;s!==null;){if(s.tag===5||s.tag===6)e.appendChild(s.stateNode);else if(s.tag!==4&&s.child!==null){s.child.return=s,s=s.child;continue}if(s===t)break;for(;s.sibling===null;){if(s.return===null||s.return===t)return;s=s.return}s.sibling.return=s.return,s=s.sibling}},ea=function(){},$f=function(e,t,s,a){var c=e.memoizedProps;if(c!==a){e=t.stateNode,$n(Bt.current);var d=null;switch(s){case"input":c=Ll(e,c),a=Ll(e,a),d=[];break;case"select":c=J({},c,{value:void 0}),a=J({},a,{value:void 0}),d=[];break;case"textarea":c=Il(e,c),a=Il(e,a),d=[];break;default:typeof c.onClick!="function"&&typeof a.onClick=="function"&&(e.onclick=ii)}Pl(s,a);var m;s=null;for($ in c)if(!a.hasOwnProperty($)&&c.hasOwnProperty($)&&c[$]!=null)if($==="style"){var y=c[$];for(m in y)y.hasOwnProperty(m)&&(s||(s={}),s[m]="")}else $!=="dangerouslySetInnerHTML"&&$!=="children"&&$!=="suppressContentEditableWarning"&&$!=="suppressHydrationWarning"&&$!=="autoFocus"&&(o.hasOwnProperty($)?d||(d=[]):(d=d||[]).push($,null));for($ in a){var k=a[$];if(y=c!=null?c[$]:void 0,a.hasOwnProperty($)&&k!==y&&(k!=null||y!=null))if($==="style")if(y){for(m in y)!y.hasOwnProperty(m)||k&&k.hasOwnProperty(m)||(s||(s={}),s[m]="");for(m in k)k.hasOwnProperty(m)&&y[m]!==k[m]&&(s||(s={}),s[m]=k[m])}else s||(d||(d=[]),d.push($,s)),s=k;else $==="dangerouslySetInnerHTML"?(k=k?k.__html:void 0,y=y?y.__html:void 0,k!=null&&y!==k&&(d=d||[]).push($,k)):$==="children"?typeof k!="string"&&typeof k!="number"||(d=d||[]).push($,""+k):$!=="suppressContentEditableWarning"&&$!=="suppressHydrationWarning"&&(o.hasOwnProperty($)?(k!=null&&$==="onScroll"&&Ee("scroll",e),d||y===k||(d=[])):(d=d||[]).push($,k))}s&&(d=d||[]).push("style",s);var $=d;(t.updateQueue=$)&&(t.flags|=4)}},If=function(e,t,s,a){s!==a&&(t.flags|=4)};function ys(e,t){if(!Ce)switch(e.tailMode){case"hidden":t=e.tail;for(var s=null;t!==null;)t.alternate!==null&&(s=t),t=t.sibling;s===null?e.tail=null:s.sibling=null;break;case"collapsed":s=e.tail;for(var a=null;s!==null;)s.alternate!==null&&(a=s),s=s.sibling;a===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function Ye(e){var t=e.alternate!==null&&e.alternate.child===e.child,s=0,a=0;if(t)for(var c=e.child;c!==null;)s|=c.lanes|c.childLanes,a|=c.subtreeFlags&14680064,a|=c.flags&14680064,c.return=e,c=c.sibling;else for(c=e.child;c!==null;)s|=c.lanes|c.childLanes,a|=c.subtreeFlags,a|=c.flags,c.return=e,c=c.sibling;return e.subtreeFlags|=a,e.childLanes=s,t}function Lm(e,t,s){var a=t.pendingProps;switch(Eo(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ye(t),null;case 1:return it(t.type)&&oi(),Ye(t),null;case 3:return a=t.stateNode,fr(),Ne(st),Ne(Qe),jo(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(di(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,$t!==null&&(fa($t),$t=null))),ea(e,t),Ye(t),null;case 5:Po(t);var c=$n(ds.current);if(s=t.type,e!==null&&t.stateNode!=null)$f(e,t,s,a,c),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!a){if(t.stateNode===null)throw Error(r(166));return Ye(t),null}if(e=$n(Bt.current),di(t)){a=t.stateNode,s=t.type;var d=t.memoizedProps;switch(a[bt]=t,a[os]=d,e=(t.mode&1)!==0,s){case"dialog":Ee("cancel",a),Ee("close",a);break;case"iframe":case"object":case"embed":Ee("load",a);break;case"video":case"audio":for(c=0;c<ss.length;c++)Ee(ss[c],a);break;case"source":Ee("error",a);break;case"img":case"image":case"link":Ee("error",a),Ee("load",a);break;case"details":Ee("toggle",a);break;case"input":cu(a,d),Ee("invalid",a);break;case"select":a._wrapperState={wasMultiple:!!d.multiple},Ee("invalid",a);break;case"textarea":hu(a,d),Ee("invalid",a)}Pl(s,d),c=null;for(var m in d)if(d.hasOwnProperty(m)){var y=d[m];m==="children"?typeof y=="string"?a.textContent!==y&&(d.suppressHydrationWarning!==!0&&si(a.textContent,y,e),c=["children",y]):typeof y=="number"&&a.textContent!==""+y&&(d.suppressHydrationWarning!==!0&&si(a.textContent,y,e),c=["children",""+y]):o.hasOwnProperty(m)&&y!=null&&m==="onScroll"&&Ee("scroll",a)}switch(s){case"input":js(a),du(a,d,!0);break;case"textarea":js(a),mu(a);break;case"select":case"option":break;default:typeof d.onClick=="function"&&(a.onclick=ii)}a=c,t.updateQueue=a,a!==null&&(t.flags|=4)}else{m=c.nodeType===9?c:c.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=gu(s)),e==="http://www.w3.org/1999/xhtml"?s==="script"?(e=m.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof a.is=="string"?e=m.createElement(s,{is:a.is}):(e=m.createElement(s),s==="select"&&(m=e,a.multiple?m.multiple=!0:a.size&&(m.size=a.size))):e=m.createElementNS(e,s),e[bt]=t,e[os]=a,Of(e,t,!1,!1),t.stateNode=e;e:{switch(m=Ml(s,a),s){case"dialog":Ee("cancel",e),Ee("close",e),c=a;break;case"iframe":case"object":case"embed":Ee("load",e),c=a;break;case"video":case"audio":for(c=0;c<ss.length;c++)Ee(ss[c],e);c=a;break;case"source":Ee("error",e),c=a;break;case"img":case"image":case"link":Ee("error",e),Ee("load",e),c=a;break;case"details":Ee("toggle",e),c=a;break;case"input":cu(e,a),c=Ll(e,a),Ee("invalid",e);break;case"option":c=a;break;case"select":e._wrapperState={wasMultiple:!!a.multiple},c=J({},a,{value:void 0}),Ee("invalid",e);break;case"textarea":hu(e,a),c=Il(e,a),Ee("invalid",e);break;default:c=a}Pl(s,c),y=c;for(d in y)if(y.hasOwnProperty(d)){var k=y[d];d==="style"?wu(e,k):d==="dangerouslySetInnerHTML"?(k=k?k.__html:void 0,k!=null&&yu(e,k)):d==="children"?typeof k=="string"?(s!=="textarea"||k!=="")&&Br(e,k):typeof k=="number"&&Br(e,""+k):d!=="suppressContentEditableWarning"&&d!=="suppressHydrationWarning"&&d!=="autoFocus"&&(o.hasOwnProperty(d)?k!=null&&d==="onScroll"&&Ee("scroll",e):k!=null&&F(e,d,k,m))}switch(s){case"input":js(e),du(e,a,!1);break;case"textarea":js(e),mu(e);break;case"option":a.value!=null&&e.setAttribute("value",""+me(a.value));break;case"select":e.multiple=!!a.multiple,d=a.value,d!=null?qn(e,!!a.multiple,d,!1):a.defaultValue!=null&&qn(e,!!a.multiple,a.defaultValue,!0);break;default:typeof c.onClick=="function"&&(e.onclick=ii)}switch(s){case"button":case"input":case"select":case"textarea":a=!!a.autoFocus;break e;case"img":a=!0;break e;default:a=!1}}a&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Ye(t),null;case 6:if(e&&t.stateNode!=null)If(e,t,e.memoizedProps,a);else{if(typeof a!="string"&&t.stateNode===null)throw Error(r(166));if(s=$n(ds.current),$n(Bt.current),di(t)){if(a=t.stateNode,s=t.memoizedProps,a[bt]=t,(d=a.nodeValue!==s)&&(e=mt,e!==null))switch(e.tag){case 3:si(a.nodeValue,s,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&si(a.nodeValue,s,(e.mode&1)!==0)}d&&(t.flags|=4)}else a=(s.nodeType===9?s:s.ownerDocument).createTextNode(a),a[bt]=t,t.stateNode=a}return Ye(t),null;case 13:if(Ne(Le),a=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Ce&&gt!==null&&(t.mode&1)!==0&&(t.flags&128)===0)Pc(),or(),t.flags|=98560,d=!1;else if(d=di(t),a!==null&&a.dehydrated!==null){if(e===null){if(!d)throw Error(r(318));if(d=t.memoizedState,d=d!==null?d.dehydrated:null,!d)throw Error(r(317));d[bt]=t}else or(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ye(t),d=!1}else $t!==null&&(fa($t),$t=null),d=!0;if(!d)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=s,t):(a=a!==null,a!==(e!==null&&e.memoizedState!==null)&&a&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(Le.current&1)!==0?Be===0&&(Be=3):pa())),t.updateQueue!==null&&(t.flags|=4),Ye(t),null);case 4:return fr(),ea(e,t),e===null&&is(t.stateNode.containerInfo),Ye(t),null;case 10:return Lo(t.type._context),Ye(t),null;case 17:return it(t.type)&&oi(),Ye(t),null;case 19:if(Ne(Le),d=t.memoizedState,d===null)return Ye(t),null;if(a=(t.flags&128)!==0,m=d.rendering,m===null)if(a)ys(d,!1);else{if(Be!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(m=vi(e),m!==null){for(t.flags|=128,ys(d,!1),a=m.updateQueue,a!==null&&(t.updateQueue=a,t.flags|=4),t.subtreeFlags=0,a=s,s=t.child;s!==null;)d=s,e=a,d.flags&=14680066,m=d.alternate,m===null?(d.childLanes=0,d.lanes=e,d.child=null,d.subtreeFlags=0,d.memoizedProps=null,d.memoizedState=null,d.updateQueue=null,d.dependencies=null,d.stateNode=null):(d.childLanes=m.childLanes,d.lanes=m.lanes,d.child=m.child,d.subtreeFlags=0,d.deletions=null,d.memoizedProps=m.memoizedProps,d.memoizedState=m.memoizedState,d.updateQueue=m.updateQueue,d.type=m.type,e=m.dependencies,d.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),s=s.sibling;return Se(Le,Le.current&1|2),t.child}e=e.sibling}d.tail!==null&&Pe()>mr&&(t.flags|=128,a=!0,ys(d,!1),t.lanes=4194304)}else{if(!a)if(e=vi(m),e!==null){if(t.flags|=128,a=!0,s=e.updateQueue,s!==null&&(t.updateQueue=s,t.flags|=4),ys(d,!0),d.tail===null&&d.tailMode==="hidden"&&!m.alternate&&!Ce)return Ye(t),null}else 2*Pe()-d.renderingStartTime>mr&&s!==1073741824&&(t.flags|=128,a=!0,ys(d,!1),t.lanes=4194304);d.isBackwards?(m.sibling=t.child,t.child=m):(s=d.last,s!==null?s.sibling=m:t.child=m,d.last=m)}return d.tail!==null?(t=d.tail,d.rendering=t,d.tail=t.sibling,d.renderingStartTime=Pe(),t.sibling=null,s=Le.current,Se(Le,a?s&1|2:s&1),t):(Ye(t),null);case 22:case 23:return ha(),a=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==a&&(t.flags|=8192),a&&(t.mode&1)!==0?(yt&1073741824)!==0&&(Ye(t),t.subtreeFlags&6&&(t.flags|=8192)):Ye(t),null;case 24:return null;case 25:return null}throw Error(r(156,t.tag))}function Om(e,t){switch(Eo(t),t.tag){case 1:return it(t.type)&&oi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return fr(),Ne(st),Ne(Qe),jo(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return Po(t),null;case 13:if(Ne(Le),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(r(340));or()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Ne(Le),null;case 4:return fr(),null;case 10:return Lo(t.type._context),null;case 22:case 23:return ha(),null;case 24:return null;default:return null}}var _i=!1,Ge=!1,$m=typeof WeakSet=="function"?WeakSet:Set,Q=null;function hr(e,t){var s=e.ref;if(s!==null)if(typeof s=="function")try{s(null)}catch(a){Ae(e,t,a)}else s.current=null}function ta(e,t,s){try{s()}catch(a){Ae(e,t,a)}}var Af=!1;function Im(e,t){if(ho=Hs,e=uc(),so(e)){if("selectionStart"in e)var s={start:e.selectionStart,end:e.selectionEnd};else e:{s=(s=e.ownerDocument)&&s.defaultView||window;var a=s.getSelection&&s.getSelection();if(a&&a.rangeCount!==0){s=a.anchorNode;var c=a.anchorOffset,d=a.focusNode;a=a.focusOffset;try{s.nodeType,d.nodeType}catch{s=null;break e}var m=0,y=-1,k=-1,$=0,b=0,B=e,R=null;t:for(;;){for(var q;B!==s||c!==0&&B.nodeType!==3||(y=m+c),B!==d||a!==0&&B.nodeType!==3||(k=m+a),B.nodeType===3&&(m+=B.nodeValue.length),(q=B.firstChild)!==null;)R=B,B=q;for(;;){if(B===e)break t;if(R===s&&++$===c&&(y=m),R===d&&++b===a&&(k=m),(q=B.nextSibling)!==null)break;B=R,R=B.parentNode}B=q}s=y===-1||k===-1?null:{start:y,end:k}}else s=null}s=s||{start:0,end:0}}else s=null;for(po={focusedElem:e,selectionRange:s},Hs=!1,Q=t;Q!==null;)if(t=Q,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,Q=e;else for(;Q!==null;){t=Q;try{var G=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(G!==null){var X=G.memoizedProps,Me=G.memoizedState,_=t.stateNode,N=_.getSnapshotBeforeUpdate(t.elementType===t.type?X:It(t.type,X),Me);_.__reactInternalSnapshotBeforeUpdate=N}break;case 3:var L=t.stateNode.containerInfo;L.nodeType===1?L.textContent="":L.nodeType===9&&L.documentElement&&L.removeChild(L.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(r(163))}}catch(U){Ae(t,t.return,U)}if(e=t.sibling,e!==null){e.return=t.return,Q=e;break}Q=t.return}return G=Af,Af=!1,G}function vs(e,t,s){var a=t.updateQueue;if(a=a!==null?a.lastEffect:null,a!==null){var c=a=a.next;do{if((c.tag&e)===e){var d=c.destroy;c.destroy=void 0,d!==void 0&&ta(t,s,d)}c=c.next}while(c!==a)}}function Li(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var s=t=t.next;do{if((s.tag&e)===e){var a=s.create;s.destroy=a()}s=s.next}while(s!==t)}}function na(e){var t=e.ref;if(t!==null){var s=e.stateNode;switch(e.tag){case 5:e=s;break;default:e=s}typeof t=="function"?t(e):t.current=e}}function Pf(e){var t=e.alternate;t!==null&&(e.alternate=null,Pf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[bt],delete t[os],delete t[vo],delete t[hm],delete t[pm])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Mf(e){return e.tag===5||e.tag===3||e.tag===4}function jf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Mf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ra(e,t,s){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?s.nodeType===8?s.parentNode.insertBefore(e,t):s.insertBefore(e,t):(s.nodeType===8?(t=s.parentNode,t.insertBefore(e,s)):(t=s,t.appendChild(e)),s=s._reactRootContainer,s!=null||t.onclick!==null||(t.onclick=ii));else if(a!==4&&(e=e.child,e!==null))for(ra(e,t,s),e=e.sibling;e!==null;)ra(e,t,s),e=e.sibling}function sa(e,t,s){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?s.insertBefore(e,t):s.appendChild(e);else if(a!==4&&(e=e.child,e!==null))for(sa(e,t,s),e=e.sibling;e!==null;)sa(e,t,s),e=e.sibling}var Ke=null,At=!1;function pn(e,t,s){for(s=s.child;s!==null;)Rf(e,t,s),s=s.sibling}function Rf(e,t,s){if(Dt&&typeof Dt.onCommitFiberUnmount=="function")try{Dt.onCommitFiberUnmount(zs,s)}catch{}switch(s.tag){case 5:Ge||hr(s,t);case 6:var a=Ke,c=At;Ke=null,pn(e,t,s),Ke=a,At=c,Ke!==null&&(At?(e=Ke,s=s.stateNode,e.nodeType===8?e.parentNode.removeChild(s):e.removeChild(s)):Ke.removeChild(s.stateNode));break;case 18:Ke!==null&&(At?(e=Ke,s=s.stateNode,e.nodeType===8?yo(e.parentNode,s):e.nodeType===1&&yo(e,s),Yr(e)):yo(Ke,s.stateNode));break;case 4:a=Ke,c=At,Ke=s.stateNode.containerInfo,At=!0,pn(e,t,s),Ke=a,At=c;break;case 0:case 11:case 14:case 15:if(!Ge&&(a=s.updateQueue,a!==null&&(a=a.lastEffect,a!==null))){c=a=a.next;do{var d=c,m=d.destroy;d=d.tag,m!==void 0&&((d&2)!==0||(d&4)!==0)&&ta(s,t,m),c=c.next}while(c!==a)}pn(e,t,s);break;case 1:if(!Ge&&(hr(s,t),a=s.stateNode,typeof a.componentWillUnmount=="function"))try{a.props=s.memoizedProps,a.state=s.memoizedState,a.componentWillUnmount()}catch(y){Ae(s,t,y)}pn(e,t,s);break;case 21:pn(e,t,s);break;case 22:s.mode&1?(Ge=(a=Ge)||s.memoizedState!==null,pn(e,t,s),Ge=a):pn(e,t,s);break;default:pn(e,t,s)}}function Df(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var s=e.stateNode;s===null&&(s=e.stateNode=new $m),t.forEach(function(a){var c=Fm.bind(null,e,a);s.has(a)||(s.add(a),a.then(c,c))})}}function Pt(e,t){var s=t.deletions;if(s!==null)for(var a=0;a<s.length;a++){var c=s[a];try{var d=e,m=t,y=m;e:for(;y!==null;){switch(y.tag){case 5:Ke=y.stateNode,At=!1;break e;case 3:Ke=y.stateNode.containerInfo,At=!0;break e;case 4:Ke=y.stateNode.containerInfo,At=!0;break e}y=y.return}if(Ke===null)throw Error(r(160));Rf(d,m,c),Ke=null,At=!1;var k=c.alternate;k!==null&&(k.return=null),c.return=null}catch($){Ae(c,t,$)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)bf(t,e),t=t.sibling}function bf(e,t){var s=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Pt(t,e),zt(e),a&4){try{vs(3,e,e.return),Li(3,e)}catch(X){Ae(e,e.return,X)}try{vs(5,e,e.return)}catch(X){Ae(e,e.return,X)}}break;case 1:Pt(t,e),zt(e),a&512&&s!==null&&hr(s,s.return);break;case 5:if(Pt(t,e),zt(e),a&512&&s!==null&&hr(s,s.return),e.flags&32){var c=e.stateNode;try{Br(c,"")}catch(X){Ae(e,e.return,X)}}if(a&4&&(c=e.stateNode,c!=null)){var d=e.memoizedProps,m=s!==null?s.memoizedProps:d,y=e.type,k=e.updateQueue;if(e.updateQueue=null,k!==null)try{y==="input"&&d.type==="radio"&&d.name!=null&&fu(c,d),Ml(y,m);var $=Ml(y,d);for(m=0;m<k.length;m+=2){var b=k[m],B=k[m+1];b==="style"?wu(c,B):b==="dangerouslySetInnerHTML"?yu(c,B):b==="children"?Br(c,B):F(c,b,B,$)}switch(y){case"input":Ol(c,d);break;case"textarea":pu(c,d);break;case"select":var R=c._wrapperState.wasMultiple;c._wrapperState.wasMultiple=!!d.multiple;var q=d.value;q!=null?qn(c,!!d.multiple,q,!1):R!==!!d.multiple&&(d.defaultValue!=null?qn(c,!!d.multiple,d.defaultValue,!0):qn(c,!!d.multiple,d.multiple?[]:"",!1))}c[os]=d}catch(X){Ae(e,e.return,X)}}break;case 6:if(Pt(t,e),zt(e),a&4){if(e.stateNode===null)throw Error(r(162));c=e.stateNode,d=e.memoizedProps;try{c.nodeValue=d}catch(X){Ae(e,e.return,X)}}break;case 3:if(Pt(t,e),zt(e),a&4&&s!==null&&s.memoizedState.isDehydrated)try{Yr(t.containerInfo)}catch(X){Ae(e,e.return,X)}break;case 4:Pt(t,e),zt(e);break;case 13:Pt(t,e),zt(e),c=e.child,c.flags&8192&&(d=c.memoizedState!==null,c.stateNode.isHidden=d,!d||c.alternate!==null&&c.alternate.memoizedState!==null||(oa=Pe())),a&4&&Df(e);break;case 22:if(b=s!==null&&s.memoizedState!==null,e.mode&1?(Ge=($=Ge)||b,Pt(t,e),Ge=$):Pt(t,e),zt(e),a&8192){if($=e.memoizedState!==null,(e.stateNode.isHidden=$)&&!b&&(e.mode&1)!==0)for(Q=e,b=e.child;b!==null;){for(B=Q=b;Q!==null;){switch(R=Q,q=R.child,R.tag){case 0:case 11:case 14:case 15:vs(4,R,R.return);break;case 1:hr(R,R.return);var G=R.stateNode;if(typeof G.componentWillUnmount=="function"){a=R,s=R.return;try{t=a,G.props=t.memoizedProps,G.state=t.memoizedState,G.componentWillUnmount()}catch(X){Ae(a,s,X)}}break;case 5:hr(R,R.return);break;case 22:if(R.memoizedState!==null){zf(B);continue}}q!==null?(q.return=R,Q=q):zf(B)}b=b.sibling}e:for(b=null,B=e;;){if(B.tag===5){if(b===null){b=B;try{c=B.stateNode,$?(d=c.style,typeof d.setProperty=="function"?d.setProperty("display","none","important"):d.display="none"):(y=B.stateNode,k=B.memoizedProps.style,m=k!=null&&k.hasOwnProperty("display")?k.display:null,y.style.display=vu("display",m))}catch(X){Ae(e,e.return,X)}}}else if(B.tag===6){if(b===null)try{B.stateNode.nodeValue=$?"":B.memoizedProps}catch(X){Ae(e,e.return,X)}}else if((B.tag!==22&&B.tag!==23||B.memoizedState===null||B===e)&&B.child!==null){B.child.return=B,B=B.child;continue}if(B===e)break e;for(;B.sibling===null;){if(B.return===null||B.return===e)break e;b===B&&(b=null),B=B.return}b===B&&(b=null),B.sibling.return=B.return,B=B.sibling}}break;case 19:Pt(t,e),zt(e),a&4&&Df(e);break;case 21:break;default:Pt(t,e),zt(e)}}function zt(e){var t=e.flags;if(t&2){try{e:{for(var s=e.return;s!==null;){if(Mf(s)){var a=s;break e}s=s.return}throw Error(r(160))}switch(a.tag){case 5:var c=a.stateNode;a.flags&32&&(Br(c,""),a.flags&=-33);var d=jf(e);sa(e,d,c);break;case 3:case 4:var m=a.stateNode.containerInfo,y=jf(e);ra(e,y,m);break;default:throw Error(r(161))}}catch(k){Ae(e,e.return,k)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Am(e,t,s){Q=e,Bf(e)}function Bf(e,t,s){for(var a=(e.mode&1)!==0;Q!==null;){var c=Q,d=c.child;if(c.tag===22&&a){var m=c.memoizedState!==null||_i;if(!m){var y=c.alternate,k=y!==null&&y.memoizedState!==null||Ge;y=_i;var $=Ge;if(_i=m,(Ge=k)&&!$)for(Q=c;Q!==null;)m=Q,k=m.child,m.tag===22&&m.memoizedState!==null?Uf(c):k!==null?(k.return=m,Q=k):Uf(c);for(;d!==null;)Q=d,Bf(d),d=d.sibling;Q=c,_i=y,Ge=$}Ff(e)}else(c.subtreeFlags&8772)!==0&&d!==null?(d.return=c,Q=d):Ff(e)}}function Ff(e){for(;Q!==null;){var t=Q;if((t.flags&8772)!==0){var s=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:Ge||Li(5,t);break;case 1:var a=t.stateNode;if(t.flags&4&&!Ge)if(s===null)a.componentDidMount();else{var c=t.elementType===t.type?s.memoizedProps:It(t.type,s.memoizedProps);a.componentDidUpdate(c,s.memoizedState,a.__reactInternalSnapshotBeforeUpdate)}var d=t.updateQueue;d!==null&&Fc(t,d,a);break;case 3:var m=t.updateQueue;if(m!==null){if(s=null,t.child!==null)switch(t.child.tag){case 5:s=t.child.stateNode;break;case 1:s=t.child.stateNode}Fc(t,m,s)}break;case 5:var y=t.stateNode;if(s===null&&t.flags&4){s=y;var k=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":k.autoFocus&&s.focus();break;case"img":k.src&&(s.src=k.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var $=t.alternate;if($!==null){var b=$.memoizedState;if(b!==null){var B=b.dehydrated;B!==null&&Yr(B)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(r(163))}Ge||t.flags&512&&na(t)}catch(R){Ae(t,t.return,R)}}if(t===e){Q=null;break}if(s=t.sibling,s!==null){s.return=t.return,Q=s;break}Q=t.return}}function zf(e){for(;Q!==null;){var t=Q;if(t===e){Q=null;break}var s=t.sibling;if(s!==null){s.return=t.return,Q=s;break}Q=t.return}}function Uf(e){for(;Q!==null;){var t=Q;try{switch(t.tag){case 0:case 11:case 15:var s=t.return;try{Li(4,t)}catch(k){Ae(t,s,k)}break;case 1:var a=t.stateNode;if(typeof a.componentDidMount=="function"){var c=t.return;try{a.componentDidMount()}catch(k){Ae(t,c,k)}}var d=t.return;try{na(t)}catch(k){Ae(t,d,k)}break;case 5:var m=t.return;try{na(t)}catch(k){Ae(t,m,k)}}}catch(k){Ae(t,t.return,k)}if(t===e){Q=null;break}var y=t.sibling;if(y!==null){y.return=t.return,Q=y;break}Q=t.return}}var Pm=Math.ceil,Oi=V.ReactCurrentDispatcher,ia=V.ReactCurrentOwner,xt=V.ReactCurrentBatchConfig,fe=0,Ue=null,Re=null,We=0,yt=0,pr=un(0),Be=0,ws=null,An=0,$i=0,la=0,Ss=null,ot=null,oa=0,mr=1/0,Gt=null,Ii=!1,aa=null,mn=null,Ai=!1,gn=null,Pi=0,ks=0,ua=null,Mi=-1,ji=0;function tt(){return(fe&6)!==0?Pe():Mi!==-1?Mi:Mi=Pe()}function yn(e){return(e.mode&1)===0?1:(fe&2)!==0&&We!==0?We&-We:gm.transition!==null?(ji===0&&(ji=Mu()),ji):(e=ge,e!==0||(e=window.event,e=e===void 0?16:Vu(e.type)),e)}function Mt(e,t,s,a){if(50<ks)throw ks=0,ua=null,Error(r(185));Wr(e,s,a),((fe&2)===0||e!==Ue)&&(e===Ue&&((fe&2)===0&&($i|=s),Be===4&&vn(e,We)),at(e,a),s===1&&fe===0&&(t.mode&1)===0&&(mr=Pe()+500,ui&&fn()))}function at(e,t){var s=e.callbackNode;gp(e,t);var a=Ks(e,e===Ue?We:0);if(a===0)s!==null&&Iu(s),e.callbackNode=null,e.callbackPriority=0;else if(t=a&-a,e.callbackPriority!==t){if(s!=null&&Iu(s),t===1)e.tag===0?mm(Kf.bind(null,e)):Lc(Kf.bind(null,e)),fm(function(){(fe&6)===0&&fn()}),s=null;else{switch(ju(a)){case 1:s=zl;break;case 4:s=Au;break;case 16:s=Fs;break;case 536870912:s=Pu;break;default:s=Fs}s=Xf(s,Vf.bind(null,e))}e.callbackPriority=t,e.callbackNode=s}}function Vf(e,t){if(Mi=-1,ji=0,(fe&6)!==0)throw Error(r(327));var s=e.callbackNode;if(gr()&&e.callbackNode!==s)return null;var a=Ks(e,e===Ue?We:0);if(a===0)return null;if((a&30)!==0||(a&e.expiredLanes)!==0||t)t=Ri(e,a);else{t=a;var c=fe;fe|=2;var d=qf();(Ue!==e||We!==t)&&(Gt=null,mr=Pe()+500,Mn(e,t));do try{Rm();break}catch(y){Wf(e,y)}while(!0);_o(),Oi.current=d,fe=c,Re!==null?t=0:(Ue=null,We=0,t=Be)}if(t!==0){if(t===2&&(c=Ul(e),c!==0&&(a=c,t=ca(e,c))),t===1)throw s=ws,Mn(e,0),vn(e,a),at(e,Pe()),s;if(t===6)vn(e,a);else{if(c=e.current.alternate,(a&30)===0&&!Mm(c)&&(t=Ri(e,a),t===2&&(d=Ul(e),d!==0&&(a=d,t=ca(e,d))),t===1))throw s=ws,Mn(e,0),vn(e,a),at(e,Pe()),s;switch(e.finishedWork=c,e.finishedLanes=a,t){case 0:case 1:throw Error(r(345));case 2:jn(e,ot,Gt);break;case 3:if(vn(e,a),(a&130023424)===a&&(t=oa+500-Pe(),10<t)){if(Ks(e,0)!==0)break;if(c=e.suspendedLanes,(c&a)!==a){tt(),e.pingedLanes|=e.suspendedLanes&c;break}e.timeoutHandle=go(jn.bind(null,e,ot,Gt),t);break}jn(e,ot,Gt);break;case 4:if(vn(e,a),(a&4194240)===a)break;for(t=e.eventTimes,c=-1;0<a;){var m=31-Lt(a);d=1<<m,m=t[m],m>c&&(c=m),a&=~d}if(a=c,a=Pe()-a,a=(120>a?120:480>a?480:1080>a?1080:1920>a?1920:3e3>a?3e3:4320>a?4320:1960*Pm(a/1960))-a,10<a){e.timeoutHandle=go(jn.bind(null,e,ot,Gt),a);break}jn(e,ot,Gt);break;case 5:jn(e,ot,Gt);break;default:throw Error(r(329))}}}return at(e,Pe()),e.callbackNode===s?Vf.bind(null,e):null}function ca(e,t){var s=Ss;return e.current.memoizedState.isDehydrated&&(Mn(e,t).flags|=256),e=Ri(e,t),e!==2&&(t=ot,ot=s,t!==null&&fa(t)),e}function fa(e){ot===null?ot=e:ot.push.apply(ot,e)}function Mm(e){for(var t=e;;){if(t.flags&16384){var s=t.updateQueue;if(s!==null&&(s=s.stores,s!==null))for(var a=0;a<s.length;a++){var c=s[a],d=c.getSnapshot;c=c.value;try{if(!Ot(d(),c))return!1}catch{return!1}}}if(s=t.child,t.subtreeFlags&16384&&s!==null)s.return=t,t=s;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function vn(e,t){for(t&=~la,t&=~$i,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var s=31-Lt(t),a=1<<s;e[s]=-1,t&=~a}}function Kf(e){if((fe&6)!==0)throw Error(r(327));gr();var t=Ks(e,0);if((t&1)===0)return at(e,Pe()),null;var s=Ri(e,t);if(e.tag!==0&&s===2){var a=Ul(e);a!==0&&(t=a,s=ca(e,a))}if(s===1)throw s=ws,Mn(e,0),vn(e,t),at(e,Pe()),s;if(s===6)throw Error(r(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,jn(e,ot,Gt),at(e,Pe()),null}function da(e,t){var s=fe;fe|=1;try{return e(t)}finally{fe=s,fe===0&&(mr=Pe()+500,ui&&fn())}}function Pn(e){gn!==null&&gn.tag===0&&(fe&6)===0&&gr();var t=fe;fe|=1;var s=xt.transition,a=ge;try{if(xt.transition=null,ge=1,e)return e()}finally{ge=a,xt.transition=s,fe=t,(fe&6)===0&&fn()}}function ha(){yt=pr.current,Ne(pr)}function Mn(e,t){e.finishedWork=null,e.finishedLanes=0;var s=e.timeoutHandle;if(s!==-1&&(e.timeoutHandle=-1,cm(s)),Re!==null)for(s=Re.return;s!==null;){var a=s;switch(Eo(a),a.tag){case 1:a=a.type.childContextTypes,a!=null&&oi();break;case 3:fr(),Ne(st),Ne(Qe),jo();break;case 5:Po(a);break;case 4:fr();break;case 13:Ne(Le);break;case 19:Ne(Le);break;case 10:Lo(a.type._context);break;case 22:case 23:ha()}s=s.return}if(Ue=e,Re=e=wn(e.current,null),We=yt=t,Be=0,ws=null,la=$i=An=0,ot=Ss=null,On!==null){for(t=0;t<On.length;t++)if(s=On[t],a=s.interleaved,a!==null){s.interleaved=null;var c=a.next,d=s.pending;if(d!==null){var m=d.next;d.next=c,a.next=m}s.pending=a}On=null}return e}function Wf(e,t){do{var s=Re;try{if(_o(),wi.current=Ni,Si){for(var a=Oe.memoizedState;a!==null;){var c=a.queue;c!==null&&(c.pending=null),a=a.next}Si=!1}if(In=0,ze=be=Oe=null,hs=!1,ps=0,ia.current=null,s===null||s.return===null){Be=1,ws=t,Re=null;break}e:{var d=e,m=s.return,y=s,k=t;if(t=We,y.flags|=32768,k!==null&&typeof k=="object"&&typeof k.then=="function"){var $=k,b=y,B=b.tag;if((b.mode&1)===0&&(B===0||B===11||B===15)){var R=b.alternate;R?(b.updateQueue=R.updateQueue,b.memoizedState=R.memoizedState,b.lanes=R.lanes):(b.updateQueue=null,b.memoizedState=null)}var q=gf(m);if(q!==null){q.flags&=-257,yf(q,m,y,d,t),q.mode&1&&mf(d,$,t),t=q,k=$;var G=t.updateQueue;if(G===null){var X=new Set;X.add(k),t.updateQueue=X}else G.add(k);break e}else{if((t&1)===0){mf(d,$,t),pa();break e}k=Error(r(426))}}else if(Ce&&y.mode&1){var Me=gf(m);if(Me!==null){(Me.flags&65536)===0&&(Me.flags|=256),yf(Me,m,y,d,t),To(dr(k,y));break e}}d=k=dr(k,y),Be!==4&&(Be=2),Ss===null?Ss=[d]:Ss.push(d),d=m;do{switch(d.tag){case 3:d.flags|=65536,t&=-t,d.lanes|=t;var _=hf(d,k,t);Bc(d,_);break e;case 1:y=k;var N=d.type,L=d.stateNode;if((d.flags&128)===0&&(typeof N.getDerivedStateFromError=="function"||L!==null&&typeof L.componentDidCatch=="function"&&(mn===null||!mn.has(L)))){d.flags|=65536,t&=-t,d.lanes|=t;var U=pf(d,y,t);Bc(d,U);break e}}d=d.return}while(d!==null)}Qf(s)}catch(Z){t=Z,Re===s&&s!==null&&(Re=s=s.return);continue}break}while(!0)}function qf(){var e=Oi.current;return Oi.current=Ni,e===null?Ni:e}function pa(){(Be===0||Be===3||Be===2)&&(Be=4),Ue===null||(An&268435455)===0&&($i&268435455)===0||vn(Ue,We)}function Ri(e,t){var s=fe;fe|=2;var a=qf();(Ue!==e||We!==t)&&(Gt=null,Mn(e,t));do try{jm();break}catch(c){Wf(e,c)}while(!0);if(_o(),fe=s,Oi.current=a,Re!==null)throw Error(r(261));return Ue=null,We=0,Be}function jm(){for(;Re!==null;)Hf(Re)}function Rm(){for(;Re!==null&&!op();)Hf(Re)}function Hf(e){var t=Gf(e.alternate,e,yt);e.memoizedProps=e.pendingProps,t===null?Qf(e):Re=t,ia.current=null}function Qf(e){var t=e;do{var s=t.alternate;if(e=t.return,(t.flags&32768)===0){if(s=Lm(s,t,yt),s!==null){Re=s;return}}else{if(s=Om(s,t),s!==null){s.flags&=32767,Re=s;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Be=6,Re=null;return}}if(t=t.sibling,t!==null){Re=t;return}Re=t=e}while(t!==null);Be===0&&(Be=5)}function jn(e,t,s){var a=ge,c=xt.transition;try{xt.transition=null,ge=1,Dm(e,t,s,a)}finally{xt.transition=c,ge=a}return null}function Dm(e,t,s,a){do gr();while(gn!==null);if((fe&6)!==0)throw Error(r(327));s=e.finishedWork;var c=e.finishedLanes;if(s===null)return null;if(e.finishedWork=null,e.finishedLanes=0,s===e.current)throw Error(r(177));e.callbackNode=null,e.callbackPriority=0;var d=s.lanes|s.childLanes;if(yp(e,d),e===Ue&&(Re=Ue=null,We=0),(s.subtreeFlags&2064)===0&&(s.flags&2064)===0||Ai||(Ai=!0,Xf(Fs,function(){return gr(),null})),d=(s.flags&15990)!==0,(s.subtreeFlags&15990)!==0||d){d=xt.transition,xt.transition=null;var m=ge;ge=1;var y=fe;fe|=4,ia.current=null,Im(e,s),bf(s,e),rm(po),Hs=!!ho,po=ho=null,e.current=s,Am(s),ap(),fe=y,ge=m,xt.transition=d}else e.current=s;if(Ai&&(Ai=!1,gn=e,Pi=c),d=e.pendingLanes,d===0&&(mn=null),fp(s.stateNode),at(e,Pe()),t!==null)for(a=e.onRecoverableError,s=0;s<t.length;s++)c=t[s],a(c.value,{componentStack:c.stack,digest:c.digest});if(Ii)throw Ii=!1,e=aa,aa=null,e;return(Pi&1)!==0&&e.tag!==0&&gr(),d=e.pendingLanes,(d&1)!==0?e===ua?ks++:(ks=0,ua=e):ks=0,fn(),null}function gr(){if(gn!==null){var e=ju(Pi),t=xt.transition,s=ge;try{if(xt.transition=null,ge=16>e?16:e,gn===null)var a=!1;else{if(e=gn,gn=null,Pi=0,(fe&6)!==0)throw Error(r(331));var c=fe;for(fe|=4,Q=e.current;Q!==null;){var d=Q,m=d.child;if((Q.flags&16)!==0){var y=d.deletions;if(y!==null){for(var k=0;k<y.length;k++){var $=y[k];for(Q=$;Q!==null;){var b=Q;switch(b.tag){case 0:case 11:case 15:vs(8,b,d)}var B=b.child;if(B!==null)B.return=b,Q=B;else for(;Q!==null;){b=Q;var R=b.sibling,q=b.return;if(Pf(b),b===$){Q=null;break}if(R!==null){R.return=q,Q=R;break}Q=q}}}var G=d.alternate;if(G!==null){var X=G.child;if(X!==null){G.child=null;do{var Me=X.sibling;X.sibling=null,X=Me}while(X!==null)}}Q=d}}if((d.subtreeFlags&2064)!==0&&m!==null)m.return=d,Q=m;else e:for(;Q!==null;){if(d=Q,(d.flags&2048)!==0)switch(d.tag){case 0:case 11:case 15:vs(9,d,d.return)}var _=d.sibling;if(_!==null){_.return=d.return,Q=_;break e}Q=d.return}}var N=e.current;for(Q=N;Q!==null;){m=Q;var L=m.child;if((m.subtreeFlags&2064)!==0&&L!==null)L.return=m,Q=L;else e:for(m=N;Q!==null;){if(y=Q,(y.flags&2048)!==0)try{switch(y.tag){case 0:case 11:case 15:Li(9,y)}}catch(Z){Ae(y,y.return,Z)}if(y===m){Q=null;break e}var U=y.sibling;if(U!==null){U.return=y.return,Q=U;break e}Q=y.return}}if(fe=c,fn(),Dt&&typeof Dt.onPostCommitFiberRoot=="function")try{Dt.onPostCommitFiberRoot(zs,e)}catch{}a=!0}return a}finally{ge=s,xt.transition=t}}return!1}function Jf(e,t,s){t=dr(s,t),t=hf(e,t,1),e=hn(e,t,1),t=tt(),e!==null&&(Wr(e,1,t),at(e,t))}function Ae(e,t,s){if(e.tag===3)Jf(e,e,s);else for(;t!==null;){if(t.tag===3){Jf(t,e,s);break}else if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(mn===null||!mn.has(a))){e=dr(s,e),e=pf(t,e,1),t=hn(t,e,1),e=tt(),t!==null&&(Wr(t,1,e),at(t,e));break}}t=t.return}}function bm(e,t,s){var a=e.pingCache;a!==null&&a.delete(t),t=tt(),e.pingedLanes|=e.suspendedLanes&s,Ue===e&&(We&s)===s&&(Be===4||Be===3&&(We&130023424)===We&&500>Pe()-oa?Mn(e,0):la|=s),at(e,t)}function Yf(e,t){t===0&&((e.mode&1)===0?t=1:(t=Vs,Vs<<=1,(Vs&130023424)===0&&(Vs=4194304)));var s=tt();e=Qt(e,t),e!==null&&(Wr(e,t,s),at(e,s))}function Bm(e){var t=e.memoizedState,s=0;t!==null&&(s=t.retryLane),Yf(e,s)}function Fm(e,t){var s=0;switch(e.tag){case 13:var a=e.stateNode,c=e.memoizedState;c!==null&&(s=c.retryLane);break;case 19:a=e.stateNode;break;default:throw Error(r(314))}a!==null&&a.delete(t),Yf(e,s)}var Gf;Gf=function(e,t,s){if(e!==null)if(e.memoizedProps!==t.pendingProps||st.current)lt=!0;else{if((e.lanes&s)===0&&(t.flags&128)===0)return lt=!1,_m(e,t,s);lt=(e.flags&131072)!==0}else lt=!1,Ce&&(t.flags&1048576)!==0&&Oc(t,fi,t.index);switch(t.lanes=0,t.tag){case 2:var a=t.type;Ci(e,t),e=t.pendingProps;var c=sr(t,Qe.current);cr(t,s),c=bo(null,t,a,e,c,s);var d=Bo();return t.flags|=1,typeof c=="object"&&c!==null&&typeof c.render=="function"&&c.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,it(a)?(d=!0,ai(t)):d=!1,t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,Io(t),c.updater=xi,t.stateNode=c,c._reactInternals=t,Wo(t,a,e,s),t=Jo(null,t,a,!0,d,s)):(t.tag=0,Ce&&d&&ko(t),et(null,t,c,s),t=t.child),t;case 16:a=t.elementType;e:{switch(Ci(e,t),e=t.pendingProps,c=a._init,a=c(a._payload),t.type=a,c=t.tag=Um(a),e=It(a,e),c){case 0:t=Qo(null,t,a,e,s);break e;case 1:t=Nf(null,t,a,e,s);break e;case 11:t=vf(null,t,a,e,s);break e;case 14:t=wf(null,t,a,It(a.type,e),s);break e}throw Error(r(306,a,""))}return t;case 0:return a=t.type,c=t.pendingProps,c=t.elementType===a?c:It(a,c),Qo(e,t,a,c,s);case 1:return a=t.type,c=t.pendingProps,c=t.elementType===a?c:It(a,c),Nf(e,t,a,c,s);case 3:e:{if(xf(t),e===null)throw Error(r(387));a=t.pendingProps,d=t.memoizedState,c=d.element,bc(e,t),yi(t,a,null,s);var m=t.memoizedState;if(a=m.element,d.isDehydrated)if(d={element:a,isDehydrated:!1,cache:m.cache,pendingSuspenseBoundaries:m.pendingSuspenseBoundaries,transitions:m.transitions},t.updateQueue.baseState=d,t.memoizedState=d,t.flags&256){c=dr(Error(r(423)),t),t=Tf(e,t,a,s,c);break e}else if(a!==c){c=dr(Error(r(424)),t),t=Tf(e,t,a,s,c);break e}else for(gt=an(t.stateNode.containerInfo.firstChild),mt=t,Ce=!0,$t=null,s=Rc(t,null,a,s),t.child=s;s;)s.flags=s.flags&-3|4096,s=s.sibling;else{if(or(),a===c){t=Yt(e,t,s);break e}et(e,t,a,s)}t=t.child}return t;case 5:return zc(t),e===null&&xo(t),a=t.type,c=t.pendingProps,d=e!==null?e.memoizedProps:null,m=c.children,mo(a,c)?m=null:d!==null&&mo(a,d)&&(t.flags|=32),Ef(e,t),et(e,t,m,s),t.child;case 6:return e===null&&xo(t),null;case 13:return Cf(e,t,s);case 4:return Ao(t,t.stateNode.containerInfo),a=t.pendingProps,e===null?t.child=ar(t,null,a,s):et(e,t,a,s),t.child;case 11:return a=t.type,c=t.pendingProps,c=t.elementType===a?c:It(a,c),vf(e,t,a,c,s);case 7:return et(e,t,t.pendingProps,s),t.child;case 8:return et(e,t,t.pendingProps.children,s),t.child;case 12:return et(e,t,t.pendingProps.children,s),t.child;case 10:e:{if(a=t.type._context,c=t.pendingProps,d=t.memoizedProps,m=c.value,Se(pi,a._currentValue),a._currentValue=m,d!==null)if(Ot(d.value,m)){if(d.children===c.children&&!st.current){t=Yt(e,t,s);break e}}else for(d=t.child,d!==null&&(d.return=t);d!==null;){var y=d.dependencies;if(y!==null){m=d.child;for(var k=y.firstContext;k!==null;){if(k.context===a){if(d.tag===1){k=Jt(-1,s&-s),k.tag=2;var $=d.updateQueue;if($!==null){$=$.shared;var b=$.pending;b===null?k.next=k:(k.next=b.next,b.next=k),$.pending=k}}d.lanes|=s,k=d.alternate,k!==null&&(k.lanes|=s),Oo(d.return,s,t),y.lanes|=s;break}k=k.next}}else if(d.tag===10)m=d.type===t.type?null:d.child;else if(d.tag===18){if(m=d.return,m===null)throw Error(r(341));m.lanes|=s,y=m.alternate,y!==null&&(y.lanes|=s),Oo(m,s,t),m=d.sibling}else m=d.child;if(m!==null)m.return=d;else for(m=d;m!==null;){if(m===t){m=null;break}if(d=m.sibling,d!==null){d.return=m.return,m=d;break}m=m.return}d=m}et(e,t,c.children,s),t=t.child}return t;case 9:return c=t.type,a=t.pendingProps.children,cr(t,s),c=Et(c),a=a(c),t.flags|=1,et(e,t,a,s),t.child;case 14:return a=t.type,c=It(a,t.pendingProps),c=It(a.type,c),wf(e,t,a,c,s);case 15:return Sf(e,t,t.type,t.pendingProps,s);case 17:return a=t.type,c=t.pendingProps,c=t.elementType===a?c:It(a,c),Ci(e,t),t.tag=1,it(a)?(e=!0,ai(t)):e=!1,cr(t,s),ff(t,a,c),Wo(t,a,c,s),Jo(null,t,a,!0,e,s);case 19:return Lf(e,t,s);case 22:return kf(e,t,s)}throw Error(r(156,t.tag))};function Xf(e,t){return $u(e,t)}function zm(e,t,s,a){this.tag=e,this.key=s,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Tt(e,t,s,a){return new zm(e,t,s,a)}function ma(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Um(e){if(typeof e=="function")return ma(e)?1:0;if(e!=null){if(e=e.$$typeof,e===M)return 11;if(e===rt)return 14}return 2}function wn(e,t){var s=e.alternate;return s===null?(s=Tt(e.tag,t,e.key,e.mode),s.elementType=e.elementType,s.type=e.type,s.stateNode=e.stateNode,s.alternate=e,e.alternate=s):(s.pendingProps=t,s.type=e.type,s.flags=0,s.subtreeFlags=0,s.deletions=null),s.flags=e.flags&14680064,s.childLanes=e.childLanes,s.lanes=e.lanes,s.child=e.child,s.memoizedProps=e.memoizedProps,s.memoizedState=e.memoizedState,s.updateQueue=e.updateQueue,t=e.dependencies,s.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},s.sibling=e.sibling,s.index=e.index,s.ref=e.ref,s}function Di(e,t,s,a,c,d){var m=2;if(a=e,typeof e=="function")ma(e)&&(m=1);else if(typeof e=="string")m=5;else e:switch(e){case Y:return Rn(s.children,c,d,t);case z:m=8,c|=8;break;case P:return e=Tt(12,s,t,c|2),e.elementType=P,e.lanes=d,e;case ee:return e=Tt(13,s,t,c),e.elementType=ee,e.lanes=d,e;case ke:return e=Tt(19,s,t,c),e.elementType=ke,e.lanes=d,e;case xe:return bi(s,c,d,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case le:m=10;break e;case ue:m=9;break e;case M:m=11;break e;case rt:m=14;break e;case He:m=16,a=null;break e}throw Error(r(130,e==null?e:typeof e,""))}return t=Tt(m,s,t,c),t.elementType=e,t.type=a,t.lanes=d,t}function Rn(e,t,s,a){return e=Tt(7,e,a,t),e.lanes=s,e}function bi(e,t,s,a){return e=Tt(22,e,a,t),e.elementType=xe,e.lanes=s,e.stateNode={isHidden:!1},e}function ga(e,t,s){return e=Tt(6,e,null,t),e.lanes=s,e}function ya(e,t,s){return t=Tt(4,e.children!==null?e.children:[],e.key,t),t.lanes=s,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Vm(e,t,s,a,c){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Vl(0),this.expirationTimes=Vl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Vl(0),this.identifierPrefix=a,this.onRecoverableError=c,this.mutableSourceEagerHydrationData=null}function va(e,t,s,a,c,d,m,y,k){return e=new Vm(e,t,s,y,k),t===1?(t=1,d===!0&&(t|=8)):t=0,d=Tt(3,null,null,t),e.current=d,d.stateNode=e,d.memoizedState={element:a,isDehydrated:s,cache:null,transitions:null,pendingSuspenseBoundaries:null},Io(d),e}function Km(e,t,s){var a=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:j,key:a==null?null:""+a,children:e,containerInfo:t,implementation:s}}function Zf(e){if(!e)return cn;e=e._reactInternals;e:{if(xn(e)!==e||e.tag!==1)throw Error(r(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(it(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(r(171))}if(e.tag===1){var s=e.type;if(it(s))return Cc(e,s,t)}return t}function ed(e,t,s,a,c,d,m,y,k){return e=va(s,a,!0,e,c,d,m,y,k),e.context=Zf(null),s=e.current,a=tt(),c=yn(s),d=Jt(a,c),d.callback=t??null,hn(s,d,c),e.current.lanes=c,Wr(e,c,a),at(e,a),e}function Bi(e,t,s,a){var c=t.current,d=tt(),m=yn(c);return s=Zf(s),t.context===null?t.context=s:t.pendingContext=s,t=Jt(d,m),t.payload={element:e},a=a===void 0?null:a,a!==null&&(t.callback=a),e=hn(c,t,m),e!==null&&(Mt(e,c,m,d),gi(e,c,m)),m}function Fi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function td(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var s=e.retryLane;e.retryLane=s!==0&&s<t?s:t}}function wa(e,t){td(e,t),(e=e.alternate)&&td(e,t)}function Wm(){return null}var nd=typeof reportError=="function"?reportError:function(e){console.error(e)};function Sa(e){this._internalRoot=e}zi.prototype.render=Sa.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(r(409));Bi(e,t,null,null)},zi.prototype.unmount=Sa.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Pn(function(){Bi(null,e,null,null)}),t[Kt]=null}};function zi(e){this._internalRoot=e}zi.prototype.unstable_scheduleHydration=function(e){if(e){var t=bu();e={blockedOn:null,target:e,priority:t};for(var s=0;s<sn.length&&t!==0&&t<sn[s].priority;s++);sn.splice(s,0,e),s===0&&zu(e)}};function ka(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ui(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function rd(){}function qm(e,t,s,a,c){if(c){if(typeof a=="function"){var d=a;a=function(){var $=Fi(m);d.call($)}}var m=ed(t,a,e,0,null,!1,!1,"",rd);return e._reactRootContainer=m,e[Kt]=m.current,is(e.nodeType===8?e.parentNode:e),Pn(),m}for(;c=e.lastChild;)e.removeChild(c);if(typeof a=="function"){var y=a;a=function(){var $=Fi(k);y.call($)}}var k=va(e,0,!1,null,null,!1,!1,"",rd);return e._reactRootContainer=k,e[Kt]=k.current,is(e.nodeType===8?e.parentNode:e),Pn(function(){Bi(t,k,s,a)}),k}function Vi(e,t,s,a,c){var d=s._reactRootContainer;if(d){var m=d;if(typeof c=="function"){var y=c;c=function(){var k=Fi(m);y.call(k)}}Bi(t,m,e,c)}else m=qm(s,t,e,c,a);return Fi(m)}Ru=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var s=Kr(t.pendingLanes);s!==0&&(Kl(t,s|1),at(t,Pe()),(fe&6)===0&&(mr=Pe()+500,fn()))}break;case 13:Pn(function(){var a=Qt(e,1);if(a!==null){var c=tt();Mt(a,e,1,c)}}),wa(e,1)}},Wl=function(e){if(e.tag===13){var t=Qt(e,134217728);if(t!==null){var s=tt();Mt(t,e,134217728,s)}wa(e,134217728)}},Du=function(e){if(e.tag===13){var t=yn(e),s=Qt(e,t);if(s!==null){var a=tt();Mt(s,e,t,a)}wa(e,t)}},bu=function(){return ge},Bu=function(e,t){var s=ge;try{return ge=e,t()}finally{ge=s}},Dl=function(e,t,s){switch(t){case"input":if(Ol(e,s),t=s.name,s.type==="radio"&&t!=null){for(s=e;s.parentNode;)s=s.parentNode;for(s=s.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<s.length;t++){var a=s[t];if(a!==e&&a.form===e.form){var c=li(a);if(!c)throw Error(r(90));uu(a),Ol(a,c)}}}break;case"textarea":pu(e,s);break;case"select":t=s.value,t!=null&&qn(e,!!s.multiple,t,!1)}},Nu=da,xu=Pn;var Hm={usingClientEntryPoint:!1,Events:[as,nr,li,ku,Eu,da]},Es={findFiberByHostInstance:Tn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Qm={bundleType:Es.bundleType,version:Es.version,rendererPackageName:Es.rendererPackageName,rendererConfig:Es.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:V.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Lu(e),e===null?null:e.stateNode},findFiberByHostInstance:Es.findFiberByHostInstance||Wm,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ki=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ki.isDisabled&&Ki.supportsFiber)try{zs=Ki.inject(Qm),Dt=Ki}catch{}}return ut.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Hm,ut.createPortal=function(e,t){var s=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ka(t))throw Error(r(200));return Km(e,t,null,s)},ut.createRoot=function(e,t){if(!ka(e))throw Error(r(299));var s=!1,a="",c=nd;return t!=null&&(t.unstable_strictMode===!0&&(s=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onRecoverableError!==void 0&&(c=t.onRecoverableError)),t=va(e,1,!1,null,null,s,!1,a,c),e[Kt]=t.current,is(e.nodeType===8?e.parentNode:e),new Sa(t)},ut.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(r(188)):(e=Object.keys(e).join(","),Error(r(268,e)));return e=Lu(t),e=e===null?null:e.stateNode,e},ut.flushSync=function(e){return Pn(e)},ut.hydrate=function(e,t,s){if(!Ui(t))throw Error(r(200));return Vi(null,e,t,!0,s)},ut.hydrateRoot=function(e,t,s){if(!ka(e))throw Error(r(405));var a=s!=null&&s.hydratedSources||null,c=!1,d="",m=nd;if(s!=null&&(s.unstable_strictMode===!0&&(c=!0),s.identifierPrefix!==void 0&&(d=s.identifierPrefix),s.onRecoverableError!==void 0&&(m=s.onRecoverableError)),t=ed(t,null,e,1,s??null,c,!1,d,m),e[Kt]=t.current,is(e),a)for(e=0;e<a.length;e++)s=a[e],c=s._getVersion,c=c(s._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[s,c]:t.mutableSourceEagerHydrationData.push(s,c);return new zi(t)},ut.render=function(e,t,s){if(!Ui(t))throw Error(r(200));return Vi(null,e,t,!1,s)},ut.unmountComponentAtNode=function(e){if(!Ui(e))throw Error(r(40));return e._reactRootContainer?(Pn(function(){Vi(null,null,e,!1,function(){e._reactRootContainer=null,e[Kt]=null})}),!0):!1},ut.unstable_batchedUpdates=da,ut.unstable_renderSubtreeIntoContainer=function(e,t,s,a){if(!Ui(s))throw Error(r(200));if(e==null||e._reactInternals===void 0)throw Error(r(38));return Vi(e,t,s,!1,a)},ut.version="18.3.1-next-f1338f8080-20240426",ut}var hd;function cg(){if(hd)return xa.exports;hd=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(n){console.error(n)}}return i(),xa.exports=ug(),xa.exports}var pd;function fg(){if(pd)return Wi;pd=1;var i=cg();return Wi.createRoot=i.createRoot,Wi.hydrateRoot=i.hydrateRoot,Wi}var dg=fg();const hg="modulepreload",pg=function(i){return"/"+i},md={},mg=function(n,r,l){let o=Promise.resolve();if(r&&r.length>0){let f=function(g){return Promise.all(g.map(x=>Promise.resolve(x).then(S=>({status:"fulfilled",value:S}),S=>({status:"rejected",reason:S}))))};document.getElementsByTagName("link");const h=document.querySelector("meta[property=csp-nonce]"),p=(h==null?void 0:h.nonce)||(h==null?void 0:h.getAttribute("nonce"));o=f(r.map(g=>{if(g=pg(g),g in md)return;md[g]=!0;const x=g.endsWith(".css"),S=x?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${g}"]${S}`))return;const T=document.createElement("link");if(T.rel=x?"stylesheet":hg,x||(T.as="script"),T.crossOrigin="",T.href=g,p&&T.setAttribute("nonce",p),document.head.appendChild(T),x)return new Promise((E,O)=>{T.addEventListener("load",E),T.addEventListener("error",()=>O(new Error(`Unable to preload CSS for ${g}`)))})}))}function u(f){const h=new Event("vite:preloadError",{cancelable:!0});if(h.payload=f,window.dispatchEvent(h),!h.defaultPrevented)throw f}return o.then(f=>{for(const h of f||[])h.status==="rejected"&&u(h.reason);return n().catch(u)})};function gg(i,n){const r=/(\x1b\[(\d+(;\d+)*)m)|([^\x1b]+)/g,l=[];let o,u={},f=!1,h=n==null?void 0:n.fg,p=n==null?void 0:n.bg;for(;(o=r.exec(i))!==null;){const[,,g,,x]=o;if(g){const S=+g;switch(S){case 0:u={};break;case 1:u["font-weight"]="bold";break;case 2:u.opacity="0.8";break;case 3:u["font-style"]="italic";break;case 4:u["text-decoration"]="underline";break;case 7:f=!0;break;case 8:u.display="none";break;case 9:u["text-decoration"]="line-through";break;case 22:delete u["font-weight"],delete u["font-style"],delete u.opacity,delete u["text-decoration"];break;case 23:delete u["font-weight"],delete u["font-style"],delete u.opacity;break;case 24:delete u["text-decoration"];break;case 27:f=!1;break;case 30:case 31:case 32:case 33:case 34:case 35:case 36:case 37:h=gd[S-30];break;case 39:h=n==null?void 0:n.fg;break;case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:p=gd[S-40];break;case 49:p=n==null?void 0:n.bg;break;case 53:u["text-decoration"]="overline";break;case 90:case 91:case 92:case 93:case 94:case 95:case 96:case 97:h=yd[S-90];break;case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:p=yd[S-100];break}}else if(x){const S={...u},T=f?p:h;T!==void 0&&(S.color=T);const E=f?h:p;E!==void 0&&(S["background-color"]=E),l.push(`<span style="${vg(S)}">${yg(x)}</span>`)}}return l.join("")}const gd={0:"var(--vscode-terminal-ansiBlack)",1:"var(--vscode-terminal-ansiRed)",2:"var(--vscode-terminal-ansiGreen)",3:"var(--vscode-terminal-ansiYellow)",4:"var(--vscode-terminal-ansiBlue)",5:"var(--vscode-terminal-ansiMagenta)",6:"var(--vscode-terminal-ansiCyan)",7:"var(--vscode-terminal-ansiWhite)"},yd={0:"var(--vscode-terminal-ansiBrightBlack)",1:"var(--vscode-terminal-ansiBrightRed)",2:"var(--vscode-terminal-ansiBrightGreen)",3:"var(--vscode-terminal-ansiBrightYellow)",4:"var(--vscode-terminal-ansiBrightBlue)",5:"var(--vscode-terminal-ansiBrightMagenta)",6:"var(--vscode-terminal-ansiBrightCyan)",7:"var(--vscode-terminal-ansiBrightWhite)"};function yg(i){return i.replace(/[&"<>]/g,n=>({"&":"&amp;",'"':"&quot;","<":"&lt;",">":"&gt;"})[n])}function vg(i){return Object.entries(i).map(([n,r])=>`${n}: ${r}`).join("; ")}const _a=({text:i,language:n,mimeType:r,linkify:l,readOnly:o,highlight:u,revealLine:f,lineNumbers:h,isFocused:p,focusOnChange:g,wrapLines:x,onChange:S,dataTestId:T,placeholder:E})=>{const[O,v]=jd(),[w]=ye.useState(mg(()=>import("./codeMirrorModule-DRsk21vu.js"),__vite__mapDeps([0,1])).then(V=>V.default)),I=ye.useRef(null),[A,F]=ye.useState();return ye.useEffect(()=>{(async()=>{var z,P;const V=await w;Sg(V);const W=v.current;if(!W)return;const j=Eg(n)||kg(r)||(l?"text/linkified":"");if(I.current&&j===I.current.cm.getOption("mode")&&!!o===I.current.cm.getOption("readOnly")&&h===I.current.cm.getOption("lineNumbers")&&x===I.current.cm.getOption("lineWrapping")&&E===I.current.cm.getOption("placeholder"))return;(P=(z=I.current)==null?void 0:z.cm)==null||P.getWrapperElement().remove();const Y=V(W,{value:"",mode:j,readOnly:!!o,lineNumbers:h,lineWrapping:x,placeholder:E});return I.current={cm:Y},p&&Y.focus(),F(Y),Y})()},[w,A,v,n,r,l,h,x,o,p,E]),ye.useEffect(()=>{I.current&&I.current.cm.setSize(O.width,O.height)},[O]),ye.useLayoutEffect(()=>{var j;if(!A)return;let V=!1;if(A.getValue()!==i&&(A.setValue(i),V=!0,g&&(A.execCommand("selectAll"),A.focus())),V||JSON.stringify(u)!==JSON.stringify(I.current.highlight)){for(const P of I.current.highlight||[])A.removeLineClass(P.line-1,"wrap");for(const P of u||[])A.addLineClass(P.line-1,"wrap",`source-line-${P.type}`);for(const P of I.current.widgets||[])A.removeLineWidget(P);for(const P of I.current.markers||[])P.clear();const Y=[],z=[];for(const P of u||[]){if(P.type!=="subtle-error"&&P.type!=="error")continue;const le=(j=I.current)==null?void 0:j.cm.getLine(P.line-1);if(le){const ue={};ue.title=P.message||"",z.push(A.markText({line:P.line-1,ch:0},{line:P.line-1,ch:P.column||le.length},{className:"source-line-error-underline",attributes:ue}))}if(P.type==="error"){const ue=document.createElement("div");ue.innerHTML=gg(P.message||""),ue.className="source-line-error-widget",Y.push(A.addLineWidget(P.line,ue,{above:!0,coverGutter:!1}))}}I.current.highlight=u,I.current.widgets=Y,I.current.markers=z}typeof f=="number"&&I.current.cm.lineCount()>=f&&A.scrollIntoView({line:Math.max(0,f-1),ch:0},50);let W;return S&&(W=()=>S(A.getValue()),A.on("change",W)),()=>{W&&A.off("change",W)}},[A,i,u,f,g,S]),H.jsx("div",{"data-testid":T,className:"cm-wrapper",ref:v,onClick:wg})};function wg(i){var r;if(!(i.target instanceof HTMLElement))return;let n;i.target.classList.contains("cm-linkified")?n=i.target.textContent:i.target.classList.contains("cm-link")&&((r=i.target.nextElementSibling)!=null&&r.classList.contains("cm-url"))&&(n=i.target.nextElementSibling.textContent.slice(1,-1)),n&&(i.preventDefault(),i.stopPropagation(),window.open(n,"_blank"))}let vd=!1;function Sg(i){vd||(vd=!0,i.defineSimpleMode("text/linkified",{start:[{regex:ng,token:"linkified"}]}))}function kg(i){if(i){if(i.includes("javascript")||i.includes("json"))return"javascript";if(i.includes("python"))return"python";if(i.includes("csharp"))return"text/x-csharp";if(i.includes("java"))return"text/x-java";if(i.includes("markdown"))return"markdown";if(i.includes("html")||i.includes("svg"))return"htmlmixed";if(i.includes("css"))return"css"}}function Eg(i){if(i)return{javascript:"javascript",jsonl:"javascript",python:"python",csharp:"text/x-csharp",java:"text/x-java",markdown:"markdown",html:"htmlmixed",css:"css",yaml:"yaml"}[i]}const Ng=50,xg=({sidebarSize:i,sidebarHidden:n=!1,sidebarIsFirst:r=!1,orientation:l="vertical",minSidebarSize:o=Ng,settingName:u,sidebar:f,main:h})=>{const p=Math.max(o,i)*window.devicePixelRatio,[g,x]=Da(u?u+"."+l+":size":void 0,p),[S,T]=Da(u?u+"."+l+":size":void 0,p),[E,O]=ye.useState(null),[v,w]=jd();let I;l==="vertical"?(I=S/window.devicePixelRatio,v&&v.height<I&&(I=v.height-10)):(I=g/window.devicePixelRatio,v&&v.width<I&&(I=v.width-10)),document.body.style.userSelect=E?"none":"inherit";let A={};return l==="vertical"?r?A={top:E?0:I-4,bottom:E?0:void 0,height:E?"initial":8}:A={bottom:E?0:I-4,top:E?0:void 0,height:E?"initial":8}:r?A={left:E?0:I-4,right:E?0:void 0,width:E?"initial":8}:A={right:E?0:I-4,left:E?0:void 0,width:E?"initial":8},H.jsxs("div",{className:Un("split-view",l,r&&"sidebar-first"),ref:w,children:[H.jsx("div",{className:"split-view-main",children:h}),!n&&H.jsx("div",{style:{flexBasis:I},className:"split-view-sidebar",children:f}),!n&&H.jsx("div",{style:A,className:"split-view-resizer",onMouseDown:F=>O({offset:l==="vertical"?F.clientY:F.clientX,size:I}),onMouseUp:()=>O(null),onMouseMove:F=>{if(!F.buttons)O(null);else if(E){const W=(l==="vertical"?F.clientY:F.clientX)-E.offset,j=r?E.size+W:E.size-W,z=F.target.parentElement.getBoundingClientRect(),P=Math.min(Math.max(o,j),(l==="vertical"?z.height:z.width)-o);l==="vertical"?T(P*window.devicePixelRatio):x(P*window.devicePixelRatio)}}})]})},Rd=({noShadow:i,children:n,noMinHeight:r,className:l,sidebarBackground:o,onClick:u})=>H.jsx("div",{className:Un("toolbar",i&&"no-shadow",r&&"no-min-height",l,o&&"toolbar-sidebar-background"),onClick:u,children:n}),Tg=({tabs:i,selectedTab:n,setSelectedTab:r,leftToolbar:l,rightToolbar:o,dataTestId:u,mode:f})=>{const h=ye.useId();return n||(n=i[0].id),f||(f="default"),H.jsx("div",{className:"tabbed-pane","data-testid":u,children:H.jsxs("div",{className:"vbox",children:[H.jsxs(Rd,{children:[l&&H.jsxs("div",{style:{flex:"none",display:"flex",margin:"0 4px",alignItems:"center"},children:[...l]}),f==="default"&&H.jsx("div",{style:{flex:"auto",display:"flex",height:"100%",overflow:"hidden"},role:"tablist",children:[...i.map(p=>H.jsx(Cg,{id:p.id,ariaControls:`${h}-${p.id}`,title:p.title,count:p.count,errorCount:p.errorCount,selected:n===p.id,onSelect:r},p.id))]}),f==="select"&&H.jsx("div",{style:{flex:"auto",display:"flex",height:"100%",overflow:"hidden"},role:"tablist",children:H.jsx("select",{style:{width:"100%",background:"none",cursor:"pointer"},value:n,onChange:p=>{r==null||r(i[p.currentTarget.selectedIndex].id)},children:i.map(p=>{let g="";return p.count&&(g=` (${p.count})`),p.errorCount&&(g=` (${p.errorCount})`),H.jsxs("option",{value:p.id,role:"tab","aria-controls":`${h}-${p.id}`,children:[p.title,g]},p.id)})})}),o&&H.jsxs("div",{style:{flex:"none",display:"flex",alignItems:"center"},children:[...o]})]}),i.map(p=>{const g="tab-content tab-"+p.id;if(p.component)return H.jsx("div",{id:`${h}-${p.id}`,role:"tabpanel","aria-label":p.title,className:g,style:{display:n===p.id?"inherit":"none"},children:p.component},p.id);if(n===p.id)return H.jsx("div",{id:`${h}-${p.id}`,role:"tabpanel","aria-label":p.title,className:g,children:p.render()},p.id)})]})})},Cg=({id:i,title:n,count:r,errorCount:l,selected:o,onSelect:u,ariaControls:f})=>H.jsxs("div",{className:Un("tabbed-pane-tab",o&&"selected"),onClick:()=>u==null?void 0:u(i),role:"tab",title:n,"aria-controls":f,children:[H.jsx("div",{className:"tabbed-pane-tab-label",children:n}),!!r&&H.jsx("div",{className:"tabbed-pane-tab-counter",children:r}),!!l&&H.jsx("div",{className:"tabbed-pane-tab-counter error",children:l})]}),_g=({sources:i,fileId:n,setFileId:r})=>H.jsx("select",{className:"source-chooser",hidden:!i.length,title:"Source chooser",value:n,onChange:l=>{r(l.target.selectedOptions[0].value)},children:Lg(i)});function Lg(i){const n=o=>o.replace(/.*[/\\]([^/\\]+)/,"$1"),r=o=>H.jsx("option",{value:o.id,children:n(o.label)},o.id),l=new Map;for(const o of i){let u=l.get(o.group||"Debugger");u||(u=[],l.set(o.group||"Debugger",u)),u.push(o)}return[...l.entries()].map(([o,u])=>H.jsx("optgroup",{label:o,children:u.filter(f=>(f.group||"Debugger")===o).map(f=>r(f))},o))}function Og(){return{id:"default",isRecorded:!1,text:"",language:"javascript",label:"",highlight:[]}}const vt=ye.forwardRef(function({children:n,title:r="",icon:l,disabled:o=!1,toggled:u=!1,onClick:f=()=>{},style:h,testId:p,className:g,ariaLabel:x},S){return H.jsxs("button",{ref:S,className:Un(g,"toolbar-button",l,u&&"toggled"),onMouseDown:Sd,onClick:f,onDoubleClick:Sd,title:r,disabled:!!o,style:h,"data-testid":p,"aria-label":x||r,children:[l&&H.jsx("span",{className:`codicon codicon-${l}`,style:n?{marginRight:5}:{}}),n]})}),wd=({style:i})=>H.jsx("div",{className:"toolbar-separator",style:i}),Sd=i=>{i.stopPropagation(),i.preventDefault()},De=function(i,n,r){return i>=n&&i<=r};function ct(i){return De(i,48,57)}function kd(i){return ct(i)||De(i,65,70)||De(i,97,102)}function $g(i){return De(i,65,90)}function Ig(i){return De(i,97,122)}function Ag(i){return $g(i)||Ig(i)}function Pg(i){return i>=128}function Gi(i){return Ag(i)||Pg(i)||i===95}function Ed(i){return Gi(i)||ct(i)||i===45}function Mg(i){return De(i,0,8)||i===11||De(i,14,31)||i===127}function Xi(i){return i===10}function Xt(i){return Xi(i)||i===9||i===32}const jg=1114111;class Ha extends Error{constructor(n){super(n),this.name="InvalidCharacterError"}}function Rg(i){const n=[];for(let r=0;r<i.length;r++){let l=i.charCodeAt(r);if(l===13&&i.charCodeAt(r+1)===10&&(l=10,r++),(l===13||l===12)&&(l=10),l===0&&(l=65533),De(l,55296,56319)&&De(i.charCodeAt(r+1),56320,57343)){const o=l-55296,u=i.charCodeAt(r+1)-56320;l=Math.pow(2,16)+o*Math.pow(2,10)+u,r++}n.push(l)}return n}function Fe(i){if(i<=65535)return String.fromCharCode(i);i-=Math.pow(2,16);const n=Math.floor(i/Math.pow(2,10))+55296,r=i%Math.pow(2,10)+56320;return String.fromCharCode(n)+String.fromCharCode(r)}function Dg(i){const n=Rg(i);let r=-1;const l=[];let o;const u=function(M){return M>=n.length?-1:n[M]},f=function(M){if(M===void 0&&(M=1),M>3)throw"Spec Error: no more than three codepoints of lookahead.";return u(r+M)},h=function(M){return M===void 0&&(M=1),r+=M,o=u(r),!0},p=function(){return r-=1,!0},g=function(M){return M===void 0&&(M=o),M===-1},x=function(){if(S(),h(),Xt(o)){for(;Xt(f());)h();return new ba}else{if(o===34)return O();if(o===35)if(Ed(f())||I(f(1),f(2))){const M=new Gd("");return F(f(1),f(2),f(3))&&(M.type="id"),M.value=Y(),M}else return new Xe(o);else return o===36?f()===61?(h(),new zg):new Xe(o):o===39?O():o===40?new qd:o===41?new Hd:o===42?f()===61?(h(),new Ug):new Xe(o):o===43?j()?(p(),T()):new Xe(o):o===44?new Ud:o===45?j()?(p(),T()):f(1)===45&&f(2)===62?(h(2),new Bd):V()?(p(),E()):new Xe(o):o===46?j()?(p(),T()):new Xe(o):o===58?new Fd:o===59?new zd:o===60?f(1)===33&&f(2)===45&&f(3)===45?(h(3),new bd):new Xe(o):o===64?F(f(1),f(2),f(3))?new Yd(Y()):new Xe(o):o===91?new Wd:o===92?A()?(p(),E()):new Xe(o):o===93?new Ba:o===94?f()===61?(h(),new Fg):new Xe(o):o===123?new Vd:o===124?f()===61?(h(),new Bg):f()===124?(h(),new Qd):new Xe(o):o===125?new Kd:o===126?f()===61?(h(),new bg):new Xe(o):ct(o)?(p(),T()):Gi(o)?(p(),E()):g()?new el:new Xe(o)}},S=function(){for(;f(1)===47&&f(2)===42;)for(h(2);;)if(h(),o===42&&f()===47){h();break}else if(g())return},T=function(){const M=z();if(F(f(1),f(2),f(3))){const ee=new Vg;return ee.value=M.value,ee.repr=M.repr,ee.type=M.type,ee.unit=Y(),ee}else if(f()===37){h();const ee=new th;return ee.value=M.value,ee.repr=M.repr,ee}else{const ee=new eh;return ee.value=M.value,ee.repr=M.repr,ee.type=M.type,ee}},E=function(){const M=Y();if(M.toLowerCase()==="url"&&f()===40){for(h();Xt(f(1))&&Xt(f(2));)h();return f()===34||f()===39?new tl(M):Xt(f())&&(f(2)===34||f(2)===39)?new tl(M):v()}else return f()===40?(h(),new tl(M)):new Jd(M)},O=function(M){M===void 0&&(M=o);let ee="";for(;h();){if(o===M||g())return new Xd(ee);if(Xi(o))return p(),new Dd;o===92?g(f())||(Xi(f())?h():ee+=Fe(w())):ee+=Fe(o)}throw new Error("Internal error")},v=function(){const M=new Zd("");for(;Xt(f());)h();if(g(f()))return M;for(;h();){if(o===41||g())return M;if(Xt(o)){for(;Xt(f());)h();return f()===41||g(f())?(h(),M):(le(),new Zi)}else{if(o===34||o===39||o===40||Mg(o))return le(),new Zi;if(o===92)if(A())M.value+=Fe(w());else return le(),new Zi;else M.value+=Fe(o)}}throw new Error("Internal error")},w=function(){if(h(),kd(o)){const M=[o];for(let ke=0;ke<5&&kd(f());ke++)h(),M.push(o);Xt(f())&&h();let ee=parseInt(M.map(function(ke){return String.fromCharCode(ke)}).join(""),16);return ee>jg&&(ee=65533),ee}else return g()?65533:o},I=function(M,ee){return!(M!==92||Xi(ee))},A=function(){return I(o,f())},F=function(M,ee,ke){return M===45?Gi(ee)||ee===45||I(ee,ke):Gi(M)?!0:M===92?I(M,ee):!1},V=function(){return F(o,f(1),f(2))},W=function(M,ee,ke){return M===43||M===45?!!(ct(ee)||ee===46&&ct(ke)):M===46?!!ct(ee):!!ct(M)},j=function(){return W(o,f(1),f(2))},Y=function(){let M="";for(;h();)if(Ed(o))M+=Fe(o);else if(A())M+=Fe(w());else return p(),M;throw new Error("Internal parse error")},z=function(){let M="",ee="integer";for((f()===43||f()===45)&&(h(),M+=Fe(o));ct(f());)h(),M+=Fe(o);if(f(1)===46&&ct(f(2)))for(h(),M+=Fe(o),h(),M+=Fe(o),ee="number";ct(f());)h(),M+=Fe(o);const ke=f(1),rt=f(2),He=f(3);if((ke===69||ke===101)&&ct(rt))for(h(),M+=Fe(o),h(),M+=Fe(o),ee="number";ct(f());)h(),M+=Fe(o);else if((ke===69||ke===101)&&(rt===43||rt===45)&&ct(He))for(h(),M+=Fe(o),h(),M+=Fe(o),h(),M+=Fe(o),ee="number";ct(f());)h(),M+=Fe(o);const xe=P(M);return{type:ee,value:xe,repr:M}},P=function(M){return+M},le=function(){for(;h();){if(o===41||g())return;A()&&w()}};let ue=0;for(;!g(f());)if(l.push(x()),ue++,ue>n.length*2)throw new Error("I'm infinite-looping!");return l}class je{constructor(){this.tokenType=""}toJSON(){return{token:this.tokenType}}toString(){return this.tokenType}toSource(){return""+this}}class Dd extends je{constructor(){super(...arguments),this.tokenType="BADSTRING"}}class Zi extends je{constructor(){super(...arguments),this.tokenType="BADURL"}}class ba extends je{constructor(){super(...arguments),this.tokenType="WHITESPACE"}toString(){return"WS"}toSource(){return" "}}class bd extends je{constructor(){super(...arguments),this.tokenType="CDO"}toSource(){return"<!--"}}class Bd extends je{constructor(){super(...arguments),this.tokenType="CDC"}toSource(){return"-->"}}class Fd extends je{constructor(){super(...arguments),this.tokenType=":"}}class zd extends je{constructor(){super(...arguments),this.tokenType=";"}}class Ud extends je{constructor(){super(...arguments),this.tokenType=","}}class $r extends je{constructor(){super(...arguments),this.value="",this.mirror=""}}class Vd extends $r{constructor(){super(),this.tokenType="{",this.value="{",this.mirror="}"}}class Kd extends $r{constructor(){super(),this.tokenType="}",this.value="}",this.mirror="{"}}class Wd extends $r{constructor(){super(),this.tokenType="[",this.value="[",this.mirror="]"}}class Ba extends $r{constructor(){super(),this.tokenType="]",this.value="]",this.mirror="["}}class qd extends $r{constructor(){super(),this.tokenType="(",this.value="(",this.mirror=")"}}class Hd extends $r{constructor(){super(),this.tokenType=")",this.value=")",this.mirror="("}}class bg extends je{constructor(){super(...arguments),this.tokenType="~="}}class Bg extends je{constructor(){super(...arguments),this.tokenType="|="}}class Fg extends je{constructor(){super(...arguments),this.tokenType="^="}}class zg extends je{constructor(){super(...arguments),this.tokenType="$="}}class Ug extends je{constructor(){super(...arguments),this.tokenType="*="}}class Qd extends je{constructor(){super(...arguments),this.tokenType="||"}}class el extends je{constructor(){super(...arguments),this.tokenType="EOF"}toSource(){return""}}class Xe extends je{constructor(n){super(),this.tokenType="DELIM",this.value="",this.value=Fe(n)}toString(){return"DELIM("+this.value+")"}toJSON(){const n=this.constructor.prototype.constructor.prototype.toJSON.call(this);return n.value=this.value,n}toSource(){return this.value==="\\"?`\\
`:this.value}}class Ir extends je{constructor(){super(...arguments),this.value=""}ASCIIMatch(n){return this.value.toLowerCase()===n.toLowerCase()}toJSON(){const n=this.constructor.prototype.constructor.prototype.toJSON.call(this);return n.value=this.value,n}}class Jd extends Ir{constructor(n){super(),this.tokenType="IDENT",this.value=n}toString(){return"IDENT("+this.value+")"}toSource(){return Is(this.value)}}class tl extends Ir{constructor(n){super(),this.tokenType="FUNCTION",this.value=n,this.mirror=")"}toString(){return"FUNCTION("+this.value+")"}toSource(){return Is(this.value)+"("}}class Yd extends Ir{constructor(n){super(),this.tokenType="AT-KEYWORD",this.value=n}toString(){return"AT("+this.value+")"}toSource(){return"@"+Is(this.value)}}class Gd extends Ir{constructor(n){super(),this.tokenType="HASH",this.value=n,this.type="unrestricted"}toString(){return"HASH("+this.value+")"}toJSON(){const n=this.constructor.prototype.constructor.prototype.toJSON.call(this);return n.value=this.value,n.type=this.type,n}toSource(){return this.type==="id"?"#"+Is(this.value):"#"+Kg(this.value)}}class Xd extends Ir{constructor(n){super(),this.tokenType="STRING",this.value=n}toString(){return'"'+nh(this.value)+'"'}}class Zd extends Ir{constructor(n){super(),this.tokenType="URL",this.value=n}toString(){return"URL("+this.value+")"}toSource(){return'url("'+nh(this.value)+'")'}}class eh extends je{constructor(){super(),this.tokenType="NUMBER",this.type="integer",this.repr=""}toString(){return this.type==="integer"?"INT("+this.value+")":"NUMBER("+this.value+")"}toJSON(){const n=super.toJSON();return n.value=this.value,n.type=this.type,n.repr=this.repr,n}toSource(){return this.repr}}class th extends je{constructor(){super(),this.tokenType="PERCENTAGE",this.repr=""}toString(){return"PERCENTAGE("+this.value+")"}toJSON(){const n=this.constructor.prototype.constructor.prototype.toJSON.call(this);return n.value=this.value,n.repr=this.repr,n}toSource(){return this.repr+"%"}}class Vg extends je{constructor(){super(),this.tokenType="DIMENSION",this.type="integer",this.repr="",this.unit=""}toString(){return"DIM("+this.value+","+this.unit+")"}toJSON(){const n=this.constructor.prototype.constructor.prototype.toJSON.call(this);return n.value=this.value,n.type=this.type,n.repr=this.repr,n.unit=this.unit,n}toSource(){const n=this.repr;let r=Is(this.unit);return r[0].toLowerCase()==="e"&&(r[1]==="-"||De(r.charCodeAt(1),48,57))&&(r="\\65 "+r.slice(1,r.length)),n+r}}function Is(i){i=""+i;let n="";const r=i.charCodeAt(0);for(let l=0;l<i.length;l++){const o=i.charCodeAt(l);if(o===0)throw new Ha("Invalid character: the input contains U+0000.");De(o,1,31)||o===127||l===0&&De(o,48,57)||l===1&&De(o,48,57)&&r===45?n+="\\"+o.toString(16)+" ":o>=128||o===45||o===95||De(o,48,57)||De(o,65,90)||De(o,97,122)?n+=i[l]:n+="\\"+i[l]}return n}function Kg(i){i=""+i;let n="";for(let r=0;r<i.length;r++){const l=i.charCodeAt(r);if(l===0)throw new Ha("Invalid character: the input contains U+0000.");l>=128||l===45||l===95||De(l,48,57)||De(l,65,90)||De(l,97,122)?n+=i[r]:n+="\\"+l.toString(16)+" "}return n}function nh(i){i=""+i;let n="";for(let r=0;r<i.length;r++){const l=i.charCodeAt(r);if(l===0)throw new Ha("Invalid character: the input contains U+0000.");De(l,1,31)||l===127?n+="\\"+l.toString(16)+" ":l===34||l===92?n+="\\"+i[r]:n+=i[r]}return n}class ft extends Error{}function Wg(i,n){let r;try{r=Dg(i),r[r.length-1]instanceof el||r.push(new el)}catch(P){const le=P.message+` while parsing css selector "${i}". Did you mean to CSS.escape it?`,ue=(P.stack||"").indexOf(P.message);throw ue!==-1&&(P.stack=P.stack.substring(0,ue)+le+P.stack.substring(ue+P.message.length)),P.message=le,P}const l=r.find(P=>P instanceof Yd||P instanceof Dd||P instanceof Zi||P instanceof Qd||P instanceof bd||P instanceof Bd||P instanceof zd||P instanceof Vd||P instanceof Kd||P instanceof Zd||P instanceof th);if(l)throw new ft(`Unsupported token "${l.toSource()}" while parsing css selector "${i}". Did you mean to CSS.escape it?`);let o=0;const u=new Set;function f(){return new ft(`Unexpected token "${r[o].toSource()}" while parsing css selector "${i}". Did you mean to CSS.escape it?`)}function h(){for(;r[o]instanceof ba;)o++}function p(P=o){return r[P]instanceof Jd}function g(P=o){return r[P]instanceof Xd}function x(P=o){return r[P]instanceof eh}function S(P=o){return r[P]instanceof Ud}function T(P=o){return r[P]instanceof qd}function E(P=o){return r[P]instanceof Hd}function O(P=o){return r[P]instanceof tl}function v(P=o){return r[P]instanceof Xe&&r[P].value==="*"}function w(P=o){return r[P]instanceof el}function I(P=o){return r[P]instanceof Xe&&[">","+","~"].includes(r[P].value)}function A(P=o){return S(P)||E(P)||w(P)||I(P)||r[P]instanceof ba}function F(){const P=[V()];for(;h(),!!S();)o++,P.push(V());return P}function V(){return h(),x()||g()?r[o++].value:W()}function W(){const P={simples:[]};for(h(),I()?P.simples.push({selector:{functions:[{name:"scope",args:[]}]},combinator:""}):P.simples.push({selector:j(),combinator:""});;){if(h(),I())P.simples[P.simples.length-1].combinator=r[o++].value,h();else if(A())break;P.simples.push({combinator:"",selector:j()})}return P}function j(){let P="";const le=[];for(;!A();)if(p()||v())P+=r[o++].toSource();else if(r[o]instanceof Gd)P+=r[o++].toSource();else if(r[o]instanceof Xe&&r[o].value===".")if(o++,p())P+="."+r[o++].toSource();else throw f();else if(r[o]instanceof Fd)if(o++,p())if(!n.has(r[o].value.toLowerCase()))P+=":"+r[o++].toSource();else{const ue=r[o++].value.toLowerCase();le.push({name:ue,args:[]}),u.add(ue)}else if(O()){const ue=r[o++].value.toLowerCase();if(n.has(ue)?(le.push({name:ue,args:F()}),u.add(ue)):P+=`:${ue}(${Y()})`,h(),!E())throw f();o++}else throw f();else if(r[o]instanceof Wd){for(P+="[",o++;!(r[o]instanceof Ba)&&!w();)P+=r[o++].toSource();if(!(r[o]instanceof Ba))throw f();P+="]",o++}else throw f();if(!P&&!le.length)throw f();return{css:P||void 0,functions:le}}function Y(){let P="",le=1;for(;!w()&&((T()||O())&&le++,E()&&le--,!!le);)P+=r[o++].toSource();return P}const z=F();if(!w())throw f();if(z.some(P=>typeof P!="object"||!("simples"in P)))throw new ft(`Error while parsing css selector "${i}". Did you mean to CSS.escape it?`);return{selector:z,names:Array.from(u)}}const Nd=new Set(["internal:has","internal:has-not","internal:and","internal:or","internal:chain","left-of","right-of","above","below","near"]),qg=new Set(["left-of","right-of","above","below","near"]),Hg=new Set(["not","is","where","has","scope","light","visible","text","text-matches","text-is","has-text","above","below","right-of","left-of","near","nth-match"]);function rh(i){const n=Jg(i),r=[];for(const l of n.parts){if(l.name==="css"||l.name==="css:light"){l.name==="css:light"&&(l.body=":light("+l.body+")");const o=Wg(l.body,Hg);r.push({name:"css",body:o.selector,source:l.body});continue}if(Nd.has(l.name)){let o,u;try{const g=JSON.parse("["+l.body+"]");if(!Array.isArray(g)||g.length<1||g.length>2||typeof g[0]!="string")throw new ft(`Malformed selector: ${l.name}=`+l.body);if(o=g[0],g.length===2){if(typeof g[1]!="number"||!qg.has(l.name))throw new ft(`Malformed selector: ${l.name}=`+l.body);u=g[1]}}catch{throw new ft(`Malformed selector: ${l.name}=`+l.body)}const f={name:l.name,source:l.body,body:{parsed:rh(o),distance:u}},h=[...f.body.parsed.parts].reverse().find(g=>g.name==="internal:control"&&g.body==="enter-frame"),p=h?f.body.parsed.parts.indexOf(h):-1;p!==-1&&Qg(f.body.parsed.parts.slice(0,p+1),r.slice(0,p+1))&&f.body.parsed.parts.splice(0,p+1),r.push(f);continue}r.push({...l,source:l.body})}if(Nd.has(r[0].name))throw new ft(`"${r[0].name}" selector cannot be first`);return{capture:n.capture,parts:r}}function Qg(i,n){return Sr({parts:i})===Sr({parts:n})}function Sr(i,n){return typeof i=="string"?i:i.parts.map((r,l)=>{let o=!0;!n&&l!==i.capture&&(r.name==="css"||r.name==="xpath"&&r.source.startsWith("//")||r.source.startsWith(".."))&&(o=!1);const u=o?r.name+"=":"";return`${l===i.capture?"*":""}${u}${r.source}`}).join(" >> ")}function Jg(i){let n=0,r,l=0;const o={parts:[]},u=()=>{const h=i.substring(l,n).trim(),p=h.indexOf("=");let g,x;p!==-1&&h.substring(0,p).trim().match(/^[a-zA-Z_0-9-+:*]+$/)?(g=h.substring(0,p).trim(),x=h.substring(p+1)):h.length>1&&h[0]==='"'&&h[h.length-1]==='"'||h.length>1&&h[0]==="'"&&h[h.length-1]==="'"?(g="text",x=h):/^\(*\/\//.test(h)||h.startsWith("..")?(g="xpath",x=h):(g="css",x=h);let S=!1;if(g[0]==="*"&&(S=!0,g=g.substring(1)),o.parts.push({name:g,body:x}),S){if(o.capture!==void 0)throw new ft("Only one of the selectors can capture using * modifier");o.capture=o.parts.length-1}};if(!i.includes(">>"))return n=i.length,u(),o;const f=()=>{const p=i.substring(l,n).match(/^\s*text\s*=(.*)$/);return!!p&&!!p[1]};for(;n<i.length;){const h=i[n];h==="\\"&&n+1<i.length?n+=2:h===r?(r=void 0,n++):!r&&(h==='"'||h==="'"||h==="`")&&!f()?(r=h,n++):!r&&h===">"&&i[n+1]===">"?(u(),n+=2,l=n):n++}return u(),o}function La(i,n){let r=0,l=i.length===0;const o=()=>i[r]||"",u=()=>{const w=o();return++r,l=r>=i.length,w},f=w=>{throw l?new ft(`Unexpected end of selector while parsing selector \`${i}\``):new ft(`Error while parsing selector \`${i}\` - unexpected symbol "${o()}" at position ${r}`+(w?" during "+w:""))};function h(){for(;!l&&/\s/.test(o());)u()}function p(w){return w>=""||w>="0"&&w<="9"||w>="A"&&w<="Z"||w>="a"&&w<="z"||w>="0"&&w<="9"||w==="_"||w==="-"}function g(){let w="";for(h();!l&&p(o());)w+=u();return w}function x(w){let I=u();for(I!==w&&f("parsing quoted string");!l&&o()!==w;)o()==="\\"&&u(),I+=u();return o()!==w&&f("parsing quoted string"),I+=u(),I}function S(){u()!=="/"&&f("parsing regular expression");let w="",I=!1;for(;!l;){if(o()==="\\")w+=u(),l&&f("parsing regular expression");else if(I&&o()==="]")I=!1;else if(!I&&o()==="[")I=!0;else if(!I&&o()==="/")break;w+=u()}u()!=="/"&&f("parsing regular expression");let A="";for(;!l&&o().match(/[dgimsuy]/);)A+=u();try{return new RegExp(w,A)}catch(F){throw new ft(`Error while parsing selector \`${i}\`: ${F.message}`)}}function T(){let w="";return h(),o()==="'"||o()==='"'?w=x(o()).slice(1,-1):w=g(),w||f("parsing property path"),w}function E(){h();let w="";return l||(w+=u()),!l&&w!=="="&&(w+=u()),["=","*=","^=","$=","|=","~="].includes(w)||f("parsing operator"),w}function O(){u();const w=[];for(w.push(T()),h();o()===".";)u(),w.push(T()),h();if(o()==="]")return u(),{name:w.join("."),jsonPath:w,op:"<truthy>",value:null,caseSensitive:!1};const I=E();let A,F=!0;if(h(),o()==="/"){if(I!=="=")throw new ft(`Error while parsing selector \`${i}\` - cannot use ${I} in attribute with regular expression`);A=S()}else if(o()==="'"||o()==='"')A=x(o()).slice(1,-1),h(),o()==="i"||o()==="I"?(F=!1,u()):(o()==="s"||o()==="S")&&(F=!0,u());else{for(A="";!l&&(p(o())||o()==="+"||o()===".");)A+=u();A==="true"?A=!0:A==="false"&&(A=!1)}if(h(),o()!=="]"&&f("parsing attribute value"),u(),I!=="="&&typeof A!="string")throw new ft(`Error while parsing selector \`${i}\` - cannot use ${I} in attribute with non-string matching value - ${A}`);return{name:w.join("."),jsonPath:w,op:I,value:A,caseSensitive:F}}const v={name:"",attributes:[]};for(v.name=g(),h();o()==="[";)v.attributes.push(O()),h();if(l||f(void 0),!v.name&&!v.attributes.length)throw new ft(`Error while parsing selector \`${i}\` - selector cannot be empty`);return v}function dl(i,n="'"){const r=JSON.stringify(i),l=r.substring(1,r.length-1).replace(/\\"/g,'"');if(n==="'")return n+l.replace(/[']/g,"\\'")+n;if(n==='"')return n+l.replace(/["]/g,'\\"')+n;if(n==="`")return n+l.replace(/[`]/g,"`")+n;throw new Error("Invalid escape char")}function ll(i){return i.charAt(0).toUpperCase()+i.substring(1)}function sh(i){return i.replace(/([a-z0-9])([A-Z])/g,"$1_$2").replace(/([A-Z])([A-Z][a-z])/g,"$1_$2").toLowerCase()}function hl(i){return i.replace(/(^|[^\\])(\\\\)*\\(['"`])/g,"$1$2$3")}function ih(i,n,r=!1){return Yg(i,n,r,1)[0]}function Yg(i,n,r=!1,l=20,o){try{return wr(new ry[i](o),rh(n),r,l)}catch{return[n]}}function wr(i,n,r=!1,l=20){const o=[...n.parts],u=[];let f=r?"frame-locator":"page";for(let h=0;h<o.length;h++){const p=o[h],g=f;if(f="locator",p.name==="internal:describe")continue;if(p.name==="nth"){p.body==="0"?u.push([i.generateLocator(g,"first",""),i.generateLocator(g,"nth","0")]):p.body==="-1"?u.push([i.generateLocator(g,"last",""),i.generateLocator(g,"nth","-1")]):u.push([i.generateLocator(g,"nth",p.body)]);continue}if(p.name==="visible"){u.push([i.generateLocator(g,"visible",p.body),i.generateLocator(g,"default",`visible=${p.body}`)]);continue}if(p.name==="internal:text"){const{exact:O,text:v}=xs(p.body);u.push([i.generateLocator(g,"text",v,{exact:O})]);continue}if(p.name==="internal:has-text"){const{exact:O,text:v}=xs(p.body);if(!O){u.push([i.generateLocator(g,"has-text",v,{exact:O})]);continue}}if(p.name==="internal:has-not-text"){const{exact:O,text:v}=xs(p.body);if(!O){u.push([i.generateLocator(g,"has-not-text",v,{exact:O})]);continue}}if(p.name==="internal:has"){const O=wr(i,p.body.parsed,!1,l);u.push(O.map(v=>i.generateLocator(g,"has",v)));continue}if(p.name==="internal:has-not"){const O=wr(i,p.body.parsed,!1,l);u.push(O.map(v=>i.generateLocator(g,"hasNot",v)));continue}if(p.name==="internal:and"){const O=wr(i,p.body.parsed,!1,l);u.push(O.map(v=>i.generateLocator(g,"and",v)));continue}if(p.name==="internal:or"){const O=wr(i,p.body.parsed,!1,l);u.push(O.map(v=>i.generateLocator(g,"or",v)));continue}if(p.name==="internal:chain"){const O=wr(i,p.body.parsed,!1,l);u.push(O.map(v=>i.generateLocator(g,"chain",v)));continue}if(p.name==="internal:label"){const{exact:O,text:v}=xs(p.body);u.push([i.generateLocator(g,"label",v,{exact:O})]);continue}if(p.name==="internal:role"){const O=La(p.body),v={attrs:[]};for(const w of O.attributes)w.name==="name"?(v.exact=w.caseSensitive,v.name=w.value):(w.name==="level"&&typeof w.value=="string"&&(w.value=+w.value),v.attrs.push({name:w.name==="include-hidden"?"includeHidden":w.name,value:w.value}));u.push([i.generateLocator(g,"role",O.name,v)]);continue}if(p.name==="internal:testid"){const O=La(p.body),{value:v}=O.attributes[0];u.push([i.generateLocator(g,"test-id",v)]);continue}if(p.name==="internal:attr"){const O=La(p.body),{name:v,value:w,caseSensitive:I}=O.attributes[0],A=w,F=!!I;if(v==="placeholder"){u.push([i.generateLocator(g,"placeholder",A,{exact:F})]);continue}if(v==="alt"){u.push([i.generateLocator(g,"alt",A,{exact:F})]);continue}if(v==="title"){u.push([i.generateLocator(g,"title",A,{exact:F})]);continue}}if(p.name==="internal:control"&&p.body==="enter-frame"){const O=u[u.length-1],v=o[h-1],w=O.map(I=>i.chainLocators([I,i.generateLocator(g,"frame","")]));["xpath","css"].includes(v.name)&&w.push(i.generateLocator(g,"frame-locator",Sr({parts:[v]})),i.generateLocator(g,"frame-locator",Sr({parts:[v]},!0))),O.splice(0,O.length,...w),f="frame-locator";continue}const x=o[h+1],S=Sr({parts:[p]}),T=i.generateLocator(g,"default",S);if(x&&["internal:has-text","internal:has-not-text"].includes(x.name)){const{exact:O,text:v}=xs(x.body);if(!O){const w=i.generateLocator("locator",x.name==="internal:has-text"?"has-text":"has-not-text",v,{exact:O}),I={};x.name==="internal:has-text"?I.hasText=v:I.hasNotText=v;const A=i.generateLocator(g,"default",S,I);u.push([i.chainLocators([T,w]),A]),h++;continue}}let E;if(["xpath","css"].includes(p.name)){const O=Sr({parts:[p]},!0);E=i.generateLocator(g,"default",O)}u.push([T,E].filter(Boolean))}return Gg(i,u,l)}function Gg(i,n,r){const l=n.map(()=>""),o=[],u=f=>{if(f===n.length)return o.push(i.chainLocators(l)),o.length<r;for(const h of n[f])if(l[f]=h,!u(f+1))return!1;return!0};return u(0),o}function xs(i){let n=!1;const r=i.match(/^\/(.*)\/([igm]*)$/);return r?{text:new RegExp(r[1],r[2])}:(i.endsWith('"')?(i=JSON.parse(i),n=!0):i.endsWith('"s')?(i=JSON.parse(i.substring(0,i.length-1)),n=!0):i.endsWith('"i')&&(i=JSON.parse(i.substring(0,i.length-1)),n=!1),{exact:n,text:i})}class Xg{constructor(n){this.preferredQuote=n}generateLocator(n,r,l,o={}){switch(r){case"default":return o.hasText!==void 0?`locator(${this.quote(l)}, { hasText: ${this.toHasText(o.hasText)} })`:o.hasNotText!==void 0?`locator(${this.quote(l)}, { hasNotText: ${this.toHasText(o.hasNotText)} })`:`locator(${this.quote(l)})`;case"frame-locator":return`frameLocator(${this.quote(l)})`;case"frame":return"contentFrame()";case"nth":return`nth(${l})`;case"first":return"first()";case"last":return"last()";case"visible":return`filter({ visible: ${l==="true"?"true":"false"} })`;case"role":const u=[];qe(o.name)?u.push(`name: ${this.regexToSourceString(o.name)}`):typeof o.name=="string"&&(u.push(`name: ${this.quote(o.name)}`),o.exact&&u.push("exact: true"));for(const{name:h,value:p}of o.attrs)u.push(`${h}: ${typeof p=="string"?this.quote(p):p}`);const f=u.length?`, { ${u.join(", ")} }`:"";return`getByRole(${this.quote(l)}${f})`;case"has-text":return`filter({ hasText: ${this.toHasText(l)} })`;case"has-not-text":return`filter({ hasNotText: ${this.toHasText(l)} })`;case"has":return`filter({ has: ${l} })`;case"hasNot":return`filter({ hasNot: ${l} })`;case"and":return`and(${l})`;case"or":return`or(${l})`;case"chain":return`locator(${l})`;case"test-id":return`getByTestId(${this.toTestIdValue(l)})`;case"text":return this.toCallWithExact("getByText",l,!!o.exact);case"alt":return this.toCallWithExact("getByAltText",l,!!o.exact);case"placeholder":return this.toCallWithExact("getByPlaceholder",l,!!o.exact);case"label":return this.toCallWithExact("getByLabel",l,!!o.exact);case"title":return this.toCallWithExact("getByTitle",l,!!o.exact);default:throw new Error("Unknown selector kind "+r)}}chainLocators(n){return n.join(".")}regexToSourceString(n){return hl(String(n))}toCallWithExact(n,r,l){return qe(r)?`${n}(${this.regexToSourceString(r)})`:l?`${n}(${this.quote(r)}, { exact: true })`:`${n}(${this.quote(r)})`}toHasText(n){return qe(n)?this.regexToSourceString(n):this.quote(n)}toTestIdValue(n){return qe(n)?this.regexToSourceString(n):this.quote(n)}quote(n){return dl(n,this.preferredQuote??"'")}}class Zg{generateLocator(n,r,l,o={}){switch(r){case"default":return o.hasText!==void 0?`locator(${this.quote(l)}, has_text=${this.toHasText(o.hasText)})`:o.hasNotText!==void 0?`locator(${this.quote(l)}, has_not_text=${this.toHasText(o.hasNotText)})`:`locator(${this.quote(l)})`;case"frame-locator":return`frame_locator(${this.quote(l)})`;case"frame":return"content_frame";case"nth":return`nth(${l})`;case"first":return"first";case"last":return"last";case"visible":return`filter(visible=${l==="true"?"True":"False"})`;case"role":const u=[];qe(o.name)?u.push(`name=${this.regexToString(o.name)}`):typeof o.name=="string"&&(u.push(`name=${this.quote(o.name)}`),o.exact&&u.push("exact=True"));for(const{name:h,value:p}of o.attrs){let g=typeof p=="string"?this.quote(p):p;typeof p=="boolean"&&(g=p?"True":"False"),u.push(`${sh(h)}=${g}`)}const f=u.length?`, ${u.join(", ")}`:"";return`get_by_role(${this.quote(l)}${f})`;case"has-text":return`filter(has_text=${this.toHasText(l)})`;case"has-not-text":return`filter(has_not_text=${this.toHasText(l)})`;case"has":return`filter(has=${l})`;case"hasNot":return`filter(has_not=${l})`;case"and":return`and_(${l})`;case"or":return`or_(${l})`;case"chain":return`locator(${l})`;case"test-id":return`get_by_test_id(${this.toTestIdValue(l)})`;case"text":return this.toCallWithExact("get_by_text",l,!!o.exact);case"alt":return this.toCallWithExact("get_by_alt_text",l,!!o.exact);case"placeholder":return this.toCallWithExact("get_by_placeholder",l,!!o.exact);case"label":return this.toCallWithExact("get_by_label",l,!!o.exact);case"title":return this.toCallWithExact("get_by_title",l,!!o.exact);default:throw new Error("Unknown selector kind "+r)}}chainLocators(n){return n.join(".")}regexToString(n){const r=n.flags.includes("i")?", re.IGNORECASE":"";return`re.compile(r"${hl(n.source).replace(/\\\//,"/").replace(/"/g,'\\"')}"${r})`}toCallWithExact(n,r,l){return qe(r)?`${n}(${this.regexToString(r)})`:l?`${n}(${this.quote(r)}, exact=True)`:`${n}(${this.quote(r)})`}toHasText(n){return qe(n)?this.regexToString(n):`${this.quote(n)}`}toTestIdValue(n){return qe(n)?this.regexToString(n):this.quote(n)}quote(n){return dl(n,'"')}}class ey{generateLocator(n,r,l,o={}){let u;switch(n){case"page":u="Page";break;case"frame-locator":u="FrameLocator";break;case"locator":u="Locator";break}switch(r){case"default":return o.hasText!==void 0?`locator(${this.quote(l)}, new ${u}.LocatorOptions().setHasText(${this.toHasText(o.hasText)}))`:o.hasNotText!==void 0?`locator(${this.quote(l)}, new ${u}.LocatorOptions().setHasNotText(${this.toHasText(o.hasNotText)}))`:`locator(${this.quote(l)})`;case"frame-locator":return`frameLocator(${this.quote(l)})`;case"frame":return"contentFrame()";case"nth":return`nth(${l})`;case"first":return"first()";case"last":return"last()";case"visible":return`filter(new ${u}.FilterOptions().setVisible(${l==="true"?"true":"false"}))`;case"role":const f=[];qe(o.name)?f.push(`.setName(${this.regexToString(o.name)})`):typeof o.name=="string"&&(f.push(`.setName(${this.quote(o.name)})`),o.exact&&f.push(".setExact(true)"));for(const{name:p,value:g}of o.attrs)f.push(`.set${ll(p)}(${typeof g=="string"?this.quote(g):g})`);const h=f.length?`, new ${u}.GetByRoleOptions()${f.join("")}`:"";return`getByRole(AriaRole.${sh(l).toUpperCase()}${h})`;case"has-text":return`filter(new ${u}.FilterOptions().setHasText(${this.toHasText(l)}))`;case"has-not-text":return`filter(new ${u}.FilterOptions().setHasNotText(${this.toHasText(l)}))`;case"has":return`filter(new ${u}.FilterOptions().setHas(${l}))`;case"hasNot":return`filter(new ${u}.FilterOptions().setHasNot(${l}))`;case"and":return`and(${l})`;case"or":return`or(${l})`;case"chain":return`locator(${l})`;case"test-id":return`getByTestId(${this.toTestIdValue(l)})`;case"text":return this.toCallWithExact(u,"getByText",l,!!o.exact);case"alt":return this.toCallWithExact(u,"getByAltText",l,!!o.exact);case"placeholder":return this.toCallWithExact(u,"getByPlaceholder",l,!!o.exact);case"label":return this.toCallWithExact(u,"getByLabel",l,!!o.exact);case"title":return this.toCallWithExact(u,"getByTitle",l,!!o.exact);default:throw new Error("Unknown selector kind "+r)}}chainLocators(n){return n.join(".")}regexToString(n){const r=n.flags.includes("i")?", Pattern.CASE_INSENSITIVE":"";return`Pattern.compile(${this.quote(hl(n.source))}${r})`}toCallWithExact(n,r,l,o){return qe(l)?`${r}(${this.regexToString(l)})`:o?`${r}(${this.quote(l)}, new ${n}.${ll(r)}Options().setExact(true))`:`${r}(${this.quote(l)})`}toHasText(n){return qe(n)?this.regexToString(n):this.quote(n)}toTestIdValue(n){return qe(n)?this.regexToString(n):this.quote(n)}quote(n){return dl(n,'"')}}class ty{generateLocator(n,r,l,o={}){switch(r){case"default":return o.hasText!==void 0?`Locator(${this.quote(l)}, new() { ${this.toHasText(o.hasText)} })`:o.hasNotText!==void 0?`Locator(${this.quote(l)}, new() { ${this.toHasNotText(o.hasNotText)} })`:`Locator(${this.quote(l)})`;case"frame-locator":return`FrameLocator(${this.quote(l)})`;case"frame":return"ContentFrame";case"nth":return`Nth(${l})`;case"first":return"First";case"last":return"Last";case"visible":return`Filter(new() { Visible = ${l==="true"?"true":"false"} })`;case"role":const u=[];qe(o.name)?u.push(`NameRegex = ${this.regexToString(o.name)}`):typeof o.name=="string"&&(u.push(`Name = ${this.quote(o.name)}`),o.exact&&u.push("Exact = true"));for(const{name:h,value:p}of o.attrs)u.push(`${ll(h)} = ${typeof p=="string"?this.quote(p):p}`);const f=u.length?`, new() { ${u.join(", ")} }`:"";return`GetByRole(AriaRole.${ll(l)}${f})`;case"has-text":return`Filter(new() { ${this.toHasText(l)} })`;case"has-not-text":return`Filter(new() { ${this.toHasNotText(l)} })`;case"has":return`Filter(new() { Has = ${l} })`;case"hasNot":return`Filter(new() { HasNot = ${l} })`;case"and":return`And(${l})`;case"or":return`Or(${l})`;case"chain":return`Locator(${l})`;case"test-id":return`GetByTestId(${this.toTestIdValue(l)})`;case"text":return this.toCallWithExact("GetByText",l,!!o.exact);case"alt":return this.toCallWithExact("GetByAltText",l,!!o.exact);case"placeholder":return this.toCallWithExact("GetByPlaceholder",l,!!o.exact);case"label":return this.toCallWithExact("GetByLabel",l,!!o.exact);case"title":return this.toCallWithExact("GetByTitle",l,!!o.exact);default:throw new Error("Unknown selector kind "+r)}}chainLocators(n){return n.join(".")}regexToString(n){const r=n.flags.includes("i")?", RegexOptions.IgnoreCase":"";return`new Regex(${this.quote(hl(n.source))}${r})`}toCallWithExact(n,r,l){return qe(r)?`${n}(${this.regexToString(r)})`:l?`${n}(${this.quote(r)}, new() { Exact = true })`:`${n}(${this.quote(r)})`}toHasText(n){return qe(n)?`HasTextRegex = ${this.regexToString(n)}`:`HasText = ${this.quote(n)}`}toTestIdValue(n){return qe(n)?this.regexToString(n):this.quote(n)}toHasNotText(n){return qe(n)?`HasNotTextRegex = ${this.regexToString(n)}`:`HasNotText = ${this.quote(n)}`}quote(n){return dl(n,'"')}}class ny{generateLocator(n,r,l,o={}){return JSON.stringify({kind:r,body:l,options:o})}chainLocators(n){const r=n.map(l=>JSON.parse(l));for(let l=0;l<r.length-1;++l)r[l].next=r[l+1];return JSON.stringify(r[0])}}const ry={javascript:Xg,python:Zg,java:ey,csharp:ty,jsonl:ny};function qe(i){return i instanceof RegExp}const sy=({language:i,log:n})=>{const r=ye.useRef(null),[l,o]=ye.useState(new Map);return ye.useLayoutEffect(()=>{var u;n.find(f=>f.reveal)&&((u=r.current)==null||u.scrollIntoView({block:"center",inline:"nearest"}))},[r,n]),H.jsxs("div",{className:"call-log",style:{flex:"auto"},children:[n.map(u=>{const f=l.get(u.id),h=typeof f=="boolean"?f:u.status!=="done",p=u.params.selector?ih(i,u.params.selector):null;let g=u.title,x="";return u.title.startsWith("expect.to")||u.title.startsWith("expect.not.to")?(g="expect(",x=`).${u.title.substring(7)}()`):u.title.startsWith("locator.")?(g="",x=`.${u.title.substring(8)}()`):(p||u.params.url)&&(g=u.title+"(",x=")"),H.jsxs("div",{className:Un("call-log-call",u.status),children:[H.jsxs("div",{className:"call-log-call-header",children:[H.jsx("span",{className:Un("codicon",`codicon-chevron-${h?"down":"right"}`),style:{cursor:"pointer"},onClick:()=>{const S=new Map(l);S.set(u.id,!h),o(S)}}),g,u.params.url?H.jsx("span",{className:"call-log-details",children:H.jsx("span",{className:"call-log-url",title:u.params.url,children:u.params.url})}):void 0,p?H.jsx("span",{className:"call-log-details",children:H.jsx("span",{className:"call-log-selector",title:`page.${p}`,children:`page.${p}`})}):void 0,x,H.jsx("span",{className:Un("codicon",iy(u))}),typeof u.duration=="number"?H.jsxs("span",{className:"call-log-time",children:["— ",eg(u.duration)]}):void 0]}),(h?u.messages:[]).map((S,T)=>H.jsx("div",{className:"call-log-message",children:S.trim()},T)),!!u.error&&H.jsx("div",{className:"call-log-message error",hidden:!h,children:u.error})]},u.id)}),H.jsx("div",{ref:r})]})};function iy(i){switch(i.status){case"done":return"codicon-check";case"in-progress":return"codicon-clock";case"paused":return"codicon-debug-pause";case"error":return"codicon-error"}}const Qa=Symbol.for("yaml.alias"),Fa=Symbol.for("yaml.document"),kn=Symbol.for("yaml.map"),lh=Symbol.for("yaml.pair"),Vt=Symbol.for("yaml.scalar"),Ar=Symbol.for("yaml.seq"),_t=Symbol.for("yaml.node.type"),Kn=i=>!!i&&typeof i=="object"&&i[_t]===Qa,Wn=i=>!!i&&typeof i=="object"&&i[_t]===Fa,Pr=i=>!!i&&typeof i=="object"&&i[_t]===kn,_e=i=>!!i&&typeof i=="object"&&i[_t]===lh,ve=i=>!!i&&typeof i=="object"&&i[_t]===Vt,Mr=i=>!!i&&typeof i=="object"&&i[_t]===Ar;function $e(i){if(i&&typeof i=="object")switch(i[_t]){case kn:case Ar:return!0}return!1}function Ie(i){if(i&&typeof i=="object")switch(i[_t]){case Qa:case kn:case Vt:case Ar:return!0}return!1}const ly=i=>(ve(i)||$e(i))&&!!i.anchor,dt=Symbol("break visit"),oh=Symbol("skip children"),Ut=Symbol("remove node");function En(i,n){const r=ah(n);Wn(i)?kr(null,i.contents,r,Object.freeze([i]))===Ut&&(i.contents=null):kr(null,i,r,Object.freeze([]))}En.BREAK=dt;En.SKIP=oh;En.REMOVE=Ut;function kr(i,n,r,l){const o=uh(i,n,r,l);if(Ie(o)||_e(o))return ch(i,l,o),kr(i,o,r,l);if(typeof o!="symbol"){if($e(n)){l=Object.freeze(l.concat(n));for(let u=0;u<n.items.length;++u){const f=kr(u,n.items[u],r,l);if(typeof f=="number")u=f-1;else{if(f===dt)return dt;f===Ut&&(n.items.splice(u,1),u-=1)}}}else if(_e(n)){l=Object.freeze(l.concat(n));const u=kr("key",n.key,r,l);if(u===dt)return dt;u===Ut&&(n.key=null);const f=kr("value",n.value,r,l);if(f===dt)return dt;f===Ut&&(n.value=null)}}return o}async function pl(i,n){const r=ah(n);Wn(i)?await Er(null,i.contents,r,Object.freeze([i]))===Ut&&(i.contents=null):await Er(null,i,r,Object.freeze([]))}pl.BREAK=dt;pl.SKIP=oh;pl.REMOVE=Ut;async function Er(i,n,r,l){const o=await uh(i,n,r,l);if(Ie(o)||_e(o))return ch(i,l,o),Er(i,o,r,l);if(typeof o!="symbol"){if($e(n)){l=Object.freeze(l.concat(n));for(let u=0;u<n.items.length;++u){const f=await Er(u,n.items[u],r,l);if(typeof f=="number")u=f-1;else{if(f===dt)return dt;f===Ut&&(n.items.splice(u,1),u-=1)}}}else if(_e(n)){l=Object.freeze(l.concat(n));const u=await Er("key",n.key,r,l);if(u===dt)return dt;u===Ut&&(n.key=null);const f=await Er("value",n.value,r,l);if(f===dt)return dt;f===Ut&&(n.value=null)}}return o}function ah(i){return typeof i=="object"&&(i.Collection||i.Node||i.Value)?Object.assign({Alias:i.Node,Map:i.Node,Scalar:i.Node,Seq:i.Node},i.Value&&{Map:i.Value,Scalar:i.Value,Seq:i.Value},i.Collection&&{Map:i.Collection,Seq:i.Collection},i):i}function uh(i,n,r,l){var o,u,f,h,p;if(typeof r=="function")return r(i,n,l);if(Pr(n))return(o=r.Map)==null?void 0:o.call(r,i,n,l);if(Mr(n))return(u=r.Seq)==null?void 0:u.call(r,i,n,l);if(_e(n))return(f=r.Pair)==null?void 0:f.call(r,i,n,l);if(ve(n))return(h=r.Scalar)==null?void 0:h.call(r,i,n,l);if(Kn(n))return(p=r.Alias)==null?void 0:p.call(r,i,n,l)}function ch(i,n,r){const l=n[n.length-1];if($e(l))l.items[i]=r;else if(_e(l))i==="key"?l.key=r:l.value=r;else if(Wn(l))l.contents=r;else{const o=Kn(l)?"alias":"scalar";throw new Error(`Cannot replace node with ${o} parent`)}}const oy={"!":"%21",",":"%2C","[":"%5B","]":"%5D","{":"%7B","}":"%7D"},ay=i=>i.replace(/[!,[\]{}]/g,n=>oy[n]);class nt{constructor(n,r){this.docStart=null,this.docEnd=!1,this.yaml=Object.assign({},nt.defaultYaml,n),this.tags=Object.assign({},nt.defaultTags,r)}clone(){const n=new nt(this.yaml,this.tags);return n.docStart=this.docStart,n}atDocument(){const n=new nt(this.yaml,this.tags);switch(this.yaml.version){case"1.1":this.atNextDocument=!0;break;case"1.2":this.atNextDocument=!1,this.yaml={explicit:nt.defaultYaml.explicit,version:"1.2"},this.tags=Object.assign({},nt.defaultTags);break}return n}add(n,r){this.atNextDocument&&(this.yaml={explicit:nt.defaultYaml.explicit,version:"1.1"},this.tags=Object.assign({},nt.defaultTags),this.atNextDocument=!1);const l=n.trim().split(/[ \t]+/),o=l.shift();switch(o){case"%TAG":{if(l.length!==2&&(r(0,"%TAG directive should contain exactly two parts"),l.length<2))return!1;const[u,f]=l;return this.tags[u]=f,!0}case"%YAML":{if(this.yaml.explicit=!0,l.length!==1)return r(0,"%YAML directive should contain exactly one part"),!1;const[u]=l;if(u==="1.1"||u==="1.2")return this.yaml.version=u,!0;{const f=/^\d+\.\d+$/.test(u);return r(6,`Unsupported YAML version ${u}`,f),!1}}default:return r(0,`Unknown directive ${o}`,!0),!1}}tagName(n,r){if(n==="!")return"!";if(n[0]!=="!")return r(`Not a valid tag: ${n}`),null;if(n[1]==="<"){const f=n.slice(2,-1);return f==="!"||f==="!!"?(r(`Verbatim tags aren't resolved, so ${n} is invalid.`),null):(n[n.length-1]!==">"&&r("Verbatim tags must end with a >"),f)}const[,l,o]=n.match(/^(.*!)([^!]*)$/s);o||r(`The ${n} tag has no suffix`);const u=this.tags[l];if(u)try{return u+decodeURIComponent(o)}catch(f){return r(String(f)),null}return l==="!"?n:(r(`Could not resolve tag: ${n}`),null)}tagString(n){for(const[r,l]of Object.entries(this.tags))if(n.startsWith(l))return r+ay(n.substring(l.length));return n[0]==="!"?n:`!<${n}>`}toString(n){const r=this.yaml.explicit?[`%YAML ${this.yaml.version||"1.2"}`]:[],l=Object.entries(this.tags);let o;if(n&&l.length>0&&Ie(n.contents)){const u={};En(n.contents,(f,h)=>{Ie(h)&&h.tag&&(u[h.tag]=!0)}),o=Object.keys(u)}else o=[];for(const[u,f]of l)u==="!!"&&f==="tag:yaml.org,2002:"||(!n||o.some(h=>h.startsWith(f)))&&r.push(`%TAG ${u} ${f}`);return r.join(`
`)}}nt.defaultYaml={explicit:!1,version:"1.2"};nt.defaultTags={"!!":"tag:yaml.org,2002:"};function fh(i){if(/[\x00-\x19\s,[\]{}]/.test(i)){const r=`Anchor must not contain whitespace or control characters: ${JSON.stringify(i)}`;throw new Error(r)}return!0}function dh(i){const n=new Set;return En(i,{Value(r,l){l.anchor&&n.add(l.anchor)}}),n}function hh(i,n){for(let r=1;;++r){const l=`${i}${r}`;if(!n.has(l))return l}}function uy(i,n){const r=[],l=new Map;let o=null;return{onAnchor:u=>{r.push(u),o||(o=dh(i));const f=hh(n,o);return o.add(f),f},setAnchors:()=>{for(const u of r){const f=l.get(u);if(typeof f=="object"&&f.anchor&&(ve(f.node)||$e(f.node)))f.node.anchor=f.anchor;else{const h=new Error("Failed to resolve repeated object (this should not happen)");throw h.source=u,h}}},sourceObjects:l}}function Nr(i,n,r,l){if(l&&typeof l=="object")if(Array.isArray(l))for(let o=0,u=l.length;o<u;++o){const f=l[o],h=Nr(i,l,String(o),f);h===void 0?delete l[o]:h!==f&&(l[o]=h)}else if(l instanceof Map)for(const o of Array.from(l.keys())){const u=l.get(o),f=Nr(i,l,o,u);f===void 0?l.delete(o):f!==u&&l.set(o,f)}else if(l instanceof Set)for(const o of Array.from(l)){const u=Nr(i,l,o,o);u===void 0?l.delete(o):u!==o&&(l.delete(o),l.add(u))}else for(const[o,u]of Object.entries(l)){const f=Nr(i,l,o,u);f===void 0?delete l[o]:f!==u&&(l[o]=f)}return i.call(n,r,l)}function Ct(i,n,r){if(Array.isArray(i))return i.map((l,o)=>Ct(l,String(o),r));if(i&&typeof i.toJSON=="function"){if(!r||!ly(i))return i.toJSON(n,r);const l={aliasCount:0,count:1,res:void 0};r.anchors.set(i,l),r.onCreate=u=>{l.res=u,delete r.onCreate};const o=i.toJSON(n,r);return r.onCreate&&r.onCreate(o),o}return typeof i=="bigint"&&!(r!=null&&r.keep)?Number(i):i}class Ja{constructor(n){Object.defineProperty(this,_t,{value:n})}clone(){const n=Object.create(Object.getPrototypeOf(this),Object.getOwnPropertyDescriptors(this));return this.range&&(n.range=this.range.slice()),n}toJS(n,{mapAsMap:r,maxAliasCount:l,onAnchor:o,reviver:u}={}){if(!Wn(n))throw new TypeError("A document argument is required");const f={anchors:new Map,doc:n,keep:!0,mapAsMap:r===!0,mapKeyWarned:!1,maxAliasCount:typeof l=="number"?l:100},h=Ct(this,"",f);if(typeof o=="function")for(const{count:p,res:g}of f.anchors.values())o(g,p);return typeof u=="function"?Nr(u,{"":h},"",h):h}}class ml extends Ja{constructor(n){super(Qa),this.source=n,Object.defineProperty(this,"tag",{set(){throw new Error("Alias nodes cannot have tags")}})}resolve(n){let r;return En(n,{Node:(l,o)=>{if(o===this)return En.BREAK;o.anchor===this.source&&(r=o)}}),r}toJSON(n,r){if(!r)return{source:this.source};const{anchors:l,doc:o,maxAliasCount:u}=r,f=this.resolve(o);if(!f){const p=`Unresolved alias (the anchor must be set before the alias): ${this.source}`;throw new ReferenceError(p)}let h=l.get(f);if(h||(Ct(f,null,r),h=l.get(f)),!h||h.res===void 0){const p="This should not happen: Alias anchor was not resolved?";throw new ReferenceError(p)}if(u>=0&&(h.count+=1,h.aliasCount===0&&(h.aliasCount=nl(o,f,l)),h.count*h.aliasCount>u)){const p="Excessive alias count indicates a resource exhaustion attack";throw new ReferenceError(p)}return h.res}toString(n,r,l){const o=`*${this.source}`;if(n){if(fh(this.source),n.options.verifyAliasOrder&&!n.anchors.has(this.source)){const u=`Unresolved alias (the anchor must be set before the alias): ${this.source}`;throw new Error(u)}if(n.implicitKey)return`${o} `}return o}}function nl(i,n,r){if(Kn(n)){const l=n.resolve(i),o=r&&l&&r.get(l);return o?o.count*o.aliasCount:0}else if($e(n)){let l=0;for(const o of n.items){const u=nl(i,o,r);u>l&&(l=u)}return l}else if(_e(n)){const l=nl(i,n.key,r),o=nl(i,n.value,r);return Math.max(l,o)}return 1}const ph=i=>!i||typeof i!="function"&&typeof i!="object";class ie extends Ja{constructor(n){super(Vt),this.value=n}toJSON(n,r){return r!=null&&r.keep?this.value:Ct(this.value,n,r)}toString(){return String(this.value)}}ie.BLOCK_FOLDED="BLOCK_FOLDED";ie.BLOCK_LITERAL="BLOCK_LITERAL";ie.PLAIN="PLAIN";ie.QUOTE_DOUBLE="QUOTE_DOUBLE";ie.QUOTE_SINGLE="QUOTE_SINGLE";const cy="tag:yaml.org,2002:";function fy(i,n,r){if(n){const l=r.filter(u=>u.tag===n),o=l.find(u=>!u.format)??l[0];if(!o)throw new Error(`Tag ${n} not found`);return o}return r.find(l=>{var o;return((o=l.identify)==null?void 0:o.call(l,i))&&!l.format})}function Ls(i,n,r){var S,T,E;if(Wn(i)&&(i=i.contents),Ie(i))return i;if(_e(i)){const O=(T=(S=r.schema[kn]).createNode)==null?void 0:T.call(S,r.schema,null,r);return O.items.push(i),O}(i instanceof String||i instanceof Number||i instanceof Boolean||typeof BigInt<"u"&&i instanceof BigInt)&&(i=i.valueOf());const{aliasDuplicateObjects:l,onAnchor:o,onTagObj:u,schema:f,sourceObjects:h}=r;let p;if(l&&i&&typeof i=="object"){if(p=h.get(i),p)return p.anchor||(p.anchor=o(i)),new ml(p.anchor);p={anchor:null,node:null},h.set(i,p)}n!=null&&n.startsWith("!!")&&(n=cy+n.slice(2));let g=fy(i,n,f.tags);if(!g){if(i&&typeof i.toJSON=="function"&&(i=i.toJSON()),!i||typeof i!="object"){const O=new ie(i);return p&&(p.node=O),O}g=i instanceof Map?f[kn]:Symbol.iterator in Object(i)?f[Ar]:f[kn]}u&&(u(g),delete r.onTagObj);const x=g!=null&&g.createNode?g.createNode(r.schema,i,r):typeof((E=g==null?void 0:g.nodeClass)==null?void 0:E.from)=="function"?g.nodeClass.from(r.schema,i,r):new ie(i);return n?x.tag=n:g.default||(x.tag=g.tag),p&&(p.node=x),x}function ol(i,n,r){let l=r;for(let o=n.length-1;o>=0;--o){const u=n[o];if(typeof u=="number"&&Number.isInteger(u)&&u>=0){const f=[];f[u]=l,l=f}else l=new Map([[u,l]])}return Ls(l,void 0,{aliasDuplicateObjects:!1,keepUndefined:!1,onAnchor:()=>{throw new Error("This should not happen, please report a bug.")},schema:i,sourceObjects:new Map})}const Cs=i=>i==null||typeof i=="object"&&!!i[Symbol.iterator]().next().done;class mh extends Ja{constructor(n,r){super(n),Object.defineProperty(this,"schema",{value:r,configurable:!0,enumerable:!1,writable:!0})}clone(n){const r=Object.create(Object.getPrototypeOf(this),Object.getOwnPropertyDescriptors(this));return n&&(r.schema=n),r.items=r.items.map(l=>Ie(l)||_e(l)?l.clone(n):l),this.range&&(r.range=this.range.slice()),r}addIn(n,r){if(Cs(n))this.add(r);else{const[l,...o]=n,u=this.get(l,!0);if($e(u))u.addIn(o,r);else if(u===void 0&&this.schema)this.set(l,ol(this.schema,o,r));else throw new Error(`Expected YAML collection at ${l}. Remaining path: ${o}`)}}deleteIn(n){const[r,...l]=n;if(l.length===0)return this.delete(r);const o=this.get(r,!0);if($e(o))return o.deleteIn(l);throw new Error(`Expected YAML collection at ${r}. Remaining path: ${l}`)}getIn(n,r){const[l,...o]=n,u=this.get(l,!0);return o.length===0?!r&&ve(u)?u.value:u:$e(u)?u.getIn(o,r):void 0}hasAllNullValues(n){return this.items.every(r=>{if(!_e(r))return!1;const l=r.value;return l==null||n&&ve(l)&&l.value==null&&!l.commentBefore&&!l.comment&&!l.tag})}hasIn(n){const[r,...l]=n;if(l.length===0)return this.has(r);const o=this.get(r,!0);return $e(o)?o.hasIn(l):!1}setIn(n,r){const[l,...o]=n;if(o.length===0)this.set(l,r);else{const u=this.get(l,!0);if($e(u))u.setIn(o,r);else if(u===void 0&&this.schema)this.set(l,ol(this.schema,o,r));else throw new Error(`Expected YAML collection at ${l}. Remaining path: ${o}`)}}}const dy=i=>i.replace(/^(?!$)(?: $)?/gm,"#");function Zt(i,n){return/^\n+$/.test(i)?i.substring(1):n?i.replace(/^(?! *$)/gm,n):i}const Bn=(i,n,r)=>i.endsWith(`
`)?Zt(r,n):r.includes(`
`)?`
`+Zt(r,n):(i.endsWith(" ")?"":" ")+r,gh="flow",za="block",rl="quoted";function gl(i,n,r="flow",{indentAtStart:l,lineWidth:o=80,minContentWidth:u=20,onFold:f,onOverflow:h}={}){if(!o||o<0)return i;o<u&&(u=0);const p=Math.max(1+u,1+o-n.length);if(i.length<=p)return i;const g=[],x={};let S=o-n.length;typeof l=="number"&&(l>o-Math.max(2,u)?g.push(0):S=o-l);let T,E,O=!1,v=-1,w=-1,I=-1;r===za&&(v=xd(i,v,n.length),v!==-1&&(S=v+p));for(let F;F=i[v+=1];){if(r===rl&&F==="\\"){switch(w=v,i[v+1]){case"x":v+=3;break;case"u":v+=5;break;case"U":v+=9;break;default:v+=1}I=v}if(F===`
`)r===za&&(v=xd(i,v,n.length)),S=v+n.length+p,T=void 0;else{if(F===" "&&E&&E!==" "&&E!==`
`&&E!=="	"){const V=i[v+1];V&&V!==" "&&V!==`
`&&V!=="	"&&(T=v)}if(v>=S)if(T)g.push(T),S=T+p,T=void 0;else if(r===rl){for(;E===" "||E==="	";)E=F,F=i[v+=1],O=!0;const V=v>I+1?v-2:w-1;if(x[V])return i;g.push(V),x[V]=!0,S=V+p,T=void 0}else O=!0}E=F}if(O&&h&&h(),g.length===0)return i;f&&f();let A=i.slice(0,g[0]);for(let F=0;F<g.length;++F){const V=g[F],W=g[F+1]||i.length;V===0?A=`
${n}${i.slice(0,W)}`:(r===rl&&x[V]&&(A+=`${i[V]}\\`),A+=`
${n}${i.slice(V+1,W)}`)}return A}function xd(i,n,r){let l=n,o=n+1,u=i[o];for(;u===" "||u==="	";)if(n<o+r)u=i[++n];else{do u=i[++n];while(u&&u!==`
`);l=n,o=n+1,u=i[o]}return l}const yl=(i,n)=>({indentAtStart:n?i.indent.length:i.indentAtStart,lineWidth:i.options.lineWidth,minContentWidth:i.options.minContentWidth}),vl=i=>/^(%|---|\.\.\.)/m.test(i);function hy(i,n,r){if(!n||n<0)return!1;const l=n-r,o=i.length;if(o<=l)return!1;for(let u=0,f=0;u<o;++u)if(i[u]===`
`){if(u-f>l)return!0;if(f=u+1,o-f<=l)return!1}return!0}function _s(i,n){const r=JSON.stringify(i);if(n.options.doubleQuotedAsJSON)return r;const{implicitKey:l}=n,o=n.options.doubleQuotedMinMultiLineLength,u=n.indent||(vl(i)?"  ":"");let f="",h=0;for(let p=0,g=r[p];g;g=r[++p])if(g===" "&&r[p+1]==="\\"&&r[p+2]==="n"&&(f+=r.slice(h,p)+"\\ ",p+=1,h=p,g="\\"),g==="\\")switch(r[p+1]){case"u":{f+=r.slice(h,p);const x=r.substr(p+2,4);switch(x){case"0000":f+="\\0";break;case"0007":f+="\\a";break;case"000b":f+="\\v";break;case"001b":f+="\\e";break;case"0085":f+="\\N";break;case"00a0":f+="\\_";break;case"2028":f+="\\L";break;case"2029":f+="\\P";break;default:x.substr(0,2)==="00"?f+="\\x"+x.substr(2):f+=r.substr(p,6)}p+=5,h=p+1}break;case"n":if(l||r[p+2]==='"'||r.length<o)p+=1;else{for(f+=r.slice(h,p)+`

`;r[p+2]==="\\"&&r[p+3]==="n"&&r[p+4]!=='"';)f+=`
`,p+=2;f+=u,r[p+2]===" "&&(f+="\\"),p+=1,h=p+1}break;default:p+=1}return f=h?f+r.slice(h):r,l?f:gl(f,u,rl,yl(n,!1))}function Ua(i,n){if(n.options.singleQuote===!1||n.implicitKey&&i.includes(`
`)||/[ \t]\n|\n[ \t]/.test(i))return _s(i,n);const r=n.indent||(vl(i)?"  ":""),l="'"+i.replace(/'/g,"''").replace(/\n+/g,`$&
${r}`)+"'";return n.implicitKey?l:gl(l,r,gh,yl(n,!1))}function xr(i,n){const{singleQuote:r}=n.options;let l;if(r===!1)l=_s;else{const o=i.includes('"'),u=i.includes("'");o&&!u?l=Ua:u&&!o?l=_s:l=r?Ua:_s}return l(i,n)}let Va;try{Va=new RegExp(`(^|(?<!
))
+(?!
|$)`,"g")}catch{Va=/\n+(?!\n|$)/g}function sl({comment:i,type:n,value:r},l,o,u){const{blockQuote:f,commentString:h,lineWidth:p}=l.options;if(!f||/\n[\t ]+$/.test(r)||/^\s*$/.test(r))return xr(r,l);const g=l.indent||(l.forceBlockIndent||vl(r)?"  ":""),x=f==="literal"?!0:f==="folded"||n===ie.BLOCK_FOLDED?!1:n===ie.BLOCK_LITERAL?!0:!hy(r,p,g.length);if(!r)return x?`|
`:`>
`;let S,T;for(T=r.length;T>0;--T){const j=r[T-1];if(j!==`
`&&j!=="	"&&j!==" ")break}let E=r.substring(T);const O=E.indexOf(`
`);O===-1?S="-":r===E||O!==E.length-1?(S="+",u&&u()):S="",E&&(r=r.slice(0,-E.length),E[E.length-1]===`
`&&(E=E.slice(0,-1)),E=E.replace(Va,`$&${g}`));let v=!1,w,I=-1;for(w=0;w<r.length;++w){const j=r[w];if(j===" ")v=!0;else if(j===`
`)I=w;else break}let A=r.substring(0,I<w?I+1:w);A&&(r=r.substring(A.length),A=A.replace(/\n+/g,`$&${g}`));let V=(x?"|":">")+(v?g?"2":"1":"")+S;if(i&&(V+=" "+h(i.replace(/ ?[\r\n]+/g," ")),o&&o()),x)return r=r.replace(/\n+/g,`$&${g}`),`${V}
${g}${A}${r}${E}`;r=r.replace(/\n+/g,`
$&`).replace(/(?:^|\n)([\t ].*)(?:([\n\t ]*)\n(?![\n\t ]))?/g,"$1$2").replace(/\n+/g,`$&${g}`);const W=gl(`${A}${r}${E}`,g,za,yl(l,!0));return`${V}
${g}${W}`}function py(i,n,r,l){const{type:o,value:u}=i,{actualString:f,implicitKey:h,indent:p,indentStep:g,inFlow:x}=n;if(h&&u.includes(`
`)||x&&/[[\]{},]/.test(u))return xr(u,n);if(!u||/^[\n\t ,[\]{}#&*!|>'"%@`]|^[?-]$|^[?-][ \t]|[\n:][ \t]|[ \t]\n|[\n\t ]#|[\n\t :]$/.test(u))return h||x||!u.includes(`
`)?xr(u,n):sl(i,n,r,l);if(!h&&!x&&o!==ie.PLAIN&&u.includes(`
`))return sl(i,n,r,l);if(vl(u)){if(p==="")return n.forceBlockIndent=!0,sl(i,n,r,l);if(h&&p===g)return xr(u,n)}const S=u.replace(/\n+/g,`$&
${p}`);if(f){const T=v=>{var w;return v.default&&v.tag!=="tag:yaml.org,2002:str"&&((w=v.test)==null?void 0:w.test(S))},{compat:E,tags:O}=n.doc.schema;if(O.some(T)||E!=null&&E.some(T))return xr(u,n)}return h?S:gl(S,p,gh,yl(n,!1))}function As(i,n,r,l){const{implicitKey:o,inFlow:u}=n,f=typeof i.value=="string"?i:Object.assign({},i,{value:String(i.value)});let{type:h}=i;h!==ie.QUOTE_DOUBLE&&/[\x00-\x08\x0b-\x1f\x7f-\x9f\u{D800}-\u{DFFF}]/u.test(f.value)&&(h=ie.QUOTE_DOUBLE);const p=x=>{switch(x){case ie.BLOCK_FOLDED:case ie.BLOCK_LITERAL:return o||u?xr(f.value,n):sl(f,n,r,l);case ie.QUOTE_DOUBLE:return _s(f.value,n);case ie.QUOTE_SINGLE:return Ua(f.value,n);case ie.PLAIN:return py(f,n,r,l);default:return null}};let g=p(h);if(g===null){const{defaultKeyType:x,defaultStringType:S}=n.options,T=o&&x||S;if(g=p(T),g===null)throw new Error(`Unsupported default string type ${T}`)}return g}function yh(i,n){const r=Object.assign({blockQuote:!0,commentString:dy,defaultKeyType:null,defaultStringType:"PLAIN",directives:null,doubleQuotedAsJSON:!1,doubleQuotedMinMultiLineLength:40,falseStr:"false",flowCollectionPadding:!0,indentSeq:!0,lineWidth:80,minContentWidth:20,nullStr:"null",simpleKeys:!1,singleQuote:null,trueStr:"true",verifyAliasOrder:!0},i.schema.toStringOptions,n);let l;switch(r.collectionStyle){case"block":l=!1;break;case"flow":l=!0;break;default:l=null}return{anchors:new Set,doc:i,flowCollectionPadding:r.flowCollectionPadding?" ":"",indent:"",indentStep:typeof r.indent=="number"?" ".repeat(r.indent):"  ",inFlow:l,options:r}}function my(i,n){var o;if(n.tag){const u=i.filter(f=>f.tag===n.tag);if(u.length>0)return u.find(f=>f.format===n.format)??u[0]}let r,l;if(ve(n)){l=n.value;let u=i.filter(f=>{var h;return(h=f.identify)==null?void 0:h.call(f,l)});if(u.length>1){const f=u.filter(h=>h.test);f.length>0&&(u=f)}r=u.find(f=>f.format===n.format)??u.find(f=>!f.format)}else l=n,r=i.find(u=>u.nodeClass&&l instanceof u.nodeClass);if(!r){const u=((o=l==null?void 0:l.constructor)==null?void 0:o.name)??typeof l;throw new Error(`Tag not resolved for ${u} value`)}return r}function gy(i,n,{anchors:r,doc:l}){if(!l.directives)return"";const o=[],u=(ve(i)||$e(i))&&i.anchor;u&&fh(u)&&(r.add(u),o.push(`&${u}`));const f=i.tag?i.tag:n.default?null:n.tag;return f&&o.push(l.directives.tagString(f)),o.join(" ")}function Lr(i,n,r,l){var p;if(_e(i))return i.toString(n,r,l);if(Kn(i)){if(n.doc.directives)return i.toString(n);if((p=n.resolvedAliases)!=null&&p.has(i))throw new TypeError("Cannot stringify circular structure without alias nodes");n.resolvedAliases?n.resolvedAliases.add(i):n.resolvedAliases=new Set([i]),i=i.resolve(n.doc)}let o;const u=Ie(i)?i:n.doc.createNode(i,{onTagObj:g=>o=g});o||(o=my(n.doc.schema.tags,u));const f=gy(u,o,n);f.length>0&&(n.indentAtStart=(n.indentAtStart??0)+f.length+1);const h=typeof o.stringify=="function"?o.stringify(u,n,r,l):ve(u)?As(u,n,r,l):u.toString(n,r,l);return f?ve(u)||h[0]==="{"||h[0]==="["?`${f} ${h}`:`${f}
${n.indent}${h}`:h}function yy({key:i,value:n},r,l,o){const{allNullValues:u,doc:f,indent:h,indentStep:p,options:{commentString:g,indentSeq:x,simpleKeys:S}}=r;let T=Ie(i)&&i.comment||null;if(S){if(T)throw new Error("With simple keys, key nodes cannot have comments");if($e(i)||!Ie(i)&&typeof i=="object"){const Y="With simple keys, collection cannot be used as a key value";throw new Error(Y)}}let E=!S&&(!i||T&&n==null&&!r.inFlow||$e(i)||(ve(i)?i.type===ie.BLOCK_FOLDED||i.type===ie.BLOCK_LITERAL:typeof i=="object"));r=Object.assign({},r,{allNullValues:!1,implicitKey:!E&&(S||!u),indent:h+p});let O=!1,v=!1,w=Lr(i,r,()=>O=!0,()=>v=!0);if(!E&&!r.inFlow&&w.length>1024){if(S)throw new Error("With simple keys, single line scalar must not span more than 1024 characters");E=!0}if(r.inFlow){if(u||n==null)return O&&l&&l(),w===""?"?":E?`? ${w}`:w}else if(u&&!S||n==null&&E)return w=`? ${w}`,T&&!O?w+=Bn(w,r.indent,g(T)):v&&o&&o(),w;O&&(T=null),E?(T&&(w+=Bn(w,r.indent,g(T))),w=`? ${w}
${h}:`):(w=`${w}:`,T&&(w+=Bn(w,r.indent,g(T))));let I,A,F;Ie(n)?(I=!!n.spaceBefore,A=n.commentBefore,F=n.comment):(I=!1,A=null,F=null,n&&typeof n=="object"&&(n=f.createNode(n))),r.implicitKey=!1,!E&&!T&&ve(n)&&(r.indentAtStart=w.length+1),v=!1,!x&&p.length>=2&&!r.inFlow&&!E&&Mr(n)&&!n.flow&&!n.tag&&!n.anchor&&(r.indent=r.indent.substring(2));let V=!1;const W=Lr(n,r,()=>V=!0,()=>v=!0);let j=" ";if(T||I||A){if(j=I?`
`:"",A){const Y=g(A);j+=`
${Zt(Y,r.indent)}`}W===""&&!r.inFlow?j===`
`&&(j=`

`):j+=`
${r.indent}`}else if(!E&&$e(n)){const Y=W[0],z=W.indexOf(`
`),P=z!==-1,le=r.inFlow??n.flow??n.items.length===0;if(P||!le){let ue=!1;if(P&&(Y==="&"||Y==="!")){let M=W.indexOf(" ");Y==="&"&&M!==-1&&M<z&&W[M+1]==="!"&&(M=W.indexOf(" ",M+1)),(M===-1||z<M)&&(ue=!0)}ue||(j=`
${r.indent}`)}}else(W===""||W[0]===`
`)&&(j="");return w+=j+W,r.inFlow?V&&l&&l():F&&!V?w+=Bn(w,r.indent,g(F)):v&&o&&o(),w}function vh(i,n){(i==="debug"||i==="warn")&&(typeof process<"u"&&process.emitWarning?process.emitWarning(n):console.warn(n))}const qi="<<",en={identify:i=>i===qi||typeof i=="symbol"&&i.description===qi,default:"key",tag:"tag:yaml.org,2002:merge",test:/^<<$/,resolve:()=>Object.assign(new ie(Symbol(qi)),{addToJSMap:wh}),stringify:()=>qi},vy=(i,n)=>(en.identify(n)||ve(n)&&(!n.type||n.type===ie.PLAIN)&&en.identify(n.value))&&(i==null?void 0:i.doc.schema.tags.some(r=>r.tag===en.tag&&r.default));function wh(i,n,r){if(r=i&&Kn(r)?r.resolve(i.doc):r,Mr(r))for(const l of r.items)Oa(i,n,l);else if(Array.isArray(r))for(const l of r)Oa(i,n,l);else Oa(i,n,r)}function Oa(i,n,r){const l=i&&Kn(r)?r.resolve(i.doc):r;if(!Pr(l))throw new Error("Merge sources must be maps or map aliases");const o=l.toJSON(null,i,Map);for(const[u,f]of o)n instanceof Map?n.has(u)||n.set(u,f):n instanceof Set?n.add(u):Object.prototype.hasOwnProperty.call(n,u)||Object.defineProperty(n,u,{value:f,writable:!0,enumerable:!0,configurable:!0});return n}function Sh(i,n,{key:r,value:l}){if(Ie(r)&&r.addToJSMap)r.addToJSMap(i,n,l);else if(vy(i,r))wh(i,n,l);else{const o=Ct(r,"",i);if(n instanceof Map)n.set(o,Ct(l,o,i));else if(n instanceof Set)n.add(o);else{const u=wy(r,o,i),f=Ct(l,u,i);u in n?Object.defineProperty(n,u,{value:f,writable:!0,enumerable:!0,configurable:!0}):n[u]=f}}return n}function wy(i,n,r){if(n===null)return"";if(typeof n!="object")return String(n);if(Ie(i)&&(r!=null&&r.doc)){const l=yh(r.doc,{});l.anchors=new Set;for(const u of r.anchors.keys())l.anchors.add(u.anchor);l.inFlow=!0,l.inStringifyKey=!0;const o=i.toString(l);if(!r.mapKeyWarned){let u=JSON.stringify(o);u.length>40&&(u=u.substring(0,36)+'..."'),vh(r.doc.options.logLevel,`Keys with collection values will be stringified due to JS Object restrictions: ${u}. Set mapAsMap: true to use object keys.`),r.mapKeyWarned=!0}return o}return JSON.stringify(n)}function Ya(i,n,r){const l=Ls(i,void 0,r),o=Ls(n,void 0,r);return new Ze(l,o)}class Ze{constructor(n,r=null){Object.defineProperty(this,_t,{value:lh}),this.key=n,this.value=r}clone(n){let{key:r,value:l}=this;return Ie(r)&&(r=r.clone(n)),Ie(l)&&(l=l.clone(n)),new Ze(r,l)}toJSON(n,r){const l=r!=null&&r.mapAsMap?new Map:{};return Sh(r,l,this)}toString(n,r,l){return n!=null&&n.doc?yy(this,n,r,l):JSON.stringify(this)}}function kh(i,n,r){return(n.inFlow??i.flow?ky:Sy)(i,n,r)}function Sy({comment:i,items:n},r,{blockItemPrefix:l,flowChars:o,itemIndent:u,onChompKeep:f,onComment:h}){const{indent:p,options:{commentString:g}}=r,x=Object.assign({},r,{indent:u,type:null});let S=!1;const T=[];for(let O=0;O<n.length;++O){const v=n[O];let w=null;if(Ie(v))!S&&v.spaceBefore&&T.push(""),al(r,T,v.commentBefore,S),v.comment&&(w=v.comment);else if(_e(v)){const A=Ie(v.key)?v.key:null;A&&(!S&&A.spaceBefore&&T.push(""),al(r,T,A.commentBefore,S))}S=!1;let I=Lr(v,x,()=>w=null,()=>S=!0);w&&(I+=Bn(I,u,g(w))),S&&w&&(S=!1),T.push(l+I)}let E;if(T.length===0)E=o.start+o.end;else{E=T[0];for(let O=1;O<T.length;++O){const v=T[O];E+=v?`
${p}${v}`:`
`}}return i?(E+=`
`+Zt(g(i),p),h&&h()):S&&f&&f(),E}function ky({items:i},n,{flowChars:r,itemIndent:l}){const{indent:o,indentStep:u,flowCollectionPadding:f,options:{commentString:h}}=n;l+=u;const p=Object.assign({},n,{indent:l,inFlow:!0,type:null});let g=!1,x=0;const S=[];for(let O=0;O<i.length;++O){const v=i[O];let w=null;if(Ie(v))v.spaceBefore&&S.push(""),al(n,S,v.commentBefore,!1),v.comment&&(w=v.comment);else if(_e(v)){const A=Ie(v.key)?v.key:null;A&&(A.spaceBefore&&S.push(""),al(n,S,A.commentBefore,!1),A.comment&&(g=!0));const F=Ie(v.value)?v.value:null;F?(F.comment&&(w=F.comment),F.commentBefore&&(g=!0)):v.value==null&&(A!=null&&A.comment)&&(w=A.comment)}w&&(g=!0);let I=Lr(v,p,()=>w=null);O<i.length-1&&(I+=","),w&&(I+=Bn(I,l,h(w))),!g&&(S.length>x||I.includes(`
`))&&(g=!0),S.push(I),x=S.length}const{start:T,end:E}=r;if(S.length===0)return T+E;if(!g){const O=S.reduce((v,w)=>v+w.length+2,2);g=n.options.lineWidth>0&&O>n.options.lineWidth}if(g){let O=T;for(const v of S)O+=v?`
${u}${o}${v}`:`
`;return`${O}
${o}${E}`}else return`${T}${f}${S.join(" ")}${f}${E}`}function al({indent:i,options:{commentString:n}},r,l,o){if(l&&o&&(l=l.replace(/^\n+/,"")),l){const u=Zt(n(l),i);r.push(u.trimStart())}}function Fn(i,n){const r=ve(n)?n.value:n;for(const l of i)if(_e(l)&&(l.key===n||l.key===r||ve(l.key)&&l.key.value===r))return l}class wt extends mh{static get tagName(){return"tag:yaml.org,2002:map"}constructor(n){super(kn,n),this.items=[]}static from(n,r,l){const{keepUndefined:o,replacer:u}=l,f=new this(n),h=(p,g)=>{if(typeof u=="function")g=u.call(r,p,g);else if(Array.isArray(u)&&!u.includes(p))return;(g!==void 0||o)&&f.items.push(Ya(p,g,l))};if(r instanceof Map)for(const[p,g]of r)h(p,g);else if(r&&typeof r=="object")for(const p of Object.keys(r))h(p,r[p]);return typeof n.sortMapEntries=="function"&&f.items.sort(n.sortMapEntries),f}add(n,r){var f;let l;_e(n)?l=n:!n||typeof n!="object"||!("key"in n)?l=new Ze(n,n==null?void 0:n.value):l=new Ze(n.key,n.value);const o=Fn(this.items,l.key),u=(f=this.schema)==null?void 0:f.sortMapEntries;if(o){if(!r)throw new Error(`Key ${l.key} already set`);ve(o.value)&&ph(l.value)?o.value.value=l.value:o.value=l.value}else if(u){const h=this.items.findIndex(p=>u(l,p)<0);h===-1?this.items.push(l):this.items.splice(h,0,l)}else this.items.push(l)}delete(n){const r=Fn(this.items,n);return r?this.items.splice(this.items.indexOf(r),1).length>0:!1}get(n,r){const l=Fn(this.items,n),o=l==null?void 0:l.value;return(!r&&ve(o)?o.value:o)??void 0}has(n){return!!Fn(this.items,n)}set(n,r){this.add(new Ze(n,r),!0)}toJSON(n,r,l){const o=l?new l:r!=null&&r.mapAsMap?new Map:{};r!=null&&r.onCreate&&r.onCreate(o);for(const u of this.items)Sh(r,o,u);return o}toString(n,r,l){if(!n)return JSON.stringify(this);for(const o of this.items)if(!_e(o))throw new Error(`Map items must all be pairs; found ${JSON.stringify(o)} instead`);return!n.allNullValues&&this.hasAllNullValues(!1)&&(n=Object.assign({},n,{allNullValues:!0})),kh(this,n,{blockItemPrefix:"",flowChars:{start:"{",end:"}"},itemIndent:n.indent||"",onChompKeep:l,onComment:r})}}const jr={collection:"map",default:!0,nodeClass:wt,tag:"tag:yaml.org,2002:map",resolve(i,n){return Pr(i)||n("Expected a mapping for this tag"),i},createNode:(i,n,r)=>wt.from(i,n,r)};class Nn extends mh{static get tagName(){return"tag:yaml.org,2002:seq"}constructor(n){super(Ar,n),this.items=[]}add(n){this.items.push(n)}delete(n){const r=Hi(n);return typeof r!="number"?!1:this.items.splice(r,1).length>0}get(n,r){const l=Hi(n);if(typeof l!="number")return;const o=this.items[l];return!r&&ve(o)?o.value:o}has(n){const r=Hi(n);return typeof r=="number"&&r<this.items.length}set(n,r){const l=Hi(n);if(typeof l!="number")throw new Error(`Expected a valid index, not ${n}.`);const o=this.items[l];ve(o)&&ph(r)?o.value=r:this.items[l]=r}toJSON(n,r){const l=[];r!=null&&r.onCreate&&r.onCreate(l);let o=0;for(const u of this.items)l.push(Ct(u,String(o++),r));return l}toString(n,r,l){return n?kh(this,n,{blockItemPrefix:"- ",flowChars:{start:"[",end:"]"},itemIndent:(n.indent||"")+"  ",onChompKeep:l,onComment:r}):JSON.stringify(this)}static from(n,r,l){const{replacer:o}=l,u=new this(n);if(r&&Symbol.iterator in Object(r)){let f=0;for(let h of r){if(typeof o=="function"){const p=r instanceof Set?h:String(f++);h=o.call(r,p,h)}u.items.push(Ls(h,void 0,l))}}return u}}function Hi(i){let n=ve(i)?i.value:i;return n&&typeof n=="string"&&(n=Number(n)),typeof n=="number"&&Number.isInteger(n)&&n>=0?n:null}const Rr={collection:"seq",default:!0,nodeClass:Nn,tag:"tag:yaml.org,2002:seq",resolve(i,n){return Mr(i)||n("Expected a sequence for this tag"),i},createNode:(i,n,r)=>Nn.from(i,n,r)},wl={identify:i=>typeof i=="string",default:!0,tag:"tag:yaml.org,2002:str",resolve:i=>i,stringify(i,n,r,l){return n=Object.assign({actualString:!0},n),As(i,n,r,l)}},Sl={identify:i=>i==null,createNode:()=>new ie(null),default:!0,tag:"tag:yaml.org,2002:null",test:/^(?:~|[Nn]ull|NULL)?$/,resolve:()=>new ie(null),stringify:({source:i},n)=>typeof i=="string"&&Sl.test.test(i)?i:n.options.nullStr},Ga={identify:i=>typeof i=="boolean",default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:[Tt]rue|TRUE|[Ff]alse|FALSE)$/,resolve:i=>new ie(i[0]==="t"||i[0]==="T"),stringify({source:i,value:n},r){if(i&&Ga.test.test(i)){const l=i[0]==="t"||i[0]==="T";if(n===l)return i}return n?r.options.trueStr:r.options.falseStr}};function Rt({format:i,minFractionDigits:n,tag:r,value:l}){if(typeof l=="bigint")return String(l);const o=typeof l=="number"?l:Number(l);if(!isFinite(o))return isNaN(o)?".nan":o<0?"-.inf":".inf";let u=JSON.stringify(l);if(!i&&n&&(!r||r==="tag:yaml.org,2002:float")&&/^\d/.test(u)){let f=u.indexOf(".");f<0&&(f=u.length,u+=".");let h=n-(u.length-f-1);for(;h-- >0;)u+="0"}return u}const Eh={identify:i=>typeof i=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.(?:inf|Inf|INF)|\.nan|\.NaN|\.NAN)$/,resolve:i=>i.slice(-3).toLowerCase()==="nan"?NaN:i[0]==="-"?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,stringify:Rt},Nh={identify:i=>typeof i=="number",default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:\.[0-9]+|[0-9]+(?:\.[0-9]*)?)[eE][-+]?[0-9]+$/,resolve:i=>parseFloat(i),stringify(i){const n=Number(i.value);return isFinite(n)?n.toExponential():Rt(i)}},xh={identify:i=>typeof i=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:\.[0-9]+|[0-9]+\.[0-9]*)$/,resolve(i){const n=new ie(parseFloat(i)),r=i.indexOf(".");return r!==-1&&i[i.length-1]==="0"&&(n.minFractionDigits=i.length-r-1),n},stringify:Rt},kl=i=>typeof i=="bigint"||Number.isInteger(i),Xa=(i,n,r,{intAsBigInt:l})=>l?BigInt(i):parseInt(i.substring(n),r);function Th(i,n,r){const{value:l}=i;return kl(l)&&l>=0?r+l.toString(n):Rt(i)}const Ch={identify:i=>kl(i)&&i>=0,default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^0o[0-7]+$/,resolve:(i,n,r)=>Xa(i,2,8,r),stringify:i=>Th(i,8,"0o")},_h={identify:kl,default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9]+$/,resolve:(i,n,r)=>Xa(i,0,10,r),stringify:Rt},Lh={identify:i=>kl(i)&&i>=0,default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^0x[0-9a-fA-F]+$/,resolve:(i,n,r)=>Xa(i,2,16,r),stringify:i=>Th(i,16,"0x")},Ey=[jr,Rr,wl,Sl,Ga,Ch,_h,Lh,Eh,Nh,xh];function Td(i){return typeof i=="bigint"||Number.isInteger(i)}const Qi=({value:i})=>JSON.stringify(i),Ny=[{identify:i=>typeof i=="string",default:!0,tag:"tag:yaml.org,2002:str",resolve:i=>i,stringify:Qi},{identify:i=>i==null,createNode:()=>new ie(null),default:!0,tag:"tag:yaml.org,2002:null",test:/^null$/,resolve:()=>null,stringify:Qi},{identify:i=>typeof i=="boolean",default:!0,tag:"tag:yaml.org,2002:bool",test:/^true|false$/,resolve:i=>i==="true",stringify:Qi},{identify:Td,default:!0,tag:"tag:yaml.org,2002:int",test:/^-?(?:0|[1-9][0-9]*)$/,resolve:(i,n,{intAsBigInt:r})=>r?BigInt(i):parseInt(i,10),stringify:({value:i})=>Td(i)?i.toString():JSON.stringify(i)},{identify:i=>typeof i=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^-?(?:0|[1-9][0-9]*)(?:\.[0-9]*)?(?:[eE][-+]?[0-9]+)?$/,resolve:i=>parseFloat(i),stringify:Qi}],xy={default:!0,tag:"",test:/^/,resolve(i,n){return n(`Unresolved plain scalar ${JSON.stringify(i)}`),i}},Ty=[jr,Rr].concat(Ny,xy),Za={identify:i=>i instanceof Uint8Array,default:!1,tag:"tag:yaml.org,2002:binary",resolve(i,n){if(typeof Buffer=="function")return Buffer.from(i,"base64");if(typeof atob=="function"){const r=atob(i.replace(/[\n\r]/g,"")),l=new Uint8Array(r.length);for(let o=0;o<r.length;++o)l[o]=r.charCodeAt(o);return l}else return n("This environment does not support reading binary tags; either Buffer or atob is required"),i},stringify({comment:i,type:n,value:r},l,o,u){const f=r;let h;if(typeof Buffer=="function")h=f instanceof Buffer?f.toString("base64"):Buffer.from(f.buffer).toString("base64");else if(typeof btoa=="function"){let p="";for(let g=0;g<f.length;++g)p+=String.fromCharCode(f[g]);h=btoa(p)}else throw new Error("This environment does not support writing binary tags; either Buffer or btoa is required");if(n||(n=ie.BLOCK_LITERAL),n!==ie.QUOTE_DOUBLE){const p=Math.max(l.options.lineWidth-l.indent.length,l.options.minContentWidth),g=Math.ceil(h.length/p),x=new Array(g);for(let S=0,T=0;S<g;++S,T+=p)x[S]=h.substr(T,p);h=x.join(n===ie.BLOCK_LITERAL?`
`:" ")}return As({comment:i,type:n,value:h},l,o,u)}};function Oh(i,n){if(Mr(i))for(let r=0;r<i.items.length;++r){let l=i.items[r];if(!_e(l)){if(Pr(l)){l.items.length>1&&n("Each pair must have its own sequence indicator");const o=l.items[0]||new Ze(new ie(null));if(l.commentBefore&&(o.key.commentBefore=o.key.commentBefore?`${l.commentBefore}
${o.key.commentBefore}`:l.commentBefore),l.comment){const u=o.value??o.key;u.comment=u.comment?`${l.comment}
${u.comment}`:l.comment}l=o}i.items[r]=_e(l)?l:new Ze(l)}}else n("Expected a sequence for this tag");return i}function $h(i,n,r){const{replacer:l}=r,o=new Nn(i);o.tag="tag:yaml.org,2002:pairs";let u=0;if(n&&Symbol.iterator in Object(n))for(let f of n){typeof l=="function"&&(f=l.call(n,String(u++),f));let h,p;if(Array.isArray(f))if(f.length===2)h=f[0],p=f[1];else throw new TypeError(`Expected [key, value] tuple: ${f}`);else if(f&&f instanceof Object){const g=Object.keys(f);if(g.length===1)h=g[0],p=f[h];else throw new TypeError(`Expected tuple with one key, not ${g.length} keys`)}else h=f;o.items.push(Ya(h,p,r))}return o}const eu={collection:"seq",default:!1,tag:"tag:yaml.org,2002:pairs",resolve:Oh,createNode:$h};class Cr extends Nn{constructor(){super(),this.add=wt.prototype.add.bind(this),this.delete=wt.prototype.delete.bind(this),this.get=wt.prototype.get.bind(this),this.has=wt.prototype.has.bind(this),this.set=wt.prototype.set.bind(this),this.tag=Cr.tag}toJSON(n,r){if(!r)return super.toJSON(n);const l=new Map;r!=null&&r.onCreate&&r.onCreate(l);for(const o of this.items){let u,f;if(_e(o)?(u=Ct(o.key,"",r),f=Ct(o.value,u,r)):u=Ct(o,"",r),l.has(u))throw new Error("Ordered maps must not include duplicate keys");l.set(u,f)}return l}static from(n,r,l){const o=$h(n,r,l),u=new this;return u.items=o.items,u}}Cr.tag="tag:yaml.org,2002:omap";const tu={collection:"seq",identify:i=>i instanceof Map,nodeClass:Cr,default:!1,tag:"tag:yaml.org,2002:omap",resolve(i,n){const r=Oh(i,n),l=[];for(const{key:o}of r.items)ve(o)&&(l.includes(o.value)?n(`Ordered maps must not include duplicate keys: ${o.value}`):l.push(o.value));return Object.assign(new Cr,r)},createNode:(i,n,r)=>Cr.from(i,n,r)};function Ih({value:i,source:n},r){return n&&(i?Ah:Ph).test.test(n)?n:i?r.options.trueStr:r.options.falseStr}const Ah={identify:i=>i===!0,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:Y|y|[Yy]es|YES|[Tt]rue|TRUE|[Oo]n|ON)$/,resolve:()=>new ie(!0),stringify:Ih},Ph={identify:i=>i===!1,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:N|n|[Nn]o|NO|[Ff]alse|FALSE|[Oo]ff|OFF)$/,resolve:()=>new ie(!1),stringify:Ih},Cy={identify:i=>typeof i=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.(?:inf|Inf|INF)|\.nan|\.NaN|\.NAN)$/,resolve:i=>i.slice(-3).toLowerCase()==="nan"?NaN:i[0]==="-"?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,stringify:Rt},_y={identify:i=>typeof i=="number",default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:[0-9][0-9_]*)?(?:\.[0-9_]*)?[eE][-+]?[0-9]+$/,resolve:i=>parseFloat(i.replace(/_/g,"")),stringify(i){const n=Number(i.value);return isFinite(n)?n.toExponential():Rt(i)}},Ly={identify:i=>typeof i=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:[0-9][0-9_]*)?\.[0-9_]*$/,resolve(i){const n=new ie(parseFloat(i.replace(/_/g,""))),r=i.indexOf(".");if(r!==-1){const l=i.substring(r+1).replace(/_/g,"");l[l.length-1]==="0"&&(n.minFractionDigits=l.length)}return n},stringify:Rt},Ps=i=>typeof i=="bigint"||Number.isInteger(i);function El(i,n,r,{intAsBigInt:l}){const o=i[0];if((o==="-"||o==="+")&&(n+=1),i=i.substring(n).replace(/_/g,""),l){switch(r){case 2:i=`0b${i}`;break;case 8:i=`0o${i}`;break;case 16:i=`0x${i}`;break}const f=BigInt(i);return o==="-"?BigInt(-1)*f:f}const u=parseInt(i,r);return o==="-"?-1*u:u}function nu(i,n,r){const{value:l}=i;if(Ps(l)){const o=l.toString(n);return l<0?"-"+r+o.substr(1):r+o}return Rt(i)}const Oy={identify:Ps,default:!0,tag:"tag:yaml.org,2002:int",format:"BIN",test:/^[-+]?0b[0-1_]+$/,resolve:(i,n,r)=>El(i,2,2,r),stringify:i=>nu(i,2,"0b")},$y={identify:Ps,default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^[-+]?0[0-7_]+$/,resolve:(i,n,r)=>El(i,1,8,r),stringify:i=>nu(i,8,"0")},Iy={identify:Ps,default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9][0-9_]*$/,resolve:(i,n,r)=>El(i,0,10,r),stringify:Rt},Ay={identify:Ps,default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^[-+]?0x[0-9a-fA-F_]+$/,resolve:(i,n,r)=>El(i,2,16,r),stringify:i=>nu(i,16,"0x")};class _r extends wt{constructor(n){super(n),this.tag=_r.tag}add(n){let r;_e(n)?r=n:n&&typeof n=="object"&&"key"in n&&"value"in n&&n.value===null?r=new Ze(n.key,null):r=new Ze(n,null),Fn(this.items,r.key)||this.items.push(r)}get(n,r){const l=Fn(this.items,n);return!r&&_e(l)?ve(l.key)?l.key.value:l.key:l}set(n,r){if(typeof r!="boolean")throw new Error(`Expected boolean value for set(key, value) in a YAML set, not ${typeof r}`);const l=Fn(this.items,n);l&&!r?this.items.splice(this.items.indexOf(l),1):!l&&r&&this.items.push(new Ze(n))}toJSON(n,r){return super.toJSON(n,r,Set)}toString(n,r,l){if(!n)return JSON.stringify(this);if(this.hasAllNullValues(!0))return super.toString(Object.assign({},n,{allNullValues:!0}),r,l);throw new Error("Set items must all have null values")}static from(n,r,l){const{replacer:o}=l,u=new this(n);if(r&&Symbol.iterator in Object(r))for(let f of r)typeof o=="function"&&(f=o.call(r,f,f)),u.items.push(Ya(f,null,l));return u}}_r.tag="tag:yaml.org,2002:set";const ru={collection:"map",identify:i=>i instanceof Set,nodeClass:_r,default:!1,tag:"tag:yaml.org,2002:set",createNode:(i,n,r)=>_r.from(i,n,r),resolve(i,n){if(Pr(i)){if(i.hasAllNullValues(!0))return Object.assign(new _r,i);n("Set items must all have null values")}else n("Expected a mapping for this tag");return i}};function su(i,n){const r=i[0],l=r==="-"||r==="+"?i.substring(1):i,o=f=>n?BigInt(f):Number(f),u=l.replace(/_/g,"").split(":").reduce((f,h)=>f*o(60)+o(h),o(0));return r==="-"?o(-1)*u:u}function Mh(i){let{value:n}=i,r=f=>f;if(typeof n=="bigint")r=f=>BigInt(f);else if(isNaN(n)||!isFinite(n))return Rt(i);let l="";n<0&&(l="-",n*=r(-1));const o=r(60),u=[n%o];return n<60?u.unshift(0):(n=(n-u[0])/o,u.unshift(n%o),n>=60&&(n=(n-u[0])/o,u.unshift(n))),l+u.map(f=>String(f).padStart(2,"0")).join(":").replace(/000000\d*$/,"")}const jh={identify:i=>typeof i=="bigint"||Number.isInteger(i),default:!0,tag:"tag:yaml.org,2002:int",format:"TIME",test:/^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+$/,resolve:(i,n,{intAsBigInt:r})=>su(i,r),stringify:Mh},Rh={identify:i=>typeof i=="number",default:!0,tag:"tag:yaml.org,2002:float",format:"TIME",test:/^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\.[0-9_]*$/,resolve:i=>su(i,!1),stringify:Mh},Nl={identify:i=>i instanceof Date,default:!0,tag:"tag:yaml.org,2002:timestamp",test:RegExp("^([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})(?:(?:t|T|[ \\t]+)([0-9]{1,2}):([0-9]{1,2}):([0-9]{1,2}(\\.[0-9]+)?)(?:[ \\t]*(Z|[-+][012]?[0-9](?::[0-9]{2})?))?)?$"),resolve(i){const n=i.match(Nl.test);if(!n)throw new Error("!!timestamp expects a date, starting with yyyy-mm-dd");const[,r,l,o,u,f,h]=n.map(Number),p=n[7]?Number((n[7]+"00").substr(1,3)):0;let g=Date.UTC(r,l-1,o,u||0,f||0,h||0,p);const x=n[8];if(x&&x!=="Z"){let S=su(x,!1);Math.abs(S)<30&&(S*=60),g-=6e4*S}return new Date(g)},stringify:({value:i})=>i.toISOString().replace(/((T00:00)?:00)?\.000Z$/,"")},Cd=[jr,Rr,wl,Sl,Ah,Ph,Oy,$y,Iy,Ay,Cy,_y,Ly,Za,en,tu,eu,ru,jh,Rh,Nl],_d=new Map([["core",Ey],["failsafe",[jr,Rr,wl]],["json",Ty],["yaml11",Cd],["yaml-1.1",Cd]]),Ld={binary:Za,bool:Ga,float:xh,floatExp:Nh,floatNaN:Eh,floatTime:Rh,int:_h,intHex:Lh,intOct:Ch,intTime:jh,map:jr,merge:en,null:Sl,omap:tu,pairs:eu,seq:Rr,set:ru,timestamp:Nl},Py={"tag:yaml.org,2002:binary":Za,"tag:yaml.org,2002:merge":en,"tag:yaml.org,2002:omap":tu,"tag:yaml.org,2002:pairs":eu,"tag:yaml.org,2002:set":ru,"tag:yaml.org,2002:timestamp":Nl};function $a(i,n,r){const l=_d.get(n);if(l&&!i)return r&&!l.includes(en)?l.concat(en):l.slice();let o=l;if(!o)if(Array.isArray(i))o=[];else{const u=Array.from(_d.keys()).filter(f=>f!=="yaml11").map(f=>JSON.stringify(f)).join(", ");throw new Error(`Unknown schema "${n}"; use one of ${u} or define customTags array`)}if(Array.isArray(i))for(const u of i)o=o.concat(u);else typeof i=="function"&&(o=i(o.slice()));return r&&(o=o.concat(en)),o.reduce((u,f)=>{const h=typeof f=="string"?Ld[f]:f;if(!h){const p=JSON.stringify(f),g=Object.keys(Ld).map(x=>JSON.stringify(x)).join(", ");throw new Error(`Unknown custom tag ${p}; use one of ${g}`)}return u.includes(h)||u.push(h),u},[])}const My=(i,n)=>i.key<n.key?-1:i.key>n.key?1:0;class xl{constructor({compat:n,customTags:r,merge:l,resolveKnownTags:o,schema:u,sortMapEntries:f,toStringDefaults:h}){this.compat=Array.isArray(n)?$a(n,"compat"):n?$a(null,n):null,this.name=typeof u=="string"&&u||"core",this.knownTags=o?Py:{},this.tags=$a(r,this.name,l),this.toStringOptions=h??null,Object.defineProperty(this,kn,{value:jr}),Object.defineProperty(this,Vt,{value:wl}),Object.defineProperty(this,Ar,{value:Rr}),this.sortMapEntries=typeof f=="function"?f:f===!0?My:null}clone(){const n=Object.create(xl.prototype,Object.getOwnPropertyDescriptors(this));return n.tags=this.tags.slice(),n}}function jy(i,n){var p;const r=[];let l=n.directives===!0;if(n.directives!==!1&&i.directives){const g=i.directives.toString(i);g?(r.push(g),l=!0):i.directives.docStart&&(l=!0)}l&&r.push("---");const o=yh(i,n),{commentString:u}=o.options;if(i.commentBefore){r.length!==1&&r.unshift("");const g=u(i.commentBefore);r.unshift(Zt(g,""))}let f=!1,h=null;if(i.contents){if(Ie(i.contents)){if(i.contents.spaceBefore&&l&&r.push(""),i.contents.commentBefore){const S=u(i.contents.commentBefore);r.push(Zt(S,""))}o.forceBlockIndent=!!i.comment,h=i.contents.comment}const g=h?void 0:()=>f=!0;let x=Lr(i.contents,o,()=>h=null,g);h&&(x+=Bn(x,"",u(h))),(x[0]==="|"||x[0]===">")&&r[r.length-1]==="---"?r[r.length-1]=`--- ${x}`:r.push(x)}else r.push(Lr(i.contents,o));if((p=i.directives)!=null&&p.docEnd)if(i.comment){const g=u(i.comment);g.includes(`
`)?(r.push("..."),r.push(Zt(g,""))):r.push(`... ${g}`)}else r.push("...");else{let g=i.comment;g&&f&&(g=g.replace(/^\n+/,"")),g&&((!f||h)&&r[r.length-1]!==""&&r.push(""),r.push(Zt(u(g),"")))}return r.join(`
`)+`
`}class Dr{constructor(n,r,l){this.commentBefore=null,this.comment=null,this.errors=[],this.warnings=[],Object.defineProperty(this,_t,{value:Fa});let o=null;typeof r=="function"||Array.isArray(r)?o=r:l===void 0&&r&&(l=r,r=void 0);const u=Object.assign({intAsBigInt:!1,keepSourceTokens:!1,logLevel:"warn",prettyErrors:!0,strict:!0,stringKeys:!1,uniqueKeys:!0,version:"1.2"},l);this.options=u;let{version:f}=u;l!=null&&l._directives?(this.directives=l._directives.atDocument(),this.directives.yaml.explicit&&(f=this.directives.yaml.version)):this.directives=new nt({version:f}),this.setSchema(f,l),this.contents=n===void 0?null:this.createNode(n,o,l)}clone(){const n=Object.create(Dr.prototype,{[_t]:{value:Fa}});return n.commentBefore=this.commentBefore,n.comment=this.comment,n.errors=this.errors.slice(),n.warnings=this.warnings.slice(),n.options=Object.assign({},this.options),this.directives&&(n.directives=this.directives.clone()),n.schema=this.schema.clone(),n.contents=Ie(this.contents)?this.contents.clone(n.schema):this.contents,this.range&&(n.range=this.range.slice()),n}add(n){yr(this.contents)&&this.contents.add(n)}addIn(n,r){yr(this.contents)&&this.contents.addIn(n,r)}createAlias(n,r){if(!n.anchor){const l=dh(this);n.anchor=!r||l.has(r)?hh(r||"a",l):r}return new ml(n.anchor)}createNode(n,r,l){let o;if(typeof r=="function")n=r.call({"":n},"",n),o=r;else if(Array.isArray(r)){const w=A=>typeof A=="number"||A instanceof String||A instanceof Number,I=r.filter(w).map(String);I.length>0&&(r=r.concat(I)),o=r}else l===void 0&&r&&(l=r,r=void 0);const{aliasDuplicateObjects:u,anchorPrefix:f,flow:h,keepUndefined:p,onTagObj:g,tag:x}=l??{},{onAnchor:S,setAnchors:T,sourceObjects:E}=uy(this,f||"a"),O={aliasDuplicateObjects:u??!0,keepUndefined:p??!1,onAnchor:S,onTagObj:g,replacer:o,schema:this.schema,sourceObjects:E},v=Ls(n,x,O);return h&&$e(v)&&(v.flow=!0),T(),v}createPair(n,r,l={}){const o=this.createNode(n,null,l),u=this.createNode(r,null,l);return new Ze(o,u)}delete(n){return yr(this.contents)?this.contents.delete(n):!1}deleteIn(n){return Cs(n)?this.contents==null?!1:(this.contents=null,!0):yr(this.contents)?this.contents.deleteIn(n):!1}get(n,r){return $e(this.contents)?this.contents.get(n,r):void 0}getIn(n,r){return Cs(n)?!r&&ve(this.contents)?this.contents.value:this.contents:$e(this.contents)?this.contents.getIn(n,r):void 0}has(n){return $e(this.contents)?this.contents.has(n):!1}hasIn(n){return Cs(n)?this.contents!==void 0:$e(this.contents)?this.contents.hasIn(n):!1}set(n,r){this.contents==null?this.contents=ol(this.schema,[n],r):yr(this.contents)&&this.contents.set(n,r)}setIn(n,r){Cs(n)?this.contents=r:this.contents==null?this.contents=ol(this.schema,Array.from(n),r):yr(this.contents)&&this.contents.setIn(n,r)}setSchema(n,r={}){typeof n=="number"&&(n=String(n));let l;switch(n){case"1.1":this.directives?this.directives.yaml.version="1.1":this.directives=new nt({version:"1.1"}),l={resolveKnownTags:!1,schema:"yaml-1.1"};break;case"1.2":case"next":this.directives?this.directives.yaml.version=n:this.directives=new nt({version:n}),l={resolveKnownTags:!0,schema:"core"};break;case null:this.directives&&delete this.directives,l=null;break;default:{const o=JSON.stringify(n);throw new Error(`Expected '1.1', '1.2' or null as first argument, but found: ${o}`)}}if(r.schema instanceof Object)this.schema=r.schema;else if(l)this.schema=new xl(Object.assign(l,r));else throw new Error("With a null YAML version, the { schema: Schema } option is required")}toJS({json:n,jsonArg:r,mapAsMap:l,maxAliasCount:o,onAnchor:u,reviver:f}={}){const h={anchors:new Map,doc:this,keep:!n,mapAsMap:l===!0,mapKeyWarned:!1,maxAliasCount:typeof o=="number"?o:100},p=Ct(this.contents,r??"",h);if(typeof u=="function")for(const{count:g,res:x}of h.anchors.values())u(x,g);return typeof f=="function"?Nr(f,{"":p},"",p):p}toJSON(n,r){return this.toJS({json:!0,jsonArg:n,mapAsMap:!1,onAnchor:r})}toString(n={}){if(this.errors.length>0)throw new Error("Document with errors cannot be stringified");if("indent"in n&&(!Number.isInteger(n.indent)||Number(n.indent)<=0)){const r=JSON.stringify(n.indent);throw new Error(`"indent" option must be a positive integer, not ${r}`)}return jy(this,n)}}function yr(i){if($e(i))return!0;throw new Error("Expected a YAML collection as document contents")}class iu extends Error{constructor(n,r,l,o){super(),this.name=n,this.code=l,this.message=o,this.pos=r}}class zn extends iu{constructor(n,r,l){super("YAMLParseError",n,r,l)}}class Dh extends iu{constructor(n,r,l){super("YAMLWarning",n,r,l)}}const ul=(i,n)=>r=>{if(r.pos[0]===-1)return;r.linePos=r.pos.map(h=>n.linePos(h));const{line:l,col:o}=r.linePos[0];r.message+=` at line ${l}, column ${o}`;let u=o-1,f=i.substring(n.lineStarts[l-1],n.lineStarts[l]).replace(/[\n\r]+$/,"");if(u>=60&&f.length>80){const h=Math.min(u-39,f.length-79);f="…"+f.substring(h),u-=h-1}if(f.length>80&&(f=f.substring(0,79)+"…"),l>1&&/^ *$/.test(f.substring(0,u))){let h=i.substring(n.lineStarts[l-2],n.lineStarts[l-1]);h.length>80&&(h=h.substring(0,79)+`…
`),f=h+f}if(/[^ ]/.test(f)){let h=1;const p=r.linePos[1];p&&p.line===l&&p.col>o&&(h=Math.max(1,Math.min(p.col-o,80-u)));const g=" ".repeat(u)+"^".repeat(h);r.message+=`:

${f}
${g}
`}};function Or(i,{flow:n,indicator:r,next:l,offset:o,onError:u,parentIndent:f,startOnNewline:h}){let p=!1,g=h,x=h,S="",T="",E=!1,O=!1,v=null,w=null,I=null,A=null,F=null,V=null,W=null;for(const z of i)switch(O&&(z.type!=="space"&&z.type!=="newline"&&z.type!=="comma"&&u(z.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),O=!1),v&&(g&&z.type!=="comment"&&z.type!=="newline"&&u(v,"TAB_AS_INDENT","Tabs are not allowed as indentation"),v=null),z.type){case"space":!n&&(r!=="doc-start"||(l==null?void 0:l.type)!=="flow-collection")&&z.source.includes("	")&&(v=z),x=!0;break;case"comment":{x||u(z,"MISSING_CHAR","Comments must be separated from other tokens by white space characters");const P=z.source.substring(1)||" ";S?S+=T+P:S=P,T="",g=!1;break}case"newline":g?S?S+=z.source:p=!0:T+=z.source,g=!0,E=!0,(w||I)&&(A=z),x=!0;break;case"anchor":w&&u(z,"MULTIPLE_ANCHORS","A node can have at most one anchor"),z.source.endsWith(":")&&u(z.offset+z.source.length-1,"BAD_ALIAS","Anchor ending in : is ambiguous",!0),w=z,W===null&&(W=z.offset),g=!1,x=!1,O=!0;break;case"tag":{I&&u(z,"MULTIPLE_TAGS","A node can have at most one tag"),I=z,W===null&&(W=z.offset),g=!1,x=!1,O=!0;break}case r:(w||I)&&u(z,"BAD_PROP_ORDER",`Anchors and tags must be after the ${z.source} indicator`),V&&u(z,"UNEXPECTED_TOKEN",`Unexpected ${z.source} in ${n??"collection"}`),V=z,g=r==="seq-item-ind"||r==="explicit-key-ind",x=!1;break;case"comma":if(n){F&&u(z,"UNEXPECTED_TOKEN",`Unexpected , in ${n}`),F=z,g=!1,x=!1;break}default:u(z,"UNEXPECTED_TOKEN",`Unexpected ${z.type} token`),g=!1,x=!1}const j=i[i.length-1],Y=j?j.offset+j.source.length:o;return O&&l&&l.type!=="space"&&l.type!=="newline"&&l.type!=="comma"&&(l.type!=="scalar"||l.source!=="")&&u(l.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),v&&(g&&v.indent<=f||(l==null?void 0:l.type)==="block-map"||(l==null?void 0:l.type)==="block-seq")&&u(v,"TAB_AS_INDENT","Tabs are not allowed as indentation"),{comma:F,found:V,spaceBefore:p,comment:S,hasNewline:E,anchor:w,tag:I,newlineAfterProp:A,end:Y,start:W??Y}}function Os(i){if(!i)return null;switch(i.type){case"alias":case"scalar":case"double-quoted-scalar":case"single-quoted-scalar":if(i.source.includes(`
`))return!0;if(i.end){for(const n of i.end)if(n.type==="newline")return!0}return!1;case"flow-collection":for(const n of i.items){for(const r of n.start)if(r.type==="newline")return!0;if(n.sep){for(const r of n.sep)if(r.type==="newline")return!0}if(Os(n.key)||Os(n.value))return!0}return!1;default:return!0}}function Ka(i,n,r){if((n==null?void 0:n.type)==="flow-collection"){const l=n.end[0];l.indent===i&&(l.source==="]"||l.source==="}")&&Os(n)&&r(l,"BAD_INDENT","Flow end indicator should be more indented than parent",!0)}}function bh(i,n,r){const{uniqueKeys:l}=i.options;if(l===!1)return!1;const o=typeof l=="function"?l:(u,f)=>u===f||ve(u)&&ve(f)&&u.value===f.value;return n.some(u=>o(u.key,r))}const Od="All mapping items must start at the same column";function Ry({composeNode:i,composeEmptyNode:n},r,l,o,u){var x;const f=(u==null?void 0:u.nodeClass)??wt,h=new f(r.schema);r.atRoot&&(r.atRoot=!1);let p=l.offset,g=null;for(const S of l.items){const{start:T,key:E,sep:O,value:v}=S,w=Or(T,{indicator:"explicit-key-ind",next:E??(O==null?void 0:O[0]),offset:p,onError:o,parentIndent:l.indent,startOnNewline:!0}),I=!w.found;if(I){if(E&&(E.type==="block-seq"?o(p,"BLOCK_AS_IMPLICIT_KEY","A block sequence may not be used as an implicit map key"):"indent"in E&&E.indent!==l.indent&&o(p,"BAD_INDENT",Od)),!w.anchor&&!w.tag&&!O){g=w.end,w.comment&&(h.comment?h.comment+=`
`+w.comment:h.comment=w.comment);continue}(w.newlineAfterProp||Os(E))&&o(E??T[T.length-1],"MULTILINE_IMPLICIT_KEY","Implicit keys need to be on a single line")}else((x=w.found)==null?void 0:x.indent)!==l.indent&&o(p,"BAD_INDENT",Od);r.atKey=!0;const A=w.end,F=E?i(r,E,w,o):n(r,A,T,null,w,o);r.schema.compat&&Ka(l.indent,E,o),r.atKey=!1,bh(r,h.items,F)&&o(A,"DUPLICATE_KEY","Map keys must be unique");const V=Or(O??[],{indicator:"map-value-ind",next:v,offset:F.range[2],onError:o,parentIndent:l.indent,startOnNewline:!E||E.type==="block-scalar"});if(p=V.end,V.found){I&&((v==null?void 0:v.type)==="block-map"&&!V.hasNewline&&o(p,"BLOCK_AS_IMPLICIT_KEY","Nested mappings are not allowed in compact mappings"),r.options.strict&&w.start<V.found.offset-1024&&o(F.range,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit block mapping key"));const W=v?i(r,v,V,o):n(r,p,O,null,V,o);r.schema.compat&&Ka(l.indent,v,o),p=W.range[2];const j=new Ze(F,W);r.options.keepSourceTokens&&(j.srcToken=S),h.items.push(j)}else{I&&o(F.range,"MISSING_CHAR","Implicit map keys need to be followed by map values"),V.comment&&(F.comment?F.comment+=`
`+V.comment:F.comment=V.comment);const W=new Ze(F);r.options.keepSourceTokens&&(W.srcToken=S),h.items.push(W)}}return g&&g<p&&o(g,"IMPOSSIBLE","Map comment with trailing content"),h.range=[l.offset,p,g??p],h}function Dy({composeNode:i,composeEmptyNode:n},r,l,o,u){const f=(u==null?void 0:u.nodeClass)??Nn,h=new f(r.schema);r.atRoot&&(r.atRoot=!1),r.atKey&&(r.atKey=!1);let p=l.offset,g=null;for(const{start:x,value:S}of l.items){const T=Or(x,{indicator:"seq-item-ind",next:S,offset:p,onError:o,parentIndent:l.indent,startOnNewline:!0});if(!T.found)if(T.anchor||T.tag||S)S&&S.type==="block-seq"?o(T.end,"BAD_INDENT","All sequence items must start at the same column"):o(p,"MISSING_CHAR","Sequence item without - indicator");else{g=T.end,T.comment&&(h.comment=T.comment);continue}const E=S?i(r,S,T,o):n(r,T.end,x,null,T,o);r.schema.compat&&Ka(l.indent,S,o),p=E.range[2],h.items.push(E)}return h.range=[l.offset,p,g??p],h}function Ms(i,n,r,l){let o="";if(i){let u=!1,f="";for(const h of i){const{source:p,type:g}=h;switch(g){case"space":u=!0;break;case"comment":{r&&!u&&l(h,"MISSING_CHAR","Comments must be separated from other tokens by white space characters");const x=p.substring(1)||" ";o?o+=f+x:o=x,f="";break}case"newline":o&&(f+=p),u=!0;break;default:l(h,"UNEXPECTED_TOKEN",`Unexpected ${g} at node end`)}n+=p.length}}return{comment:o,offset:n}}const Ia="Block collections are not allowed within flow collections",Aa=i=>i&&(i.type==="block-map"||i.type==="block-seq");function by({composeNode:i,composeEmptyNode:n},r,l,o,u){const f=l.start.source==="{",h=f?"flow map":"flow sequence",p=(u==null?void 0:u.nodeClass)??(f?wt:Nn),g=new p(r.schema);g.flow=!0;const x=r.atRoot;x&&(r.atRoot=!1),r.atKey&&(r.atKey=!1);let S=l.offset+l.start.source.length;for(let w=0;w<l.items.length;++w){const I=l.items[w],{start:A,key:F,sep:V,value:W}=I,j=Or(A,{flow:h,indicator:"explicit-key-ind",next:F??(V==null?void 0:V[0]),offset:S,onError:o,parentIndent:l.indent,startOnNewline:!1});if(!j.found){if(!j.anchor&&!j.tag&&!V&&!W){w===0&&j.comma?o(j.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${h}`):w<l.items.length-1&&o(j.start,"UNEXPECTED_TOKEN",`Unexpected empty item in ${h}`),j.comment&&(g.comment?g.comment+=`
`+j.comment:g.comment=j.comment),S=j.end;continue}!f&&r.options.strict&&Os(F)&&o(F,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line")}if(w===0)j.comma&&o(j.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${h}`);else if(j.comma||o(j.start,"MISSING_CHAR",`Missing , between ${h} items`),j.comment){let Y="";e:for(const z of A)switch(z.type){case"comma":case"space":break;case"comment":Y=z.source.substring(1);break e;default:break e}if(Y){let z=g.items[g.items.length-1];_e(z)&&(z=z.value??z.key),z.comment?z.comment+=`
`+Y:z.comment=Y,j.comment=j.comment.substring(Y.length+1)}}if(!f&&!V&&!j.found){const Y=W?i(r,W,j,o):n(r,j.end,V,null,j,o);g.items.push(Y),S=Y.range[2],Aa(W)&&o(Y.range,"BLOCK_IN_FLOW",Ia)}else{r.atKey=!0;const Y=j.end,z=F?i(r,F,j,o):n(r,Y,A,null,j,o);Aa(F)&&o(z.range,"BLOCK_IN_FLOW",Ia),r.atKey=!1;const P=Or(V??[],{flow:h,indicator:"map-value-ind",next:W,offset:z.range[2],onError:o,parentIndent:l.indent,startOnNewline:!1});if(P.found){if(!f&&!j.found&&r.options.strict){if(V)for(const M of V){if(M===P.found)break;if(M.type==="newline"){o(M,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line");break}}j.start<P.found.offset-1024&&o(P.found,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit flow sequence key")}}else W&&("source"in W&&W.source&&W.source[0]===":"?o(W,"MISSING_CHAR",`Missing space after : in ${h}`):o(P.start,"MISSING_CHAR",`Missing , or : between ${h} items`));const le=W?i(r,W,P,o):P.found?n(r,P.end,V,null,P,o):null;le?Aa(W)&&o(le.range,"BLOCK_IN_FLOW",Ia):P.comment&&(z.comment?z.comment+=`
`+P.comment:z.comment=P.comment);const ue=new Ze(z,le);if(r.options.keepSourceTokens&&(ue.srcToken=I),f){const M=g;bh(r,M.items,z)&&o(Y,"DUPLICATE_KEY","Map keys must be unique"),M.items.push(ue)}else{const M=new wt(r.schema);M.flow=!0,M.items.push(ue);const ee=(le??z).range;M.range=[z.range[0],ee[1],ee[2]],g.items.push(M)}S=le?le.range[2]:P.end}}const T=f?"}":"]",[E,...O]=l.end;let v=S;if(E&&E.source===T)v=E.offset+E.source.length;else{const w=h[0].toUpperCase()+h.substring(1),I=x?`${w} must end with a ${T}`:`${w} in block collection must be sufficiently indented and end with a ${T}`;o(S,x?"MISSING_CHAR":"BAD_INDENT",I),E&&E.source.length!==1&&O.unshift(E)}if(O.length>0){const w=Ms(O,v,r.options.strict,o);w.comment&&(g.comment?g.comment+=`
`+w.comment:g.comment=w.comment),g.range=[l.offset,v,w.offset]}else g.range=[l.offset,v,v];return g}function Pa(i,n,r,l,o,u){const f=r.type==="block-map"?Ry(i,n,r,l,u):r.type==="block-seq"?Dy(i,n,r,l,u):by(i,n,r,l,u),h=f.constructor;return o==="!"||o===h.tagName?(f.tag=h.tagName,f):(o&&(f.tag=o),f)}function By(i,n,r,l,o){var T;const u=l.tag,f=u?n.directives.tagName(u.source,E=>o(u,"TAG_RESOLVE_FAILED",E)):null;if(r.type==="block-seq"){const{anchor:E,newlineAfterProp:O}=l,v=E&&u?E.offset>u.offset?E:u:E??u;v&&(!O||O.offset<v.offset)&&o(v,"MISSING_CHAR","Missing newline after block sequence props")}const h=r.type==="block-map"?"map":r.type==="block-seq"?"seq":r.start.source==="{"?"map":"seq";if(!u||!f||f==="!"||f===wt.tagName&&h==="map"||f===Nn.tagName&&h==="seq")return Pa(i,n,r,o,f);let p=n.schema.tags.find(E=>E.tag===f&&E.collection===h);if(!p){const E=n.schema.knownTags[f];if(E&&E.collection===h)n.schema.tags.push(Object.assign({},E,{default:!1})),p=E;else return E!=null&&E.collection?o(u,"BAD_COLLECTION_TYPE",`${E.tag} used for ${h} collection, but expects ${E.collection}`,!0):o(u,"TAG_RESOLVE_FAILED",`Unresolved tag: ${f}`,!0),Pa(i,n,r,o,f)}const g=Pa(i,n,r,o,f,p),x=((T=p.resolve)==null?void 0:T.call(p,g,E=>o(u,"TAG_RESOLVE_FAILED",E),n.options))??g,S=Ie(x)?x:new ie(x);return S.range=g.range,S.tag=f,p!=null&&p.format&&(S.format=p.format),S}function Bh(i,n,r){const l=n.offset,o=Fy(n,i.options.strict,r);if(!o)return{value:"",type:null,comment:"",range:[l,l,l]};const u=o.mode===">"?ie.BLOCK_FOLDED:ie.BLOCK_LITERAL,f=n.source?zy(n.source):[];let h=f.length;for(let v=f.length-1;v>=0;--v){const w=f[v][1];if(w===""||w==="\r")h=v;else break}if(h===0){const v=o.chomp==="+"&&f.length>0?`
`.repeat(Math.max(1,f.length-1)):"";let w=l+o.length;return n.source&&(w+=n.source.length),{value:v,type:u,comment:o.comment,range:[l,w,w]}}let p=n.indent+o.indent,g=n.offset+o.length,x=0;for(let v=0;v<h;++v){const[w,I]=f[v];if(I===""||I==="\r")o.indent===0&&w.length>p&&(p=w.length);else{w.length<p&&r(g+w.length,"MISSING_CHAR","Block scalars with more-indented leading empty lines must use an explicit indentation indicator"),o.indent===0&&(p=w.length),x=v,p===0&&!i.atRoot&&r(g,"BAD_INDENT","Block scalar values in collections must be indented");break}g+=w.length+I.length+1}for(let v=f.length-1;v>=h;--v)f[v][0].length>p&&(h=v+1);let S="",T="",E=!1;for(let v=0;v<x;++v)S+=f[v][0].slice(p)+`
`;for(let v=x;v<h;++v){let[w,I]=f[v];g+=w.length+I.length+1;const A=I[I.length-1]==="\r";if(A&&(I=I.slice(0,-1)),I&&w.length<p){const V=`Block scalar lines must not be less indented than their ${o.indent?"explicit indentation indicator":"first line"}`;r(g-I.length-(A?2:1),"BAD_INDENT",V),w=""}u===ie.BLOCK_LITERAL?(S+=T+w.slice(p)+I,T=`
`):w.length>p||I[0]==="	"?(T===" "?T=`
`:!E&&T===`
`&&(T=`

`),S+=T+w.slice(p)+I,T=`
`,E=!0):I===""?T===`
`?S+=`
`:T=`
`:(S+=T+I,T=" ",E=!1)}switch(o.chomp){case"-":break;case"+":for(let v=h;v<f.length;++v)S+=`
`+f[v][0].slice(p);S[S.length-1]!==`
`&&(S+=`
`);break;default:S+=`
`}const O=l+o.length+n.source.length;return{value:S,type:u,comment:o.comment,range:[l,O,O]}}function Fy({offset:i,props:n},r,l){if(n[0].type!=="block-scalar-header")return l(n[0],"IMPOSSIBLE","Block scalar header not found"),null;const{source:o}=n[0],u=o[0];let f=0,h="",p=-1;for(let T=1;T<o.length;++T){const E=o[T];if(!h&&(E==="-"||E==="+"))h=E;else{const O=Number(E);!f&&O?f=O:p===-1&&(p=i+T)}}p!==-1&&l(p,"UNEXPECTED_TOKEN",`Block scalar header includes extra characters: ${o}`);let g=!1,x="",S=o.length;for(let T=1;T<n.length;++T){const E=n[T];switch(E.type){case"space":g=!0;case"newline":S+=E.source.length;break;case"comment":r&&!g&&l(E,"MISSING_CHAR","Comments must be separated from other tokens by white space characters"),S+=E.source.length,x=E.source.substring(1);break;case"error":l(E,"UNEXPECTED_TOKEN",E.message),S+=E.source.length;break;default:{const O=`Unexpected token in block scalar header: ${E.type}`;l(E,"UNEXPECTED_TOKEN",O);const v=E.source;v&&typeof v=="string"&&(S+=v.length)}}}return{mode:u,indent:f,chomp:h,comment:x,length:S}}function zy(i){const n=i.split(/\n( *)/),r=n[0],l=r.match(/^( *)/),u=[l!=null&&l[1]?[l[1],r.slice(l[1].length)]:["",r]];for(let f=1;f<n.length;f+=2)u.push([n[f],n[f+1]]);return u}function Fh(i,n,r){const{offset:l,type:o,source:u,end:f}=i;let h,p;const g=(T,E,O)=>r(l+T,E,O);switch(o){case"scalar":h=ie.PLAIN,p=Uy(u,g);break;case"single-quoted-scalar":h=ie.QUOTE_SINGLE,p=Vy(u,g);break;case"double-quoted-scalar":h=ie.QUOTE_DOUBLE,p=Ky(u,g);break;default:return r(i,"UNEXPECTED_TOKEN",`Expected a flow scalar value, but found: ${o}`),{value:"",type:null,comment:"",range:[l,l+u.length,l+u.length]}}const x=l+u.length,S=Ms(f,x,n,r);return{value:p,type:h,comment:S.comment,range:[l,x,S.offset]}}function Uy(i,n){let r="";switch(i[0]){case"	":r="a tab character";break;case",":r="flow indicator character ,";break;case"%":r="directive indicator character %";break;case"|":case">":{r=`block scalar indicator ${i[0]}`;break}case"@":case"`":{r=`reserved character ${i[0]}`;break}}return r&&n(0,"BAD_SCALAR_START",`Plain value cannot start with ${r}`),zh(i)}function Vy(i,n){return(i[i.length-1]!=="'"||i.length===1)&&n(i.length,"MISSING_CHAR","Missing closing 'quote"),zh(i.slice(1,-1)).replace(/''/g,"'")}function zh(i){let n,r;try{n=new RegExp(`(.*?)(?<![ 	])[ 	]*\r?
`,"sy"),r=new RegExp(`[ 	]*(.*?)(?:(?<![ 	])[ 	]*)?\r?
`,"sy")}catch{n=/(.*?)[ \t]*\r?\n/sy,r=/[ \t]*(.*?)[ \t]*\r?\n/sy}let l=n.exec(i);if(!l)return i;let o=l[1],u=" ",f=n.lastIndex;for(r.lastIndex=f;l=r.exec(i);)l[1]===""?u===`
`?o+=u:u=`
`:(o+=u+l[1],u=" "),f=r.lastIndex;const h=/[ \t]*(.*)/sy;return h.lastIndex=f,l=h.exec(i),o+u+((l==null?void 0:l[1])??"")}function Ky(i,n){let r="";for(let l=1;l<i.length-1;++l){const o=i[l];if(!(o==="\r"&&i[l+1]===`
`))if(o===`
`){const{fold:u,offset:f}=Wy(i,l);r+=u,l=f}else if(o==="\\"){let u=i[++l];const f=qy[u];if(f)r+=f;else if(u===`
`)for(u=i[l+1];u===" "||u==="	";)u=i[++l+1];else if(u==="\r"&&i[l+1]===`
`)for(u=i[++l+1];u===" "||u==="	";)u=i[++l+1];else if(u==="x"||u==="u"||u==="U"){const h={x:2,u:4,U:8}[u];r+=Hy(i,l+1,h,n),l+=h}else{const h=i.substr(l-1,2);n(l-1,"BAD_DQ_ESCAPE",`Invalid escape sequence ${h}`),r+=h}}else if(o===" "||o==="	"){const u=l;let f=i[l+1];for(;f===" "||f==="	";)f=i[++l+1];f!==`
`&&!(f==="\r"&&i[l+2]===`
`)&&(r+=l>u?i.slice(u,l+1):o)}else r+=o}return(i[i.length-1]!=='"'||i.length===1)&&n(i.length,"MISSING_CHAR",'Missing closing "quote'),r}function Wy(i,n){let r="",l=i[n+1];for(;(l===" "||l==="	"||l===`
`||l==="\r")&&!(l==="\r"&&i[n+2]!==`
`);)l===`
`&&(r+=`
`),n+=1,l=i[n+1];return r||(r=" "),{fold:r,offset:n}}const qy={0:"\0",a:"\x07",b:"\b",e:"\x1B",f:"\f",n:`
`,r:"\r",t:"	",v:"\v",N:"",_:" ",L:"\u2028",P:"\u2029"," ":" ",'"':'"',"/":"/","\\":"\\","	":"	"};function Hy(i,n,r,l){const o=i.substr(n,r),f=o.length===r&&/^[0-9a-fA-F]+$/.test(o)?parseInt(o,16):NaN;if(isNaN(f)){const h=i.substr(n-2,r+2);return l(n-2,"BAD_DQ_ESCAPE",`Invalid escape sequence ${h}`),h}return String.fromCodePoint(f)}function Uh(i,n,r,l){const{value:o,type:u,comment:f,range:h}=n.type==="block-scalar"?Bh(i,n,l):Fh(n,i.options.strict,l),p=r?i.directives.tagName(r.source,S=>l(r,"TAG_RESOLVE_FAILED",S)):null;let g;i.options.stringKeys&&i.atKey?g=i.schema[Vt]:p?g=Qy(i.schema,o,p,r,l):n.type==="scalar"?g=Jy(i,o,n,l):g=i.schema[Vt];let x;try{const S=g.resolve(o,T=>l(r??n,"TAG_RESOLVE_FAILED",T),i.options);x=ve(S)?S:new ie(S)}catch(S){const T=S instanceof Error?S.message:String(S);l(r??n,"TAG_RESOLVE_FAILED",T),x=new ie(o)}return x.range=h,x.source=o,u&&(x.type=u),p&&(x.tag=p),g.format&&(x.format=g.format),f&&(x.comment=f),x}function Qy(i,n,r,l,o){var h;if(r==="!")return i[Vt];const u=[];for(const p of i.tags)if(!p.collection&&p.tag===r)if(p.default&&p.test)u.push(p);else return p;for(const p of u)if((h=p.test)!=null&&h.test(n))return p;const f=i.knownTags[r];return f&&!f.collection?(i.tags.push(Object.assign({},f,{default:!1,test:void 0})),f):(o(l,"TAG_RESOLVE_FAILED",`Unresolved tag: ${r}`,r!=="tag:yaml.org,2002:str"),i[Vt])}function Jy({atKey:i,directives:n,schema:r},l,o,u){const f=r.tags.find(h=>{var p;return(h.default===!0||i&&h.default==="key")&&((p=h.test)==null?void 0:p.test(l))})||r[Vt];if(r.compat){const h=r.compat.find(p=>{var g;return p.default&&((g=p.test)==null?void 0:g.test(l))})??r[Vt];if(f.tag!==h.tag){const p=n.tagString(f.tag),g=n.tagString(h.tag),x=`Value may be parsed as either ${p} or ${g}`;u(o,"TAG_RESOLVE_FAILED",x,!0)}}return f}function Yy(i,n,r){if(n){r===null&&(r=n.length);for(let l=r-1;l>=0;--l){let o=n[l];switch(o.type){case"space":case"comment":case"newline":i-=o.source.length;continue}for(o=n[++l];(o==null?void 0:o.type)==="space";)i+=o.source.length,o=n[++l];break}}return i}const Gy={composeNode:Vh,composeEmptyNode:lu};function Vh(i,n,r,l){const o=i.atKey,{spaceBefore:u,comment:f,anchor:h,tag:p}=r;let g,x=!0;switch(n.type){case"alias":g=Xy(i,n,l),(h||p)&&l(n,"ALIAS_PROPS","An alias node must not specify any properties");break;case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"block-scalar":g=Uh(i,n,p,l),h&&(g.anchor=h.source.substring(1));break;case"block-map":case"block-seq":case"flow-collection":g=By(Gy,i,n,r,l),h&&(g.anchor=h.source.substring(1));break;default:{const S=n.type==="error"?n.message:`Unsupported token (type: ${n.type})`;l(n,"UNEXPECTED_TOKEN",S),g=lu(i,n.offset,void 0,null,r,l),x=!1}}return h&&g.anchor===""&&l(h,"BAD_ALIAS","Anchor cannot be an empty string"),o&&i.options.stringKeys&&(!ve(g)||typeof g.value!="string"||g.tag&&g.tag!=="tag:yaml.org,2002:str")&&l(p??n,"NON_STRING_KEY","With stringKeys, all keys must be strings"),u&&(g.spaceBefore=!0),f&&(n.type==="scalar"&&n.source===""?g.comment=f:g.commentBefore=f),i.options.keepSourceTokens&&x&&(g.srcToken=n),g}function lu(i,n,r,l,{spaceBefore:o,comment:u,anchor:f,tag:h,end:p},g){const x={type:"scalar",offset:Yy(n,r,l),indent:-1,source:""},S=Uh(i,x,h,g);return f&&(S.anchor=f.source.substring(1),S.anchor===""&&g(f,"BAD_ALIAS","Anchor cannot be an empty string")),o&&(S.spaceBefore=!0),u&&(S.comment=u,S.range[2]=p),S}function Xy({options:i},{offset:n,source:r,end:l},o){const u=new ml(r.substring(1));u.source===""&&o(n,"BAD_ALIAS","Alias cannot be an empty string"),u.source.endsWith(":")&&o(n+r.length-1,"BAD_ALIAS","Alias ending in : is ambiguous",!0);const f=n+r.length,h=Ms(l,f,i.strict,o);return u.range=[n,f,h.offset],h.comment&&(u.comment=h.comment),u}function Zy(i,n,{offset:r,start:l,value:o,end:u},f){const h=Object.assign({_directives:n},i),p=new Dr(void 0,h),g={atKey:!1,atRoot:!0,directives:p.directives,options:p.options,schema:p.schema},x=Or(l,{indicator:"doc-start",next:o??(u==null?void 0:u[0]),offset:r,onError:f,parentIndent:0,startOnNewline:!0});x.found&&(p.directives.docStart=!0,o&&(o.type==="block-map"||o.type==="block-seq")&&!x.hasNewline&&f(x.end,"MISSING_CHAR","Block collection cannot start on same line with directives-end marker")),p.contents=o?Vh(g,o,x,f):lu(g,x.end,l,null,x,f);const S=p.contents.range[2],T=Ms(u,S,!1,f);return T.comment&&(p.comment=T.comment),p.range=[r,S,T.offset],p}function Ts(i){if(typeof i=="number")return[i,i+1];if(Array.isArray(i))return i.length===2?i:[i[0],i[1]];const{offset:n,source:r}=i;return[n,n+(typeof r=="string"?r.length:1)]}function $d(i){var o;let n="",r=!1,l=!1;for(let u=0;u<i.length;++u){const f=i[u];switch(f[0]){case"#":n+=(n===""?"":l?`

`:`
`)+(f.substring(1)||" "),r=!0,l=!1;break;case"%":((o=i[u+1])==null?void 0:o[0])!=="#"&&(u+=1),r=!1;break;default:r||(l=!0),r=!1}}return{comment:n,afterEmptyLine:l}}class ou{constructor(n={}){this.doc=null,this.atDirectives=!1,this.prelude=[],this.errors=[],this.warnings=[],this.onError=(r,l,o,u)=>{const f=Ts(r);u?this.warnings.push(new Dh(f,l,o)):this.errors.push(new zn(f,l,o))},this.directives=new nt({version:n.version||"1.2"}),this.options=n}decorate(n,r){const{comment:l,afterEmptyLine:o}=$d(this.prelude);if(l){const u=n.contents;if(r)n.comment=n.comment?`${n.comment}
${l}`:l;else if(o||n.directives.docStart||!u)n.commentBefore=l;else if($e(u)&&!u.flow&&u.items.length>0){let f=u.items[0];_e(f)&&(f=f.key);const h=f.commentBefore;f.commentBefore=h?`${l}
${h}`:l}else{const f=u.commentBefore;u.commentBefore=f?`${l}
${f}`:l}}r?(Array.prototype.push.apply(n.errors,this.errors),Array.prototype.push.apply(n.warnings,this.warnings)):(n.errors=this.errors,n.warnings=this.warnings),this.prelude=[],this.errors=[],this.warnings=[]}streamInfo(){return{comment:$d(this.prelude).comment,directives:this.directives,errors:this.errors,warnings:this.warnings}}*compose(n,r=!1,l=-1){for(const o of n)yield*this.next(o);yield*this.end(r,l)}*next(n){switch(n.type){case"directive":this.directives.add(n.source,(r,l,o)=>{const u=Ts(n);u[0]+=r,this.onError(u,"BAD_DIRECTIVE",l,o)}),this.prelude.push(n.source),this.atDirectives=!0;break;case"document":{const r=Zy(this.options,this.directives,n,this.onError);this.atDirectives&&!r.directives.docStart&&this.onError(n,"MISSING_CHAR","Missing directives-end/doc-start indicator line"),this.decorate(r,!1),this.doc&&(yield this.doc),this.doc=r,this.atDirectives=!1;break}case"byte-order-mark":case"space":break;case"comment":case"newline":this.prelude.push(n.source);break;case"error":{const r=n.source?`${n.message}: ${JSON.stringify(n.source)}`:n.message,l=new zn(Ts(n),"UNEXPECTED_TOKEN",r);this.atDirectives||!this.doc?this.errors.push(l):this.doc.errors.push(l);break}case"doc-end":{if(!this.doc){const l="Unexpected doc-end without preceding document";this.errors.push(new zn(Ts(n),"UNEXPECTED_TOKEN",l));break}this.doc.directives.docEnd=!0;const r=Ms(n.end,n.offset+n.source.length,this.doc.options.strict,this.onError);if(this.decorate(this.doc,!0),r.comment){const l=this.doc.comment;this.doc.comment=l?`${l}
${r.comment}`:r.comment}this.doc.range[2]=r.offset;break}default:this.errors.push(new zn(Ts(n),"UNEXPECTED_TOKEN",`Unsupported token ${n.type}`))}}*end(n=!1,r=-1){if(this.doc)this.decorate(this.doc,!0),yield this.doc,this.doc=null;else if(n){const l=Object.assign({_directives:this.directives},this.options),o=new Dr(void 0,l);this.atDirectives&&this.onError(r,"MISSING_CHAR","Missing directives-end indicator line"),o.range=[0,r,r],this.decorate(o,!1),yield o}}}function e0(i,n=!0,r){if(i){const l=(o,u,f)=>{const h=typeof o=="number"?o:Array.isArray(o)?o[0]:o.offset;if(r)r(h,u,f);else throw new zn([h,h+1],u,f)};switch(i.type){case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return Fh(i,n,l);case"block-scalar":return Bh({options:{strict:n}},i,l)}}return null}function t0(i,n){const{implicitKey:r=!1,indent:l,inFlow:o=!1,offset:u=-1,type:f="PLAIN"}=n,h=As({type:f,value:i},{implicitKey:r,indent:l>0?" ".repeat(l):"",inFlow:o,options:{blockQuote:!0,lineWidth:-1}}),p=n.end??[{type:"newline",offset:-1,indent:l,source:`
`}];switch(h[0]){case"|":case">":{const g=h.indexOf(`
`),x=h.substring(0,g),S=h.substring(g+1)+`
`,T=[{type:"block-scalar-header",offset:u,indent:l,source:x}];return Kh(T,p)||T.push({type:"newline",offset:-1,indent:l,source:`
`}),{type:"block-scalar",offset:u,indent:l,props:T,source:S}}case'"':return{type:"double-quoted-scalar",offset:u,indent:l,source:h,end:p};case"'":return{type:"single-quoted-scalar",offset:u,indent:l,source:h,end:p};default:return{type:"scalar",offset:u,indent:l,source:h,end:p}}}function n0(i,n,r={}){let{afterKey:l=!1,implicitKey:o=!1,inFlow:u=!1,type:f}=r,h="indent"in i?i.indent:null;if(l&&typeof h=="number"&&(h+=2),!f)switch(i.type){case"single-quoted-scalar":f="QUOTE_SINGLE";break;case"double-quoted-scalar":f="QUOTE_DOUBLE";break;case"block-scalar":{const g=i.props[0];if(g.type!=="block-scalar-header")throw new Error("Invalid block scalar header");f=g.source[0]===">"?"BLOCK_FOLDED":"BLOCK_LITERAL";break}default:f="PLAIN"}const p=As({type:f,value:n},{implicitKey:o||h===null,indent:h!==null&&h>0?" ".repeat(h):"",inFlow:u,options:{blockQuote:!0,lineWidth:-1}});switch(p[0]){case"|":case">":r0(i,p);break;case'"':Ma(i,p,"double-quoted-scalar");break;case"'":Ma(i,p,"single-quoted-scalar");break;default:Ma(i,p,"scalar")}}function r0(i,n){const r=n.indexOf(`
`),l=n.substring(0,r),o=n.substring(r+1)+`
`;if(i.type==="block-scalar"){const u=i.props[0];if(u.type!=="block-scalar-header")throw new Error("Invalid block scalar header");u.source=l,i.source=o}else{const{offset:u}=i,f="indent"in i?i.indent:-1,h=[{type:"block-scalar-header",offset:u,indent:f,source:l}];Kh(h,"end"in i?i.end:void 0)||h.push({type:"newline",offset:-1,indent:f,source:`
`});for(const p of Object.keys(i))p!=="type"&&p!=="offset"&&delete i[p];Object.assign(i,{type:"block-scalar",indent:f,props:h,source:o})}}function Kh(i,n){if(n)for(const r of n)switch(r.type){case"space":case"comment":i.push(r);break;case"newline":return i.push(r),!0}return!1}function Ma(i,n,r){switch(i.type){case"scalar":case"double-quoted-scalar":case"single-quoted-scalar":i.type=r,i.source=n;break;case"block-scalar":{const l=i.props.slice(1);let o=n.length;i.props[0].type==="block-scalar-header"&&(o-=i.props[0].source.length);for(const u of l)u.offset+=o;delete i.props,Object.assign(i,{type:r,source:n,end:l});break}case"block-map":case"block-seq":{const o={type:"newline",offset:i.offset+n.length,indent:i.indent,source:`
`};delete i.items,Object.assign(i,{type:r,source:n,end:[o]});break}default:{const l="indent"in i?i.indent:-1,o="end"in i&&Array.isArray(i.end)?i.end.filter(u=>u.type==="space"||u.type==="comment"||u.type==="newline"):[];for(const u of Object.keys(i))u!=="type"&&u!=="offset"&&delete i[u];Object.assign(i,{type:r,indent:l,source:n,end:o})}}}const s0=i=>"type"in i?cl(i):il(i);function cl(i){switch(i.type){case"block-scalar":{let n="";for(const r of i.props)n+=cl(r);return n+i.source}case"block-map":case"block-seq":{let n="";for(const r of i.items)n+=il(r);return n}case"flow-collection":{let n=i.start.source;for(const r of i.items)n+=il(r);for(const r of i.end)n+=r.source;return n}case"document":{let n=il(i);if(i.end)for(const r of i.end)n+=r.source;return n}default:{let n=i.source;if("end"in i&&i.end)for(const r of i.end)n+=r.source;return n}}}function il({start:i,key:n,sep:r,value:l}){let o="";for(const u of i)o+=u.source;if(n&&(o+=cl(n)),r)for(const u of r)o+=u.source;return l&&(o+=cl(l)),o}const Wa=Symbol("break visit"),i0=Symbol("skip children"),Wh=Symbol("remove item");function Vn(i,n){"type"in i&&i.type==="document"&&(i={start:i.start,value:i.value}),qh(Object.freeze([]),i,n)}Vn.BREAK=Wa;Vn.SKIP=i0;Vn.REMOVE=Wh;Vn.itemAtPath=(i,n)=>{let r=i;for(const[l,o]of n){const u=r==null?void 0:r[l];if(u&&"items"in u)r=u.items[o];else return}return r};Vn.parentCollection=(i,n)=>{const r=Vn.itemAtPath(i,n.slice(0,-1)),l=n[n.length-1][0],o=r==null?void 0:r[l];if(o&&"items"in o)return o;throw new Error("Parent collection not found")};function qh(i,n,r){let l=r(n,i);if(typeof l=="symbol")return l;for(const o of["key","value"]){const u=n[o];if(u&&"items"in u){for(let f=0;f<u.items.length;++f){const h=qh(Object.freeze(i.concat([[o,f]])),u.items[f],r);if(typeof h=="number")f=h-1;else{if(h===Wa)return Wa;h===Wh&&(u.items.splice(f,1),f-=1)}}typeof l=="function"&&o==="key"&&(l=l(n,i))}}return typeof l=="function"?l(n,i):l}const Tl="\uFEFF",Cl="",_l="",$s="",l0=i=>!!i&&"items"in i,o0=i=>!!i&&(i.type==="scalar"||i.type==="single-quoted-scalar"||i.type==="double-quoted-scalar"||i.type==="block-scalar");function a0(i){switch(i){case Tl:return"<BOM>";case Cl:return"<DOC>";case _l:return"<FLOW_END>";case $s:return"<SCALAR>";default:return JSON.stringify(i)}}function Hh(i){switch(i){case Tl:return"byte-order-mark";case Cl:return"doc-mode";case _l:return"flow-error-end";case $s:return"scalar";case"---":return"doc-start";case"...":return"doc-end";case"":case`
`:case`\r
`:return"newline";case"-":return"seq-item-ind";case"?":return"explicit-key-ind";case":":return"map-value-ind";case"{":return"flow-map-start";case"}":return"flow-map-end";case"[":return"flow-seq-start";case"]":return"flow-seq-end";case",":return"comma"}switch(i[0]){case" ":case"	":return"space";case"#":return"comment";case"%":return"directive-line";case"*":return"alias";case"&":return"anchor";case"!":return"tag";case"'":return"single-quoted-scalar";case'"':return"double-quoted-scalar";case"|":case">":return"block-scalar-header"}return null}const u0=Object.freeze(Object.defineProperty({__proto__:null,BOM:Tl,DOCUMENT:Cl,FLOW_END:_l,SCALAR:$s,createScalarToken:t0,isCollection:l0,isScalar:o0,prettyToken:a0,resolveAsScalar:e0,setScalarValue:n0,stringify:s0,tokenType:Hh,visit:Vn},Symbol.toStringTag,{value:"Module"}));function jt(i){switch(i){case void 0:case" ":case`
`:case"\r":case"	":return!0;default:return!1}}const Id=new Set("0123456789ABCDEFabcdef"),c0=new Set("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-#;/?:@&=+$_.!~*'()"),Ji=new Set(",[]{}"),f0=new Set(` ,[]{}
\r	`),ja=i=>!i||f0.has(i);class Qh{constructor(){this.atEnd=!1,this.blockScalarIndent=-1,this.blockScalarKeep=!1,this.buffer="",this.flowKey=!1,this.flowLevel=0,this.indentNext=0,this.indentValue=0,this.lineEndPos=null,this.next=null,this.pos=0}*lex(n,r=!1){if(n){if(typeof n!="string")throw TypeError("source is not a string");this.buffer=this.buffer?this.buffer+n:n,this.lineEndPos=null}this.atEnd=!r;let l=this.next??"stream";for(;l&&(r||this.hasChars(1));)l=yield*this.parseNext(l)}atLineEnd(){let n=this.pos,r=this.buffer[n];for(;r===" "||r==="	";)r=this.buffer[++n];return!r||r==="#"||r===`
`?!0:r==="\r"?this.buffer[n+1]===`
`:!1}charAt(n){return this.buffer[this.pos+n]}continueScalar(n){let r=this.buffer[n];if(this.indentNext>0){let l=0;for(;r===" ";)r=this.buffer[++l+n];if(r==="\r"){const o=this.buffer[l+n+1];if(o===`
`||!o&&!this.atEnd)return n+l+1}return r===`
`||l>=this.indentNext||!r&&!this.atEnd?n+l:-1}if(r==="-"||r==="."){const l=this.buffer.substr(n,3);if((l==="---"||l==="...")&&jt(this.buffer[n+3]))return-1}return n}getLine(){let n=this.lineEndPos;return(typeof n!="number"||n!==-1&&n<this.pos)&&(n=this.buffer.indexOf(`
`,this.pos),this.lineEndPos=n),n===-1?this.atEnd?this.buffer.substring(this.pos):null:(this.buffer[n-1]==="\r"&&(n-=1),this.buffer.substring(this.pos,n))}hasChars(n){return this.pos+n<=this.buffer.length}setNext(n){return this.buffer=this.buffer.substring(this.pos),this.pos=0,this.lineEndPos=null,this.next=n,null}peek(n){return this.buffer.substr(this.pos,n)}*parseNext(n){switch(n){case"stream":return yield*this.parseStream();case"line-start":return yield*this.parseLineStart();case"block-start":return yield*this.parseBlockStart();case"doc":return yield*this.parseDocument();case"flow":return yield*this.parseFlowCollection();case"quoted-scalar":return yield*this.parseQuotedScalar();case"block-scalar":return yield*this.parseBlockScalar();case"plain-scalar":return yield*this.parsePlainScalar()}}*parseStream(){let n=this.getLine();if(n===null)return this.setNext("stream");if(n[0]===Tl&&(yield*this.pushCount(1),n=n.substring(1)),n[0]==="%"){let r=n.length,l=n.indexOf("#");for(;l!==-1;){const u=n[l-1];if(u===" "||u==="	"){r=l-1;break}else l=n.indexOf("#",l+1)}for(;;){const u=n[r-1];if(u===" "||u==="	")r-=1;else break}const o=(yield*this.pushCount(r))+(yield*this.pushSpaces(!0));return yield*this.pushCount(n.length-o),this.pushNewline(),"stream"}if(this.atLineEnd()){const r=yield*this.pushSpaces(!0);return yield*this.pushCount(n.length-r),yield*this.pushNewline(),"stream"}return yield Cl,yield*this.parseLineStart()}*parseLineStart(){const n=this.charAt(0);if(!n&&!this.atEnd)return this.setNext("line-start");if(n==="-"||n==="."){if(!this.atEnd&&!this.hasChars(4))return this.setNext("line-start");const r=this.peek(3);if((r==="---"||r==="...")&&jt(this.charAt(3)))return yield*this.pushCount(3),this.indentValue=0,this.indentNext=0,r==="---"?"doc":"stream"}return this.indentValue=yield*this.pushSpaces(!1),this.indentNext>this.indentValue&&!jt(this.charAt(1))&&(this.indentNext=this.indentValue),yield*this.parseBlockStart()}*parseBlockStart(){const[n,r]=this.peek(2);if(!r&&!this.atEnd)return this.setNext("block-start");if((n==="-"||n==="?"||n===":")&&jt(r)){const l=(yield*this.pushCount(1))+(yield*this.pushSpaces(!0));return this.indentNext=this.indentValue+1,this.indentValue+=l,yield*this.parseBlockStart()}return"doc"}*parseDocument(){yield*this.pushSpaces(!0);const n=this.getLine();if(n===null)return this.setNext("doc");let r=yield*this.pushIndicators();switch(n[r]){case"#":yield*this.pushCount(n.length-r);case void 0:return yield*this.pushNewline(),yield*this.parseLineStart();case"{":case"[":return yield*this.pushCount(1),this.flowKey=!1,this.flowLevel=1,"flow";case"}":case"]":return yield*this.pushCount(1),"doc";case"*":return yield*this.pushUntil(ja),"doc";case'"':case"'":return yield*this.parseQuotedScalar();case"|":case">":return r+=yield*this.parseBlockScalarHeader(),r+=yield*this.pushSpaces(!0),yield*this.pushCount(n.length-r),yield*this.pushNewline(),yield*this.parseBlockScalar();default:return yield*this.parsePlainScalar()}}*parseFlowCollection(){let n,r,l=-1;do n=yield*this.pushNewline(),n>0?(r=yield*this.pushSpaces(!1),this.indentValue=l=r):r=0,r+=yield*this.pushSpaces(!0);while(n+r>0);const o=this.getLine();if(o===null)return this.setNext("flow");if((l!==-1&&l<this.indentNext&&o[0]!=="#"||l===0&&(o.startsWith("---")||o.startsWith("..."))&&jt(o[3]))&&!(l===this.indentNext-1&&this.flowLevel===1&&(o[0]==="]"||o[0]==="}")))return this.flowLevel=0,yield _l,yield*this.parseLineStart();let u=0;for(;o[u]===",";)u+=yield*this.pushCount(1),u+=yield*this.pushSpaces(!0),this.flowKey=!1;switch(u+=yield*this.pushIndicators(),o[u]){case void 0:return"flow";case"#":return yield*this.pushCount(o.length-u),"flow";case"{":case"[":return yield*this.pushCount(1),this.flowKey=!1,this.flowLevel+=1,"flow";case"}":case"]":return yield*this.pushCount(1),this.flowKey=!0,this.flowLevel-=1,this.flowLevel?"flow":"doc";case"*":return yield*this.pushUntil(ja),"flow";case'"':case"'":return this.flowKey=!0,yield*this.parseQuotedScalar();case":":{const f=this.charAt(1);if(this.flowKey||jt(f)||f===",")return this.flowKey=!1,yield*this.pushCount(1),yield*this.pushSpaces(!0),"flow"}default:return this.flowKey=!1,yield*this.parsePlainScalar()}}*parseQuotedScalar(){const n=this.charAt(0);let r=this.buffer.indexOf(n,this.pos+1);if(n==="'")for(;r!==-1&&this.buffer[r+1]==="'";)r=this.buffer.indexOf("'",r+2);else for(;r!==-1;){let u=0;for(;this.buffer[r-1-u]==="\\";)u+=1;if(u%2===0)break;r=this.buffer.indexOf('"',r+1)}const l=this.buffer.substring(0,r);let o=l.indexOf(`
`,this.pos);if(o!==-1){for(;o!==-1;){const u=this.continueScalar(o+1);if(u===-1)break;o=l.indexOf(`
`,u)}o!==-1&&(r=o-(l[o-1]==="\r"?2:1))}if(r===-1){if(!this.atEnd)return this.setNext("quoted-scalar");r=this.buffer.length}return yield*this.pushToIndex(r+1,!1),this.flowLevel?"flow":"doc"}*parseBlockScalarHeader(){this.blockScalarIndent=-1,this.blockScalarKeep=!1;let n=this.pos;for(;;){const r=this.buffer[++n];if(r==="+")this.blockScalarKeep=!0;else if(r>"0"&&r<="9")this.blockScalarIndent=Number(r)-1;else if(r!=="-")break}return yield*this.pushUntil(r=>jt(r)||r==="#")}*parseBlockScalar(){let n=this.pos-1,r=0,l;e:for(let u=this.pos;l=this.buffer[u];++u)switch(l){case" ":r+=1;break;case`
`:n=u,r=0;break;case"\r":{const f=this.buffer[u+1];if(!f&&!this.atEnd)return this.setNext("block-scalar");if(f===`
`)break}default:break e}if(!l&&!this.atEnd)return this.setNext("block-scalar");if(r>=this.indentNext){this.blockScalarIndent===-1?this.indentNext=r:this.indentNext=this.blockScalarIndent+(this.indentNext===0?1:this.indentNext);do{const u=this.continueScalar(n+1);if(u===-1)break;n=this.buffer.indexOf(`
`,u)}while(n!==-1);if(n===-1){if(!this.atEnd)return this.setNext("block-scalar");n=this.buffer.length}}let o=n+1;for(l=this.buffer[o];l===" ";)l=this.buffer[++o];if(l==="	"){for(;l==="	"||l===" "||l==="\r"||l===`
`;)l=this.buffer[++o];n=o-1}else if(!this.blockScalarKeep)do{let u=n-1,f=this.buffer[u];f==="\r"&&(f=this.buffer[--u]);const h=u;for(;f===" ";)f=this.buffer[--u];if(f===`
`&&u>=this.pos&&u+1+r>h)n=u;else break}while(!0);return yield $s,yield*this.pushToIndex(n+1,!0),yield*this.parseLineStart()}*parsePlainScalar(){const n=this.flowLevel>0;let r=this.pos-1,l=this.pos-1,o;for(;o=this.buffer[++l];)if(o===":"){const u=this.buffer[l+1];if(jt(u)||n&&Ji.has(u))break;r=l}else if(jt(o)){let u=this.buffer[l+1];if(o==="\r"&&(u===`
`?(l+=1,o=`
`,u=this.buffer[l+1]):r=l),u==="#"||n&&Ji.has(u))break;if(o===`
`){const f=this.continueScalar(l+1);if(f===-1)break;l=Math.max(l,f-2)}}else{if(n&&Ji.has(o))break;r=l}return!o&&!this.atEnd?this.setNext("plain-scalar"):(yield $s,yield*this.pushToIndex(r+1,!0),n?"flow":"doc")}*pushCount(n){return n>0?(yield this.buffer.substr(this.pos,n),this.pos+=n,n):0}*pushToIndex(n,r){const l=this.buffer.slice(this.pos,n);return l?(yield l,this.pos+=l.length,l.length):(r&&(yield""),0)}*pushIndicators(){switch(this.charAt(0)){case"!":return(yield*this.pushTag())+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators());case"&":return(yield*this.pushUntil(ja))+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators());case"-":case"?":case":":{const n=this.flowLevel>0,r=this.charAt(1);if(jt(r)||n&&Ji.has(r))return n?this.flowKey&&(this.flowKey=!1):this.indentNext=this.indentValue+1,(yield*this.pushCount(1))+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators())}}return 0}*pushTag(){if(this.charAt(1)==="<"){let n=this.pos+2,r=this.buffer[n];for(;!jt(r)&&r!==">";)r=this.buffer[++n];return yield*this.pushToIndex(r===">"?n+1:n,!1)}else{let n=this.pos+1,r=this.buffer[n];for(;r;)if(c0.has(r))r=this.buffer[++n];else if(r==="%"&&Id.has(this.buffer[n+1])&&Id.has(this.buffer[n+2]))r=this.buffer[n+=3];else break;return yield*this.pushToIndex(n,!1)}}*pushNewline(){const n=this.buffer[this.pos];return n===`
`?yield*this.pushCount(1):n==="\r"&&this.charAt(1)===`
`?yield*this.pushCount(2):0}*pushSpaces(n){let r=this.pos-1,l;do l=this.buffer[++r];while(l===" "||n&&l==="	");const o=r-this.pos;return o>0&&(yield this.buffer.substr(this.pos,o),this.pos=r),o}*pushUntil(n){let r=this.pos,l=this.buffer[r];for(;!n(l);)l=this.buffer[++r];return yield*this.pushToIndex(r,!1)}}class Jh{constructor(){this.lineStarts=[],this.addNewLine=n=>this.lineStarts.push(n),this.linePos=n=>{let r=0,l=this.lineStarts.length;for(;r<l;){const u=r+l>>1;this.lineStarts[u]<n?r=u+1:l=u}if(this.lineStarts[r]===n)return{line:r+1,col:1};if(r===0)return{line:0,col:n};const o=this.lineStarts[r-1];return{line:r,col:n-o+1}}}}function bn(i,n){for(let r=0;r<i.length;++r)if(i[r].type===n)return!0;return!1}function Ad(i){for(let n=0;n<i.length;++n)switch(i[n].type){case"space":case"comment":case"newline":break;default:return n}return-1}function Yh(i){switch(i==null?void 0:i.type){case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"flow-collection":return!0;default:return!1}}function Yi(i){switch(i.type){case"document":return i.start;case"block-map":{const n=i.items[i.items.length-1];return n.sep??n.start}case"block-seq":return i.items[i.items.length-1].start;default:return[]}}function vr(i){var r;if(i.length===0)return[];let n=i.length;e:for(;--n>=0;)switch(i[n].type){case"doc-start":case"explicit-key-ind":case"map-value-ind":case"seq-item-ind":case"newline":break e}for(;((r=i[++n])==null?void 0:r.type)==="space";);return i.splice(n,i.length)}function Pd(i){if(i.start.type==="flow-seq-start")for(const n of i.items)n.sep&&!n.value&&!bn(n.start,"explicit-key-ind")&&!bn(n.sep,"map-value-ind")&&(n.key&&(n.value=n.key),delete n.key,Yh(n.value)?n.value.end?Array.prototype.push.apply(n.value.end,n.sep):n.value.end=n.sep:Array.prototype.push.apply(n.start,n.sep),delete n.sep)}class au{constructor(n){this.atNewLine=!0,this.atScalar=!1,this.indent=0,this.offset=0,this.onKeyLine=!1,this.stack=[],this.source="",this.type="",this.lexer=new Qh,this.onNewLine=n}*parse(n,r=!1){this.onNewLine&&this.offset===0&&this.onNewLine(0);for(const l of this.lexer.lex(n,r))yield*this.next(l);r||(yield*this.end())}*next(n){if(this.source=n,this.atScalar){this.atScalar=!1,yield*this.step(),this.offset+=n.length;return}const r=Hh(n);if(r)if(r==="scalar")this.atNewLine=!1,this.atScalar=!0,this.type="scalar";else{switch(this.type=r,yield*this.step(),r){case"newline":this.atNewLine=!0,this.indent=0,this.onNewLine&&this.onNewLine(this.offset+n.length);break;case"space":this.atNewLine&&n[0]===" "&&(this.indent+=n.length);break;case"explicit-key-ind":case"map-value-ind":case"seq-item-ind":this.atNewLine&&(this.indent+=n.length);break;case"doc-mode":case"flow-error-end":return;default:this.atNewLine=!1}this.offset+=n.length}else{const l=`Not a YAML token: ${n}`;yield*this.pop({type:"error",offset:this.offset,message:l,source:n}),this.offset+=n.length}}*end(){for(;this.stack.length>0;)yield*this.pop()}get sourceToken(){return{type:this.type,offset:this.offset,indent:this.indent,source:this.source}}*step(){const n=this.peek(1);if(this.type==="doc-end"&&(!n||n.type!=="doc-end")){for(;this.stack.length>0;)yield*this.pop();this.stack.push({type:"doc-end",offset:this.offset,source:this.source});return}if(!n)return yield*this.stream();switch(n.type){case"document":return yield*this.document(n);case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return yield*this.scalar(n);case"block-scalar":return yield*this.blockScalar(n);case"block-map":return yield*this.blockMap(n);case"block-seq":return yield*this.blockSequence(n);case"flow-collection":return yield*this.flowCollection(n);case"doc-end":return yield*this.documentEnd(n)}yield*this.pop()}peek(n){return this.stack[this.stack.length-n]}*pop(n){const r=n??this.stack.pop();if(!r)yield{type:"error",offset:this.offset,source:"",message:"Tried to pop an empty stack"};else if(this.stack.length===0)yield r;else{const l=this.peek(1);switch(r.type==="block-scalar"?r.indent="indent"in l?l.indent:0:r.type==="flow-collection"&&l.type==="document"&&(r.indent=0),r.type==="flow-collection"&&Pd(r),l.type){case"document":l.value=r;break;case"block-scalar":l.props.push(r);break;case"block-map":{const o=l.items[l.items.length-1];if(o.value){l.items.push({start:[],key:r,sep:[]}),this.onKeyLine=!0;return}else if(o.sep)o.value=r;else{Object.assign(o,{key:r,sep:[]}),this.onKeyLine=!o.explicitKey;return}break}case"block-seq":{const o=l.items[l.items.length-1];o.value?l.items.push({start:[],value:r}):o.value=r;break}case"flow-collection":{const o=l.items[l.items.length-1];!o||o.value?l.items.push({start:[],key:r,sep:[]}):o.sep?o.value=r:Object.assign(o,{key:r,sep:[]});return}default:yield*this.pop(),yield*this.pop(r)}if((l.type==="document"||l.type==="block-map"||l.type==="block-seq")&&(r.type==="block-map"||r.type==="block-seq")){const o=r.items[r.items.length-1];o&&!o.sep&&!o.value&&o.start.length>0&&Ad(o.start)===-1&&(r.indent===0||o.start.every(u=>u.type!=="comment"||u.indent<r.indent))&&(l.type==="document"?l.end=o.start:l.items.push({start:o.start}),r.items.splice(-1,1))}}}*stream(){switch(this.type){case"directive-line":yield{type:"directive",offset:this.offset,source:this.source};return;case"byte-order-mark":case"space":case"comment":case"newline":yield this.sourceToken;return;case"doc-mode":case"doc-start":{const n={type:"document",offset:this.offset,start:[]};this.type==="doc-start"&&n.start.push(this.sourceToken),this.stack.push(n);return}}yield{type:"error",offset:this.offset,message:`Unexpected ${this.type} token in YAML stream`,source:this.source}}*document(n){if(n.value)return yield*this.lineEnd(n);switch(this.type){case"doc-start":{Ad(n.start)!==-1?(yield*this.pop(),yield*this.step()):n.start.push(this.sourceToken);return}case"anchor":case"tag":case"space":case"comment":case"newline":n.start.push(this.sourceToken);return}const r=this.startBlockValue(n);r?this.stack.push(r):yield{type:"error",offset:this.offset,message:`Unexpected ${this.type} token in YAML document`,source:this.source}}*scalar(n){if(this.type==="map-value-ind"){const r=Yi(this.peek(2)),l=vr(r);let o;n.end?(o=n.end,o.push(this.sourceToken),delete n.end):o=[this.sourceToken];const u={type:"block-map",offset:n.offset,indent:n.indent,items:[{start:l,key:n,sep:o}]};this.onKeyLine=!0,this.stack[this.stack.length-1]=u}else yield*this.lineEnd(n)}*blockScalar(n){switch(this.type){case"space":case"comment":case"newline":n.props.push(this.sourceToken);return;case"scalar":if(n.source=this.source,this.atNewLine=!0,this.indent=0,this.onNewLine){let r=this.source.indexOf(`
`)+1;for(;r!==0;)this.onNewLine(this.offset+r),r=this.source.indexOf(`
`,r)+1}yield*this.pop();break;default:yield*this.pop(),yield*this.step()}}*blockMap(n){var l;const r=n.items[n.items.length-1];switch(this.type){case"newline":if(this.onKeyLine=!1,r.value){const o="end"in r.value?r.value.end:void 0,u=Array.isArray(o)?o[o.length-1]:void 0;(u==null?void 0:u.type)==="comment"?o==null||o.push(this.sourceToken):n.items.push({start:[this.sourceToken]})}else r.sep?r.sep.push(this.sourceToken):r.start.push(this.sourceToken);return;case"space":case"comment":if(r.value)n.items.push({start:[this.sourceToken]});else if(r.sep)r.sep.push(this.sourceToken);else{if(this.atIndentedComment(r.start,n.indent)){const o=n.items[n.items.length-2],u=(l=o==null?void 0:o.value)==null?void 0:l.end;if(Array.isArray(u)){Array.prototype.push.apply(u,r.start),u.push(this.sourceToken),n.items.pop();return}}r.start.push(this.sourceToken)}return}if(this.indent>=n.indent){const o=!this.onKeyLine&&this.indent===n.indent,u=o&&(r.sep||r.explicitKey)&&this.type!=="seq-item-ind";let f=[];if(u&&r.sep&&!r.value){const h=[];for(let p=0;p<r.sep.length;++p){const g=r.sep[p];switch(g.type){case"newline":h.push(p);break;case"space":break;case"comment":g.indent>n.indent&&(h.length=0);break;default:h.length=0}}h.length>=2&&(f=r.sep.splice(h[1]))}switch(this.type){case"anchor":case"tag":u||r.value?(f.push(this.sourceToken),n.items.push({start:f}),this.onKeyLine=!0):r.sep?r.sep.push(this.sourceToken):r.start.push(this.sourceToken);return;case"explicit-key-ind":!r.sep&&!r.explicitKey?(r.start.push(this.sourceToken),r.explicitKey=!0):u||r.value?(f.push(this.sourceToken),n.items.push({start:f,explicitKey:!0})):this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:[this.sourceToken],explicitKey:!0}]}),this.onKeyLine=!0;return;case"map-value-ind":if(r.explicitKey)if(r.sep)if(r.value)n.items.push({start:[],key:null,sep:[this.sourceToken]});else if(bn(r.sep,"map-value-ind"))this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:f,key:null,sep:[this.sourceToken]}]});else if(Yh(r.key)&&!bn(r.sep,"newline")){const h=vr(r.start),p=r.key,g=r.sep;g.push(this.sourceToken),delete r.key,delete r.sep,this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:h,key:p,sep:g}]})}else f.length>0?r.sep=r.sep.concat(f,this.sourceToken):r.sep.push(this.sourceToken);else if(bn(r.start,"newline"))Object.assign(r,{key:null,sep:[this.sourceToken]});else{const h=vr(r.start);this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:h,key:null,sep:[this.sourceToken]}]})}else r.sep?r.value||u?n.items.push({start:f,key:null,sep:[this.sourceToken]}):bn(r.sep,"map-value-ind")?this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:[],key:null,sep:[this.sourceToken]}]}):r.sep.push(this.sourceToken):Object.assign(r,{key:null,sep:[this.sourceToken]});this.onKeyLine=!0;return;case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":{const h=this.flowScalar(this.type);u||r.value?(n.items.push({start:f,key:h,sep:[]}),this.onKeyLine=!0):r.sep?this.stack.push(h):(Object.assign(r,{key:h,sep:[]}),this.onKeyLine=!0);return}default:{const h=this.startBlockValue(n);if(h){o&&h.type!=="block-seq"&&n.items.push({start:f}),this.stack.push(h);return}}}}yield*this.pop(),yield*this.step()}*blockSequence(n){var l;const r=n.items[n.items.length-1];switch(this.type){case"newline":if(r.value){const o="end"in r.value?r.value.end:void 0,u=Array.isArray(o)?o[o.length-1]:void 0;(u==null?void 0:u.type)==="comment"?o==null||o.push(this.sourceToken):n.items.push({start:[this.sourceToken]})}else r.start.push(this.sourceToken);return;case"space":case"comment":if(r.value)n.items.push({start:[this.sourceToken]});else{if(this.atIndentedComment(r.start,n.indent)){const o=n.items[n.items.length-2],u=(l=o==null?void 0:o.value)==null?void 0:l.end;if(Array.isArray(u)){Array.prototype.push.apply(u,r.start),u.push(this.sourceToken),n.items.pop();return}}r.start.push(this.sourceToken)}return;case"anchor":case"tag":if(r.value||this.indent<=n.indent)break;r.start.push(this.sourceToken);return;case"seq-item-ind":if(this.indent!==n.indent)break;r.value||bn(r.start,"seq-item-ind")?n.items.push({start:[this.sourceToken]}):r.start.push(this.sourceToken);return}if(this.indent>n.indent){const o=this.startBlockValue(n);if(o){this.stack.push(o);return}}yield*this.pop(),yield*this.step()}*flowCollection(n){const r=n.items[n.items.length-1];if(this.type==="flow-error-end"){let l;do yield*this.pop(),l=this.peek(1);while(l&&l.type==="flow-collection")}else if(n.end.length===0){switch(this.type){case"comma":case"explicit-key-ind":!r||r.sep?n.items.push({start:[this.sourceToken]}):r.start.push(this.sourceToken);return;case"map-value-ind":!r||r.value?n.items.push({start:[],key:null,sep:[this.sourceToken]}):r.sep?r.sep.push(this.sourceToken):Object.assign(r,{key:null,sep:[this.sourceToken]});return;case"space":case"comment":case"newline":case"anchor":case"tag":!r||r.value?n.items.push({start:[this.sourceToken]}):r.sep?r.sep.push(this.sourceToken):r.start.push(this.sourceToken);return;case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":{const o=this.flowScalar(this.type);!r||r.value?n.items.push({start:[],key:o,sep:[]}):r.sep?this.stack.push(o):Object.assign(r,{key:o,sep:[]});return}case"flow-map-end":case"flow-seq-end":n.end.push(this.sourceToken);return}const l=this.startBlockValue(n);l?this.stack.push(l):(yield*this.pop(),yield*this.step())}else{const l=this.peek(2);if(l.type==="block-map"&&(this.type==="map-value-ind"&&l.indent===n.indent||this.type==="newline"&&!l.items[l.items.length-1].sep))yield*this.pop(),yield*this.step();else if(this.type==="map-value-ind"&&l.type!=="flow-collection"){const o=Yi(l),u=vr(o);Pd(n);const f=n.end.splice(1,n.end.length);f.push(this.sourceToken);const h={type:"block-map",offset:n.offset,indent:n.indent,items:[{start:u,key:n,sep:f}]};this.onKeyLine=!0,this.stack[this.stack.length-1]=h}else yield*this.lineEnd(n)}}flowScalar(n){if(this.onNewLine){let r=this.source.indexOf(`
`)+1;for(;r!==0;)this.onNewLine(this.offset+r),r=this.source.indexOf(`
`,r)+1}return{type:n,offset:this.offset,indent:this.indent,source:this.source}}startBlockValue(n){switch(this.type){case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return this.flowScalar(this.type);case"block-scalar-header":return{type:"block-scalar",offset:this.offset,indent:this.indent,props:[this.sourceToken],source:""};case"flow-map-start":case"flow-seq-start":return{type:"flow-collection",offset:this.offset,indent:this.indent,start:this.sourceToken,items:[],end:[]};case"seq-item-ind":return{type:"block-seq",offset:this.offset,indent:this.indent,items:[{start:[this.sourceToken]}]};case"explicit-key-ind":{this.onKeyLine=!0;const r=Yi(n),l=vr(r);return l.push(this.sourceToken),{type:"block-map",offset:this.offset,indent:this.indent,items:[{start:l,explicitKey:!0}]}}case"map-value-ind":{this.onKeyLine=!0;const r=Yi(n),l=vr(r);return{type:"block-map",offset:this.offset,indent:this.indent,items:[{start:l,key:null,sep:[this.sourceToken]}]}}}return null}atIndentedComment(n,r){return this.type!=="comment"||this.indent<=r?!1:n.every(l=>l.type==="newline"||l.type==="space")}*documentEnd(n){this.type!=="doc-mode"&&(n.end?n.end.push(this.sourceToken):n.end=[this.sourceToken],this.type==="newline"&&(yield*this.pop()))}*lineEnd(n){switch(this.type){case"comma":case"doc-start":case"doc-end":case"flow-seq-end":case"flow-map-end":case"map-value-ind":yield*this.pop(),yield*this.step();break;case"newline":this.onKeyLine=!1;case"space":case"comment":default:n.end?n.end.push(this.sourceToken):n.end=[this.sourceToken],this.type==="newline"&&(yield*this.pop())}}}function Gh(i){const n=i.prettyErrors!==!1;return{lineCounter:i.lineCounter||n&&new Jh||null,prettyErrors:n}}function d0(i,n={}){const{lineCounter:r,prettyErrors:l}=Gh(n),o=new au(r==null?void 0:r.addNewLine),u=new ou(n),f=Array.from(u.compose(o.parse(i)));if(l&&r)for(const h of f)h.errors.forEach(ul(i,r)),h.warnings.forEach(ul(i,r));return f.length>0?f:Object.assign([],{empty:!0},u.streamInfo())}function Xh(i,n={}){const{lineCounter:r,prettyErrors:l}=Gh(n),o=new au(r==null?void 0:r.addNewLine),u=new ou(n);let f=null;for(const h of u.compose(o.parse(i),!0,i.length))if(!f)f=h;else if(f.options.logLevel!=="silent"){f.errors.push(new zn(h.range.slice(0,2),"MULTIPLE_DOCS","Source contains multiple documents; please use YAML.parseAllDocuments()"));break}return l&&r&&(f.errors.forEach(ul(i,r)),f.warnings.forEach(ul(i,r))),f}function h0(i,n,r){let l;typeof n=="function"?l=n:r===void 0&&n&&typeof n=="object"&&(r=n);const o=Xh(i,r);if(!o)return null;if(o.warnings.forEach(u=>vh(o.options.logLevel,u)),o.errors.length>0){if(o.options.logLevel!=="silent")throw o.errors[0];o.errors=[]}return o.toJS(Object.assign({reviver:l},r))}function p0(i,n,r){let l=null;if(typeof n=="function"||Array.isArray(n)?l=n:r===void 0&&n&&(r=n),typeof r=="string"&&(r=r.length),typeof r=="number"){const o=Math.round(r);r=o<1?void 0:o>8?{indent:8}:{indent:o}}if(i===void 0){const{keepUndefined:o}=r??n??{};if(!o)return}return Wn(i)&&!l?i.toString(r):new Dr(i,l,r).toString(r)}const m0=Object.freeze(Object.defineProperty({__proto__:null,Alias:ml,CST:u0,Composer:ou,Document:Dr,Lexer:Qh,LineCounter:Jh,Pair:Ze,Parser:au,Scalar:ie,Schema:xl,YAMLError:iu,YAMLMap:wt,YAMLParseError:zn,YAMLSeq:Nn,YAMLWarning:Dh,isAlias:Kn,isCollection:$e,isDocument:Wn,isMap:Pr,isNode:Ie,isPair:_e,isScalar:ve,isSeq:Mr,parse:h0,parseAllDocuments:d0,parseDocument:Xh,stringify:p0,visit:En,visitAsync:pl},Symbol.toStringTag,{value:"Module"}));function g0(i,n,r={}){var T;const l=new i.LineCounter,o={keepSourceTokens:!0,lineCounter:l,...r},u=i.parseDocument(n,o),f=[],h=E=>[l.linePos(E[0]),l.linePos(E[1])],p=E=>{f.push({message:E.message,range:[l.linePos(E.pos[0]),l.linePos(E.pos[1])]})},g=(E,O)=>{for(const v of O.items){if(v instanceof i.Scalar&&typeof v.value=="string"){const A=fl.parse(v,o,f);A&&(E.children=E.children||[],E.children.push(A));continue}if(v instanceof i.YAMLMap){x(E,v);continue}f.push({message:"Sequence items should be strings or maps",range:h(v.range||O.range)})}},x=(E,O)=>{for(const v of O.items){if(E.children=E.children||[],!(v.key instanceof i.Scalar&&typeof v.key.value=="string")){f.push({message:"Only string keys are supported",range:h(v.key.range||O.range)});continue}const I=v.key,A=v.value;if(I.value==="text"){if(!(A instanceof i.Scalar&&typeof A.value=="string")){f.push({message:"Text value should be a string",range:h(v.value.range||O.range)});continue}E.children.push({kind:"text",text:Ra(A.value)});continue}if(I.value==="/children"){if(!(A instanceof i.Scalar&&typeof A.value=="string")||A.value!=="contain"&&A.value!=="equal"&&A.value!=="deep-equal"){f.push({message:'Strict value should be "contain", "equal" or "deep-equal"',range:h(v.value.range||O.range)});continue}E.containerMode=A.value;continue}if(I.value.startsWith("/")){if(!(A instanceof i.Scalar&&typeof A.value=="string")){f.push({message:"Property value should be a string",range:h(v.value.range||O.range)});continue}E.props=E.props??{},E.props[I.value.slice(1)]=Ra(A.value);continue}const F=fl.parse(I,o,f);if(!F)continue;if(A instanceof i.Scalar){const j=typeof A.value;if(j!=="string"&&j!=="number"&&j!=="boolean"){f.push({message:"Node value should be a string or a sequence",range:h(v.value.range||O.range)});continue}E.children.push({...F,children:[{kind:"text",text:Ra(String(A.value))}]});continue}if(A instanceof i.YAMLSeq){E.children.push(F),g(F,A);continue}f.push({message:"Map values should be strings or sequences",range:h(v.value.range||O.range)})}},S={kind:"role",role:"fragment"};return u.errors.forEach(p),f.length?{errors:f,fragment:S}:(u.contents instanceof i.YAMLSeq||f.push({message:'Aria snapshot must be a YAML sequence, elements starting with " -"',range:u.contents?h(u.contents.range):[{line:0,col:0},{line:0,col:0}]}),f.length?{errors:f,fragment:S}:(g(S,u.contents),f.length?{errors:f,fragment:y0}:((T=S.children)==null?void 0:T.length)===1?{fragment:S.children[0],errors:f}:{fragment:S,errors:f}))}const y0={kind:"role",role:"fragment"};function Zh(i){return i.replace(/[\u200b\u00ad]/g,"").replace(/[\r\n\s\t]+/g," ").trim()}function Ra(i){return i.startsWith("/")&&i.endsWith("/")&&i.length>1?{pattern:i.slice(1,-1)}:Zh(i)}class fl{static parse(n,r,l){try{return new fl(n.value)._parse()}catch(o){if(o instanceof Md){const u=r.prettyErrors===!1?o.message:o.message+`:

`+n.value+`
`+" ".repeat(o.pos)+`^
`;return l.push({message:u,range:[r.lineCounter.linePos(n.range[0]),r.lineCounter.linePos(n.range[0]+o.pos)]}),null}throw o}}constructor(n){this._input=n,this._pos=0,this._length=n.length}_peek(){return this._input[this._pos]||""}_next(){return this._pos<this._length?this._input[this._pos++]:null}_eof(){return this._pos>=this._length}_isWhitespace(){return!this._eof()&&/\s/.test(this._peek())}_skipWhitespace(){for(;this._isWhitespace();)this._pos++}_readIdentifier(n){this._eof()&&this._throwError(`Unexpected end of input when expecting ${n}`);const r=this._pos;for(;!this._eof()&&/[a-zA-Z]/.test(this._peek());)this._pos++;return this._input.slice(r,this._pos)}_readString(){let n="",r=!1;for(;!this._eof();){const l=this._next();if(r)n+=l,r=!1;else if(l==="\\")r=!0;else{if(l==='"')return n;n+=l}}this._throwError("Unterminated string")}_throwError(n,r=0){throw new Md(n,r||this._pos)}_readRegex(){let n="",r=!1,l=!1;for(;!this._eof();){const o=this._next();if(r)n+=o,r=!1;else if(o==="\\")r=!0,n+=o;else{if(o==="/"&&!l)return{pattern:n};o==="["?(l=!0,n+=o):o==="]"&&l?(n+=o,l=!1):n+=o}}this._throwError("Unterminated regex")}_readStringOrRegex(){const n=this._peek();return n==='"'?(this._next(),Zh(this._readString())):n==="/"?(this._next(),this._readRegex()):null}_readAttributes(n){let r=this._pos;for(;this._skipWhitespace(),this._peek()==="[";){this._next(),this._skipWhitespace(),r=this._pos;const l=this._readIdentifier("attribute");this._skipWhitespace();let o="";if(this._peek()==="=")for(this._next(),this._skipWhitespace(),r=this._pos;this._peek()!=="]"&&!this._isWhitespace()&&!this._eof();)o+=this._next();this._skipWhitespace(),this._peek()!=="]"&&this._throwError("Expected ]"),this._next(),this._applyAttribute(n,l,o||"true",r)}}_parse(){this._skipWhitespace();const n=this._readIdentifier("role");this._skipWhitespace();const r=this._readStringOrRegex()||"",l={kind:"role",role:n,name:r};return this._readAttributes(l),this._skipWhitespace(),this._eof()||this._throwError("Unexpected input"),l}_applyAttribute(n,r,l,o){if(r==="checked"){this._assert(l==="true"||l==="false"||l==="mixed",'Value of "checked" attribute must be a boolean or "mixed"',o),n.checked=l==="true"?!0:l==="false"?!1:"mixed";return}if(r==="disabled"){this._assert(l==="true"||l==="false",'Value of "disabled" attribute must be a boolean',o),n.disabled=l==="true";return}if(r==="expanded"){this._assert(l==="true"||l==="false",'Value of "expanded" attribute must be a boolean',o),n.expanded=l==="true";return}if(r==="level"){this._assert(!isNaN(Number(l)),'Value of "level" attribute must be a number',o),n.level=Number(l);return}if(r==="pressed"){this._assert(l==="true"||l==="false"||l==="mixed",'Value of "pressed" attribute must be a boolean or "mixed"',o),n.pressed=l==="true"?!0:l==="false"?!1:"mixed";return}if(r==="selected"){this._assert(l==="true"||l==="false",'Value of "selected" attribute must be a boolean',o),n.selected=l==="true";return}this._assert(!1,`Unsupported attribute [${r}]`,o)}_assert(n,r,l){n||this._throwError(r||"Assertion error",l)}}class Md extends Error{constructor(n,r){super(n),this.pos=r}}const v0=({sources:i,paused:n,log:r,mode:l})=>{var W;const[o,u]=ye.useState(),[f,h]=ye.useState(),[p,g]=Da("recorderPropertiesTab","log"),[x,S]=ye.useState(),[T,E]=ye.useState(),O=o||f||((W=i[0])==null?void 0:W.id),v=ye.useMemo(()=>{if(O){const j=i.find(Y=>Y.id===O);if(j)return j}return Og()},[i,O]),[w,I]=ye.useState("");window.playwrightElementPicked=(j,Y)=>{const z=v.language;I(ih(z,j.selector)),S(j.ariaSnapshot),E([]),Y&&p!=="locator"&&p!=="aria"&&g("locator"),l==="inspecting"&&p==="aria"||window.dispatch({event:"setMode",params:{mode:l==="inspecting"?"standby":"recording"}}).catch(()=>{})},window.playwrightSetRunningFile=h;const A=ye.useRef(null);ye.useLayoutEffect(()=>{var j;(j=A.current)==null||j.scrollIntoView({block:"center",inline:"nearest"})},[A]),ye.useEffect(()=>{const j=Y=>{switch(Y.key){case"F8":Y.preventDefault(),n?window.dispatch({event:"resume"}):window.dispatch({event:"pause"});break;case"F10":Y.preventDefault(),n&&window.dispatch({event:"step"});break}};return document.addEventListener("keydown",j),()=>document.removeEventListener("keydown",j)},[n]);const F=ye.useCallback(j=>{(l==="none"||l==="inspecting")&&window.dispatch({event:"setMode",params:{mode:"standby"}}),I(j),window.dispatch({event:"highlightRequested",params:{selector:j}})},[l]),V=ye.useCallback(j=>{(l==="none"||l==="inspecting")&&window.dispatch({event:"setMode",params:{mode:"standby"}});const{fragment:Y,errors:z}=g0(m0,j,{prettyErrors:!1}),P=z.map(le=>({message:le.message,line:le.range[1].line,column:le.range[1].col,type:"subtle-error"}));E(P),S(j),z.length||window.dispatch({event:"highlightRequested",params:{ariaTemplate:Y}})},[l]);return H.jsxs("div",{className:"recorder",children:[H.jsxs(Rd,{children:[H.jsx(vt,{icon:"circle-large-filled",title:"Record",toggled:l==="recording"||l==="recording-inspecting"||l==="assertingText"||l==="assertingVisibility",onClick:()=>{window.dispatch({event:"setMode",params:{mode:l==="none"||l==="standby"||l==="inspecting"?"recording":"standby"}})},children:"Record"}),H.jsx(wd,{}),H.jsx(vt,{icon:"inspect",title:"Pick locator",toggled:l==="inspecting"||l==="recording-inspecting",onClick:()=>{const j={inspecting:"standby",none:"inspecting",standby:"inspecting",recording:"recording-inspecting","recording-inspecting":"recording",assertingText:"recording-inspecting",assertingVisibility:"recording-inspecting",assertingValue:"recording-inspecting",assertingSnapshot:"recording-inspecting"}[l];window.dispatch({event:"setMode",params:{mode:j}}).catch(()=>{})}}),H.jsx(vt,{icon:"eye",title:"Assert visibility",toggled:l==="assertingVisibility",disabled:l==="none"||l==="standby"||l==="inspecting",onClick:()=>{window.dispatch({event:"setMode",params:{mode:l==="assertingVisibility"?"recording":"assertingVisibility"}})}}),H.jsx(vt,{icon:"whole-word",title:"Assert text",toggled:l==="assertingText",disabled:l==="none"||l==="standby"||l==="inspecting",onClick:()=>{window.dispatch({event:"setMode",params:{mode:l==="assertingText"?"recording":"assertingText"}})}}),H.jsx(vt,{icon:"symbol-constant",title:"Assert value",toggled:l==="assertingValue",disabled:l==="none"||l==="standby"||l==="inspecting",onClick:()=>{window.dispatch({event:"setMode",params:{mode:l==="assertingValue"?"recording":"assertingValue"}})}}),H.jsx(vt,{icon:"gist",title:"Assert snapshot",toggled:l==="assertingSnapshot",disabled:l==="none"||l==="standby"||l==="inspecting",onClick:()=>{window.dispatch({event:"setMode",params:{mode:l==="assertingSnapshot"?"recording":"assertingSnapshot"}})}}),H.jsx(wd,{}),H.jsx(vt,{icon:"files",title:"Copy",disabled:!v||!v.text,onClick:()=>{ad(v.text)}}),H.jsx(vt,{icon:"debug-continue",title:"Resume (F8)",ariaLabel:"Resume",disabled:!n,onClick:()=>{window.dispatch({event:"resume"})}}),H.jsx(vt,{icon:"debug-pause",title:"Pause (F8)",ariaLabel:"Pause",disabled:n,onClick:()=>{window.dispatch({event:"pause"})}}),H.jsx(vt,{icon:"debug-step-over",title:"Step over (F10)",ariaLabel:"Step over",disabled:!n,onClick:()=>{window.dispatch({event:"step"})}}),H.jsx("div",{style:{flex:"auto"}}),H.jsx("div",{children:"Target:"}),H.jsx(_g,{fileId:O,sources:i,setFileId:j=>{u(j),window.dispatch({event:"fileChanged",params:{file:j}})}}),H.jsx(vt,{icon:"clear-all",title:"Clear",disabled:!v||!v.text,onClick:()=>{window.dispatch({event:"clear"})}}),H.jsx(vt,{icon:"color-mode",title:"Toggle color mode",toggled:!1,onClick:()=>ig()})]}),H.jsx(xg,{sidebarSize:200,main:H.jsx(_a,{text:v.text,language:v.language,highlight:v.highlight,revealLine:v.revealLine,readOnly:!0,lineNumbers:!0}),sidebar:H.jsx(Tg,{rightToolbar:p==="locator"||p==="aria"?[H.jsx(vt,{icon:"files",title:"Copy",onClick:()=>ad((p==="locator"?w:x)||"")},1)]:[],tabs:[{id:"locator",title:"Locator",render:()=>H.jsx(_a,{text:w,placeholder:"Type locator to inspect",language:v.language,focusOnChange:!0,onChange:F,wrapLines:!0})},{id:"log",title:"Log",render:()=>H.jsx(sy,{language:v.language,log:Array.from(r.values())})},{id:"aria",title:"Aria",render:()=>H.jsx(_a,{text:x||"",placeholder:"Type aria template to match",language:"yaml",onChange:V,highlight:T,wrapLines:!0})}],selectedTab:p,setSelectedTab:g})})]})},w0=({})=>{const[i,n]=ye.useState([]),[r,l]=ye.useState(!1),[o,u]=ye.useState(new Map),[f,h]=ye.useState("none");return ye.useLayoutEffect(()=>{window.playwrightSetMode=h,window.playwrightSetSources=(p,g)=>{n(p),window.playwrightSourcesEchoForTest=p,document.title=g?`Playwright Inspector - ${g}`:"Playwright Inspector"},window.playwrightSetPaused=l,window.playwrightUpdateLogs=p=>{u(g=>{const x=new Map(g);for(const S of p)S.reveal=!g.has(S.id),x.set(S.id,S);return x})}},[]),H.jsx(v0,{sources:i,paused:r,log:o,mode:f})};(async()=>(rg(),dg.createRoot(document.querySelector("#root")).render(H.jsx(w0,{}))))();export{Ym as g};
