{"info": {"name": "蓝兔支付系统API", "description": "蓝兔支付系统后端API接口集合", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}], "item": [{"name": "系统接口", "item": [{"name": "健康检查", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}, "response": []}, {"name": "API信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api", "host": ["{{baseUrl}}"], "path": ["api"]}}, "response": []}]}, {"name": "支付接口", "item": [{"name": "创建支付订单", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 0.01,\n  \"body\": \"测试商品\",\n  \"attach\": \"测试数据\",\n  \"time_expire\": \"30m\"\n}"}, "url": {"raw": "{{baseUrl}}/api/payment/create", "host": ["{{baseUrl}}"], "path": ["api", "payment", "create"]}}, "response": []}, {"name": "查询支付订单", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/payment/query/PAY16409952000001234", "host": ["{{baseUrl}}"], "path": ["api", "payment", "query", "PAY16409952000001234"]}}, "response": []}, {"name": "获取订单列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/payment/orders?page=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["api", "payment", "orders"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}, {"key": "pay_status", "value": "paid", "disabled": true}, {"key": "start_date", "value": "2024-01-01", "disabled": true}, {"key": "end_date", "value": "2024-01-31", "disabled": true}, {"key": "out_trade_no", "value": "", "disabled": true}]}}, "response": []}, {"name": "获取支付统计", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/payment/stats", "host": ["{{baseUrl}}"], "path": ["api", "payment", "stats"], "query": [{"key": "start_date", "value": "2024-01-01", "disabled": true}, {"key": "end_date", "value": "2024-01-31", "disabled": true}]}}, "response": []}, {"name": "测试接口", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/payment/test", "host": ["{{baseUrl}}"], "path": ["api", "payment", "test"]}}, "response": []}, {"name": "支付通知回调（模拟）", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"code\": \"0\",\n  \"out_trade_no\": \"PAY16409952000001234\",\n  \"order_no\": \"LT2024010100001\",\n  \"pay_no\": \"4200001234567890123456789\",\n  \"total_fee\": \"1\",\n  \"mch_id\": \"1684186147\",\n  \"timestamp\": \"1640995200\",\n  \"sign\": \"ABC123DEF456\",\n  \"pay_channel\": \"wxpay\",\n  \"trade_type\": \"NATIVE\",\n  \"success_time\": \"2024-01-01 00:15:00\",\n  \"attach\": \"测试数据\",\n  \"openid\": \"oUpF8uMuAJO_M2pxb1Q9zNjWeS6o\"\n}"}, "url": {"raw": "{{baseUrl}}/api/payment/notify", "host": ["{{baseUrl}}"], "path": ["api", "payment", "notify"]}}, "response": []}]}]}