const { sequelize } = require('../config/database');
const Order = require('./Order');
const PaymentNotify = require('./PaymentNotify');
const Merchant = require('./Merchant');

// 定义模型关联关系
const setupAssociations = () => {
  // 订单与支付通知的关联
  Order.hasMany(PaymentNotify, {
    foreignKey: 'out_trade_no',
    sourceKey: 'out_trade_no',
    as: 'notifications'
  });
  
  PaymentNotify.belongsTo(Order, {
    foreignKey: 'out_trade_no',
    targetKey: 'out_trade_no',
    as: 'order'
  });
  
  // 商户与订单的关联（通过mch_id字段）
  // 注意：这里需要在Order模型中添加mch_id字段
  // Merchant.hasMany(Order, {
  //   foreignKey: 'mch_id',
  //   sourceKey: 'mch_id',
  //   as: 'orders'
  // });
  
  // Order.belongsTo(Merchant, {
  //   foreignKey: 'mch_id',
  //   targetKey: 'mch_id',
  //   as: 'merchant'
  // });
};

// 初始化数据库
const initDatabase = async () => {
  try {
    // 设置关联关系
    setupAssociations();
    
    // 同步数据库表结构
    await sequelize.sync({ 
      force: false, // 设置为true会删除现有表重新创建，生产环境请设置为false
      alter: true   // 自动更新表结构
    });
    
    console.log('数据库表结构同步完成');
    
    // 创建默认商户（如果不存在）
    await createDefaultMerchant();
    
  } catch (error) {
    console.error('数据库初始化失败:', error);
    throw error;
  }
};

// 创建默认商户
const createDefaultMerchant = async () => {
  try {
    const defaultMchId = process.env.LTZF_MCH_ID || '1230000109';
    const defaultApiKey = process.env.LTZF_API_KEY || 'test_api_key';
    
    const existingMerchant = await Merchant.findOne({
      where: { mch_id: defaultMchId }
    });
    
    if (!existingMerchant) {
      await Merchant.create({
        mch_id: defaultMchId,
        mch_name: '默认商户',
        api_key: defaultApiKey,
        status: 'active',
        supported_channels: ['wxpay'],
        supported_types: ['NATIVE'],
        default_notify_url: process.env.NOTIFY_URL || 'http://localhost:3000/api/payment/notify',
        contact_name: '系统管理员',
        remark: '系统默认创建的商户'
      });
      
      console.log('默认商户创建成功');
    }
  } catch (error) {
    console.error('创建默认商户失败:', error);
  }
};

module.exports = {
  sequelize,
  Order,
  PaymentNotify,
  Merchant,
  initDatabase,
  setupAssociations
};
