const Router = require('koa-router');
const PaymentController = require('../controllers/PaymentController');

const router = new Router();
const paymentController = new PaymentController();

// 支付相关路由
router.prefix('/api/payment');

/**
 * @api {post} /api/payment/create 创建支付订单
 * @apiName CreatePayment
 * @apiGroup Payment
 * 
 * @apiParam {Number} amount 支付金额（元）
 * @apiParam {String} body 商品描述
 * @apiParam {String} [attach] 附加数据
 * @apiParam {String} [time_expire=30m] 过期时间
 * @apiParam {String} [notify_url] 通知地址
 * 
 * @apiSuccess {Number} code 状态码（0表示成功）
 * @apiSuccess {String} message 响应消息
 * @apiSuccess {Object} data 响应数据
 * @apiSuccess {String} data.out_trade_no 商户订单号
 * @apiSuccess {Number} data.amount 支付金额
 * @apiSuccess {String} data.code_url 支付链接
 * @apiSuccess {String} data.qrcode_url 二维码图片链接
 * @apiSuccess {String} data.expire_time 过期时间
 */
router.post('/create', paymentController.createPayment.bind(paymentController));

/**
 * @api {get} /api/payment/query/:out_trade_no 查询支付订单
 * @apiName QueryPayment
 * @apiGroup Payment
 * 
 * @apiParam {String} out_trade_no 商户订单号
 * 
 * @apiSuccess {Number} code 状态码（0表示成功）
 * @apiSuccess {String} message 响应消息
 * @apiSuccess {Object} data 订单信息
 */
router.get('/query/:out_trade_no', paymentController.queryPayment.bind(paymentController));

/**
 * @api {post} /api/payment/notify 支付通知回调
 * @apiName PaymentNotify
 * @apiGroup Payment
 * 
 * @apiDescription 蓝兔支付系统的支付通知回调接口
 */
router.post('/notify', paymentController.handleNotify.bind(paymentController));

/**
 * @api {get} /api/payment/orders 获取订单列表
 * @apiName GetOrderList
 * @apiGroup Payment
 * 
 * @apiParam {Number} [page=1] 页码
 * @apiParam {Number} [pageSize=10] 每页数量
 * @apiParam {String} [pay_status] 支付状态
 * @apiParam {String} [start_date] 开始日期
 * @apiParam {String} [end_date] 结束日期
 * @apiParam {String} [out_trade_no] 订单号
 * 
 * @apiSuccess {Number} code 状态码（0表示成功）
 * @apiSuccess {String} message 响应消息
 * @apiSuccess {Object} data 响应数据
 * @apiSuccess {Array} data.list 订单列表
 * @apiSuccess {Object} data.pagination 分页信息
 */
router.get('/orders', paymentController.getOrderList.bind(paymentController));

/**
 * @api {get} /api/payment/stats 获取支付统计
 * @apiName GetPaymentStats
 * @apiGroup Payment
 * 
 * @apiParam {String} [start_date] 开始日期
 * @apiParam {String} [end_date] 结束日期
 * 
 * @apiSuccess {Number} code 状态码（0表示成功）
 * @apiSuccess {String} message 响应消息
 * @apiSuccess {Object} data 统计数据
 */
router.get('/stats', paymentController.getPaymentStats.bind(paymentController));

/**
 * @api {get} /api/payment/test 测试接口
 * @apiName TestPayment
 * @apiGroup Payment
 * 
 * @apiSuccess {Number} code 状态码（0表示成功）
 * @apiSuccess {String} message 响应消息
 * @apiSuccess {Object} data 测试数据
 */
router.get('/test', paymentController.test.bind(paymentController));

module.exports = router;
