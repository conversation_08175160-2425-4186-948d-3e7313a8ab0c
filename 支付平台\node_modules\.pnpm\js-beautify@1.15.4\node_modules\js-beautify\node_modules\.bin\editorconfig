#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/editorconfig@1.0.4/node_modules/editorconfig/bin/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/editorconfig@1.0.4/node_modules/editorconfig/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/editorconfig@1.0.4/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/editorconfig@1.0.4/node_modules/editorconfig/bin/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/editorconfig@1.0.4/node_modules/editorconfig/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/editorconfig@1.0.4/node_modules:/mnt/c/Users/<USER>/Desktop/支付平台/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../editorconfig@1.0.4/node_modules/editorconfig/bin/editorconfig" "$@"
else
  exec node  "$basedir/../../../../../editorconfig@1.0.4/node_modules/editorconfig/bin/editorconfig" "$@"
fi
